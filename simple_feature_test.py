#!/usr/bin/env python3
"""
Simple test script for feature engineering without external dependencies.
"""

import pandas as pd
import numpy as np
from pathlib import Path
import sys

# Add src to path
sys.path.append(str(Path(__file__).parent))

# Import technical indicators directly
from src.features.technical_indicators import TechnicalIndicators
from src.features.statistical_features import StatisticalFeatures


def test_technical_indicators():
    """Test technical indicators with sample data."""
    print("Testing Technical Indicators...")
    
    # Create sample data
    np.random.seed(42)
    dates = pd.date_range('2020-01-01', periods=100, freq='D')
    
    # Generate realistic price data
    price = 100
    prices = [price]
    
    for _ in range(99):
        change = np.random.normal(0, 0.02)  # 2% daily volatility
        price = price * (1 + change)
        prices.append(price)
    
    df = pd.DataFrame({
        'timestamp': dates,
        'open': prices,
        'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
        'close': prices,
        'volume': np.random.randint(1000, 10000, 100)
    })
    
    print(f"Sample data shape: {df.shape}")
    print(f"Price range: {df['close'].min():.2f} to {df['close'].max():.2f}")
    
    # Test SMA
    print("\nTesting SMA...")
    sma_20 = TechnicalIndicators.sma(df['close'], 20)
    print(f"SMA 20 (last 5): {sma_20.tail().values}")
    
    # Test EMA
    print("\nTesting EMA...")
    ema_12 = TechnicalIndicators.ema(df['close'], 12)
    print(f"EMA 12 (last 5): {ema_12.tail().values}")
    
    # Test RSI
    print("\nTesting RSI...")
    rsi = TechnicalIndicators.rsi(df['close'])
    print(f"RSI (last 5): {rsi.tail().values}")
    
    # Test MACD
    print("\nTesting MACD...")
    macd_data = TechnicalIndicators.macd(df['close'])
    print(f"MACD (last 5): {macd_data['macd'].tail().values}")
    print(f"Signal (last 5): {macd_data['signal'].tail().values}")
    
    # Test Bollinger Bands
    print("\nTesting Bollinger Bands...")
    bb_data = TechnicalIndicators.bollinger_bands(df['close'])
    print(f"BB Upper (last 5): {bb_data['upper'].tail().values}")
    print(f"BB Lower (last 5): {bb_data['lower'].tail().values}")
    print(f"BB %B (last 5): {bb_data['percent_b'].tail().values}")
    
    # Test ATR
    print("\nTesting ATR...")
    atr = TechnicalIndicators.atr(df['high'], df['low'], df['close'])
    print(f"ATR (last 5): {atr.tail().values}")
    
    # Test Stochastic
    print("\nTesting Stochastic...")
    stoch_data = TechnicalIndicators.stochastic(df['high'], df['low'], df['close'])
    print(f"Stoch %K (last 5): {stoch_data['k'].tail().values}")
    print(f"Stoch %D (last 5): {stoch_data['d'].tail().values}")
    
    # Test Williams %R
    print("\nTesting Williams %R...")
    williams_r = TechnicalIndicators.williams_r(df['high'], df['low'], df['close'])
    print(f"Williams %R (last 5): {williams_r.tail().values}")
    
    # Test OBV
    print("\nTesting OBV...")
    obv = TechnicalIndicators.obv(df['close'], df['volume'])
    print(f"OBV (last 5): {obv.tail().values}")
    
    # Test VWAP
    print("\nTesting VWAP...")
    vwap = TechnicalIndicators.vwap(df['high'], df['low'], df['close'], df['volume'])
    print(f"VWAP (last 5): {vwap.tail().values}")
    
    return df


def test_statistical_features(df):
    """Test statistical features."""
    print("\n" + "="*50)
    print("Testing Statistical Features...")
    print("="*50)
    
    # Test Returns
    print("\nTesting Returns...")
    returns = StatisticalFeatures.returns(df['close'])
    print(f"Returns (last 5): {returns.tail().values}")
    
    log_returns = StatisticalFeatures.log_returns(df['close'])
    print(f"Log Returns (last 5): {log_returns.tail().values}")
    
    # Test Volatility
    print("\nTesting Volatility...")
    volatility = StatisticalFeatures.volatility(df['close'], 20)
    print(f"Volatility (last 5): {volatility.tail().values}")
    
    # Test Rolling Statistics
    print("\nTesting Rolling Statistics...")
    rolling_mean = StatisticalFeatures.rolling_mean(df['close'], 20)
    print(f"Rolling Mean (last 5): {rolling_mean.tail().values}")
    
    rolling_std = StatisticalFeatures.rolling_std(df['close'], 20)
    print(f"Rolling Std (last 5): {rolling_std.tail().values}")
    
    # Test Z-Score
    print("\nTesting Z-Score...")
    z_score = StatisticalFeatures.z_score(df['close'], 20)
    print(f"Z-Score (last 5): {z_score.tail().values}")
    
    # Test Momentum
    print("\nTesting Momentum...")
    momentum = StatisticalFeatures.momentum(df['close'], 10)
    print(f"Momentum (last 5): {momentum.tail().values}")
    
    # Test Percentile Rank
    print("\nTesting Percentile Rank...")
    percentile_rank = StatisticalFeatures.percentile_rank(df['close'], 20)
    print(f"Percentile Rank (last 5): {percentile_rank.tail().values}")


def create_comprehensive_features(df):
    """Create comprehensive features manually."""
    print("\n" + "="*50)
    print("Creating Comprehensive Feature Set...")
    print("="*50)
    
    df_features = df.copy()
    
    # Technical Indicators
    print("Adding technical indicators...")
    
    # Moving Averages
    for period in [10, 20, 50]:
        df_features[f'sma_{period}'] = TechnicalIndicators.sma(df['close'], period)
        df_features[f'ema_{period}'] = TechnicalIndicators.ema(df['close'], period)
    
    # RSI
    df_features['rsi'] = TechnicalIndicators.rsi(df['close'])
    
    # MACD
    macd_data = TechnicalIndicators.macd(df['close'])
    df_features['macd'] = macd_data['macd']
    df_features['macd_signal'] = macd_data['signal']
    df_features['macd_histogram'] = macd_data['histogram']
    
    # Bollinger Bands
    bb_data = TechnicalIndicators.bollinger_bands(df['close'])
    df_features['bb_upper'] = bb_data['upper']
    df_features['bb_lower'] = bb_data['lower']
    df_features['bb_percent_b'] = bb_data['percent_b']
    df_features['bb_width'] = bb_data['width']
    
    # ATR
    df_features['atr'] = TechnicalIndicators.atr(df['high'], df['low'], df['close'])
    
    # Stochastic
    stoch_data = TechnicalIndicators.stochastic(df['high'], df['low'], df['close'])
    df_features['stoch_k'] = stoch_data['k']
    df_features['stoch_d'] = stoch_data['d']
    
    # Volume indicators
    df_features['obv'] = TechnicalIndicators.obv(df['close'], df['volume'])
    df_features['vwap'] = TechnicalIndicators.vwap(df['high'], df['low'], df['close'], df['volume'])
    
    # Statistical Features
    print("Adding statistical features...")
    
    # Returns
    df_features['returns'] = StatisticalFeatures.returns(df['close'])
    df_features['log_returns'] = StatisticalFeatures.log_returns(df['close'])
    
    # Volatility
    df_features['volatility'] = StatisticalFeatures.volatility(df['close'], 20)
    
    # Rolling statistics
    for window in [10, 20]:
        df_features[f'rolling_mean_{window}'] = StatisticalFeatures.rolling_mean(df['close'], window)
        df_features[f'rolling_std_{window}'] = StatisticalFeatures.rolling_std(df['close'], window)
        df_features[f'z_score_{window}'] = StatisticalFeatures.z_score(df['close'], window)
        df_features[f'momentum_{window}'] = StatisticalFeatures.momentum(df['close'], window)
    
    # Lagged Features
    print("Adding lagged features...")
    for lag in [1, 2, 3, 5]:
        df_features[f'close_lag_{lag}'] = df['close'].shift(lag)
        df_features[f'volume_lag_{lag}'] = df['volume'].shift(lag)
    
    # Time Features
    print("Adding time features...")
    df_features['hour'] = df['timestamp'].dt.hour
    df_features['day_of_week'] = df['timestamp'].dt.dayofweek
    df_features['month'] = df['timestamp'].dt.month
    
    # Cyclical encoding
    df_features['hour_sin'] = np.sin(2 * np.pi * df_features['hour'] / 24)
    df_features['hour_cos'] = np.cos(2 * np.pi * df_features['hour'] / 24)
    df_features['day_sin'] = np.sin(2 * np.pi * df_features['day_of_week'] / 7)
    df_features['day_cos'] = np.cos(2 * np.pi * df_features['day_of_week'] / 7)
    
    print(f"Original features: {df.shape[1]}")
    print(f"Total features: {df_features.shape[1]}")
    print(f"Added features: {df_features.shape[1] - df.shape[1]}")
    
    # Check for missing values
    missing_counts = df_features.isnull().sum()
    features_with_missing = missing_counts[missing_counts > 0]
    print(f"Features with missing values: {len(features_with_missing)}")
    
    if len(features_with_missing) > 0:
        print("Features with missing values:")
        for feature, count in features_with_missing.items():
            print(f"  {feature}: {count} ({count/len(df_features)*100:.1f}%)")
    
    # Save sample
    output_dir = Path("data/processed")
    output_dir.mkdir(exist_ok=True)
    
    df_features.to_csv(output_dir / "sample_comprehensive_features.csv", index=False)
    print(f"\nSaved comprehensive features to: {output_dir / 'sample_comprehensive_features.csv'}")
    
    return df_features


def main():
    """Main test function."""
    print("Simple Feature Engineering Test")
    print("="*50)
    
    # Test technical indicators
    df = test_technical_indicators()
    
    # Test statistical features
    test_statistical_features(df)
    
    # Create comprehensive features
    df_features = create_comprehensive_features(df)
    
    print("\n" + "="*50)
    print("Feature Engineering Test Completed Successfully!")
    print("="*50)
    print(f"Final dataset shape: {df_features.shape}")
    print(f"Feature columns: {list(df_features.columns)}")


if __name__ == "__main__":
    main()
