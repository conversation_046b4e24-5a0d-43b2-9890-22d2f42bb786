#!/usr/bin/env python3
"""
Test script for MT5 integration and API functionality.
"""

import pandas as pd
import numpy as np
from pathlib import Path
import sys
import json
import time
import requests
from datetime import datetime

# Add src to path
sys.path.append(str(Path(__file__).parent / "src" / "trading"))

# Import MT5 connector directly
from mt5_connector import MT5Connector


def test_mt5_connector():
    """Test MT5 connector functionality."""
    print("\n" + "="*50)
    print("Testing MT5 Connector")
    print("="*50)
    
    # Initialize MT5 connector
    config = {
        'server': 'MetaQuotes-Demo',
        'login': 0,  # Demo login
        'password': '',  # Demo password
        'symbols': ['EURUSD', 'GBPUSD', 'USDJPY'],
        'timeframes': {
            'M1': 1, 'M5': 5, 'M15': 15, 'M30': 30,
            'H1': 60, 'H4': 240, 'D1': 1440
        }
    }
    
    connector = MT5Connector(config)
    
    print(f"MT5 library available: {connector.mt5_available}")
    
    # Test connection
    print("\nTesting connection...")
    connected = connector.connect()
    print(f"Connection successful: {connected}")
    
    if connected:
        # Test account info
        print("\nTesting account info...")
        account_info = connector.get_account_info()
        print(f"Account info: {account_info}")
        
        # Test symbol info
        print("\nTesting symbol info...")
        symbol_info = connector.get_symbol_info('EURUSD')
        print(f"EURUSD info: {symbol_info}")
        
        # Test current prices
        print("\nTesting current prices...")
        prices = connector.get_current_prices(['EURUSD', 'GBPUSD'])
        print(f"Current prices: {prices}")
        
        # Test historical data
        print("\nTesting historical data...")
        historical_data = connector.get_historical_data('EURUSD', 'M15', 100)
        print(f"Historical data shape: {historical_data.shape}")
        if not historical_data.empty:
            print(f"Columns: {list(historical_data.columns)}")
            print(f"Date range: {historical_data['time'].min()} to {historical_data['time'].max()}")
            print(f"Price range: {historical_data['close'].min():.5f} to {historical_data['close'].max():.5f}")
        
        # Test market status
        print("\nTesting market status...")
        market_open = connector.is_market_open('EURUSD')
        print(f"Market open for EURUSD: {market_open}")
        
        # Test positions
        print("\nTesting positions...")
        positions = connector.get_positions()
        print(f"Current positions: {len(positions)}")
        
        # Test orders
        print("\nTesting orders...")
        orders = connector.get_orders()
        print(f"Pending orders: {len(orders)}")
        
        # Test order placement (simulation mode)
        print("\nTesting order placement...")
        order_result = connector.place_order(
            symbol='EURUSD',
            order_type='buy',
            volume=0.01,
            comment='Test order'
        )
        print(f"Order result: {order_result}")
        
        # Disconnect
        connector.disconnect()
    
    return connector


def test_api_endpoints():
    """Test API endpoints (requires running FastAPI server)."""
    print("\n" + "="*50)
    print("Testing API Endpoints")
    print("="*50)
    
    base_url = "http://localhost:8000"
    
    # Test if server is running
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code != 200:
            print("API server not running. Skipping API tests.")
            return
    except requests.exceptions.RequestException:
        print("API server not accessible. Skipping API tests.")
        return
    
    print("API server is running. Testing endpoints...")
    
    # Test root endpoint
    print("\nTesting root endpoint...")
    try:
        response = requests.get(f"{base_url}/")
        print(f"Root endpoint: {response.status_code} - {response.json()}")
    except Exception as e:
        print(f"Error testing root endpoint: {e}")
    
    # Test health check
    print("\nTesting health check...")
    try:
        response = requests.get(f"{base_url}/health")
        print(f"Health check: {response.status_code} - {response.json()}")
    except Exception as e:
        print(f"Error testing health check: {e}")
    
    # Test MT5 connection
    print("\nTesting MT5 connection...")
    try:
        response = requests.post(f"{base_url}/mt5/connect")
        print(f"MT5 connect: {response.status_code} - {response.json()}")
    except Exception as e:
        print(f"Error testing MT5 connection: {e}")
    
    # Test account info
    print("\nTesting account info...")
    try:
        response = requests.get(f"{base_url}/mt5/account")
        print(f"Account info: {response.status_code}")
        if response.status_code == 200:
            account_data = response.json()
            print(f"Account data keys: {list(account_data.keys())}")
    except Exception as e:
        print(f"Error testing account info: {e}")
    
    # Test current prices
    print("\nTesting current prices...")
    try:
        response = requests.get(f"{base_url}/mt5/prices/EURUSD")
        print(f"EURUSD price: {response.status_code}")
        if response.status_code == 200:
            price_data = response.json()
            print(f"Price data: {price_data}")
    except Exception as e:
        print(f"Error testing current prices: {e}")
    
    # Test historical data
    print("\nTesting historical data...")
    try:
        response = requests.get(f"{base_url}/mt5/history/EURUSD?timeframe=M15&count=50")
        print(f"Historical data: {response.status_code}")
        if response.status_code == 200:
            hist_data = response.json()
            print(f"Historical data count: {hist_data.get('count', 0)}")
    except Exception as e:
        print(f"Error testing historical data: {e}")
    
    # Test positions
    print("\nTesting positions...")
    try:
        response = requests.get(f"{base_url}/trading/positions")
        print(f"Positions: {response.status_code}")
        if response.status_code == 200:
            positions_data = response.json()
            print(f"Positions count: {len(positions_data.get('positions', []))}")
    except Exception as e:
        print(f"Error testing positions: {e}")
    
    # Test model summary
    print("\nTesting model summary...")
    try:
        response = requests.get(f"{base_url}/models/summary")
        print(f"Model summary: {response.status_code}")
        if response.status_code == 200:
            model_data = response.json()
            print(f"Model summary keys: {list(model_data.keys())}")
    except Exception as e:
        print(f"Error testing model summary: {e}")
    
    # Test prediction
    print("\nTesting prediction...")
    try:
        prediction_request = {
            "symbol": "EURUSD",
            "model_name": "ensemble",
            "horizon": 1
        }
        response = requests.post(f"{base_url}/models/predict", json=prediction_request)
        print(f"Prediction: {response.status_code}")
        if response.status_code == 200:
            prediction_data = response.json()
            print(f"Prediction: {prediction_data}")
    except Exception as e:
        print(f"Error testing prediction: {e}")
    
    # Test backtest
    print("\nTesting backtest...")
    try:
        backtest_request = {
            "symbol": "EURUSD",
            "strategy": "sma_crossover",
            "initial_capital": 10000.0
        }
        response = requests.post(f"{base_url}/backtest/run", json=backtest_request)
        print(f"Backtest: {response.status_code}")
        if response.status_code == 200:
            backtest_data = response.json()
            print(f"Backtest results: {backtest_data}")
    except Exception as e:
        print(f"Error testing backtest: {e}")
    
    # Test status
    print("\nTesting status...")
    try:
        response = requests.get(f"{base_url}/status")
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            status_data = response.json()
            print(f"System status: {status_data}")
    except Exception as e:
        print(f"Error testing status: {e}")


def test_order_placement():
    """Test order placement functionality."""
    print("\n" + "="*50)
    print("Testing Order Placement")
    print("="*50)
    
    connector = MT5Connector()
    connected = connector.connect()
    
    if not connected:
        print("Could not connect to MT5. Skipping order tests.")
        return
    
    print("Testing order placement (simulation mode)...")
    
    # Test buy order
    print("\nTesting buy order...")
    buy_order = connector.place_order(
        symbol='EURUSD',
        order_type='buy',
        volume=0.01,
        comment='Test buy order'
    )
    print(f"Buy order result: {buy_order}")
    
    # Test sell order
    print("\nTesting sell order...")
    sell_order = connector.place_order(
        symbol='EURUSD',
        order_type='sell',
        volume=0.01,
        comment='Test sell order'
    )
    print(f"Sell order result: {sell_order}")
    
    # Test order with stop loss and take profit
    print("\nTesting order with SL/TP...")
    sl_tp_order = connector.place_order(
        symbol='EURUSD',
        order_type='buy',
        volume=0.01,
        sl=1.0900,  # Stop loss
        tp=1.1100,  # Take profit
        comment='Test order with SL/TP'
    )
    print(f"SL/TP order result: {sl_tp_order}")
    
    connector.disconnect()


def create_api_documentation():
    """Create simple API documentation."""
    print("\n" + "="*50)
    print("Creating API Documentation")
    print("="*50)
    
    api_docs = {
        "title": "AI Trading Bot API Documentation",
        "version": "1.0.0",
        "base_url": "http://localhost:8000",
        "endpoints": {
            "health": {
                "method": "GET",
                "path": "/health",
                "description": "Check API health status"
            },
            "mt5_connect": {
                "method": "POST",
                "path": "/mt5/connect",
                "description": "Connect to MetaTrader 5"
            },
            "mt5_account": {
                "method": "GET",
                "path": "/mt5/account",
                "description": "Get MT5 account information"
            },
            "current_prices": {
                "method": "GET",
                "path": "/mt5/prices/{symbol}",
                "description": "Get current price for a symbol"
            },
            "historical_data": {
                "method": "GET",
                "path": "/mt5/history/{symbol}",
                "description": "Get historical data",
                "parameters": {
                    "timeframe": "M1, M5, M15, M30, H1, H4, D1",
                    "count": "Number of bars to retrieve"
                }
            },
            "place_order": {
                "method": "POST",
                "path": "/trading/order",
                "description": "Place a trading order",
                "body": {
                    "symbol": "Trading symbol",
                    "order_type": "buy or sell",
                    "volume": "Order volume",
                    "price": "Order price (optional)",
                    "stop_loss": "Stop loss price (optional)",
                    "take_profit": "Take profit price (optional)"
                }
            },
            "get_positions": {
                "method": "GET",
                "path": "/trading/positions",
                "description": "Get current open positions"
            },
            "close_position": {
                "method": "DELETE",
                "path": "/trading/position/{ticket}",
                "description": "Close a position by ticket"
            },
            "model_summary": {
                "method": "GET",
                "path": "/models/summary",
                "description": "Get summary of trained models"
            },
            "make_prediction": {
                "method": "POST",
                "path": "/models/predict",
                "description": "Make a prediction",
                "body": {
                    "symbol": "Trading symbol",
                    "model_name": "Model to use (optional)",
                    "horizon": "Prediction horizon"
                }
            },
            "run_backtest": {
                "method": "POST",
                "path": "/backtest/run",
                "description": "Run a backtest",
                "body": {
                    "symbol": "Trading symbol",
                    "strategy": "Strategy name",
                    "initial_capital": "Starting capital"
                }
            },
            "start_trading": {
                "method": "POST",
                "path": "/trading/start",
                "description": "Start automated trading"
            },
            "stop_trading": {
                "method": "POST",
                "path": "/trading/stop",
                "description": "Stop automated trading"
            },
            "system_status": {
                "method": "GET",
                "path": "/status",
                "description": "Get comprehensive system status"
            }
        }
    }
    
    # Save documentation
    output_dir = Path("data/processed")
    output_dir.mkdir(exist_ok=True)
    
    with open(output_dir / "api_documentation.json", 'w') as f:
        json.dump(api_docs, f, indent=2)
    
    print(f"API documentation saved to: {output_dir / 'api_documentation.json'}")
    
    # Create simple usage examples
    examples = {
        "curl_examples": {
            "health_check": "curl -X GET http://localhost:8000/health",
            "connect_mt5": "curl -X POST http://localhost:8000/mt5/connect",
            "get_price": "curl -X GET http://localhost:8000/mt5/prices/EURUSD",
            "place_order": 'curl -X POST http://localhost:8000/trading/order -H "Content-Type: application/json" -d \'{"symbol": "EURUSD", "order_type": "buy", "volume": 0.01}\'',
            "get_positions": "curl -X GET http://localhost:8000/trading/positions",
            "start_trading": "curl -X POST http://localhost:8000/trading/start"
        },
        "python_examples": {
            "health_check": "requests.get('http://localhost:8000/health')",
            "place_order": "requests.post('http://localhost:8000/trading/order', json={'symbol': 'EURUSD', 'order_type': 'buy', 'volume': 0.01})",
            "get_prediction": "requests.post('http://localhost:8000/models/predict', json={'symbol': 'EURUSD', 'horizon': 1})"
        }
    }
    
    with open(output_dir / "api_examples.json", 'w') as f:
        json.dump(examples, f, indent=2)
    
    print(f"API examples saved to: {output_dir / 'api_examples.json'}")


def main():
    """Main test function."""
    print("MT5 Integration and API Test")
    print("="*50)
    
    try:
        # Test MT5 connector
        connector = test_mt5_connector()
        
        # Test order placement
        test_order_placement()
        
        # Test API endpoints (if server is running)
        test_api_endpoints()
        
        # Create API documentation
        create_api_documentation()
        
        print("\n" + "="*50)
        print("MT5 Integration and API Test Completed!")
        print("="*50)
        
        # Save test summary
        output_dir = Path("data/processed")
        output_dir.mkdir(exist_ok=True)
        
        test_summary = {
            "mt5_connector_tested": True,
            "mt5_library_available": connector.mt5_available if connector else False,
            "connection_successful": connector.connected if connector else False,
            "api_endpoints_tested": True,
            "documentation_created": True,
            "timestamp": datetime.now().isoformat()
        }
        
        with open(output_dir / "mt5_api_test_summary.json", 'w') as f:
            json.dump(test_summary, f, indent=2)
        
        print(f"\nTest summary saved to: {output_dir / 'mt5_api_test_summary.json'}")
        
        # Instructions for running the API
        print("\n" + "="*50)
        print("To run the FastAPI server:")
        print("  python src/api/main.py")
        print("  or")
        print("  uvicorn src.api.main:app --reload --host 0.0.0.0 --port 8000")
        print("\nAPI will be available at: http://localhost:8000")
        print("Interactive docs at: http://localhost:8000/docs")
        print("="*50)
        
    except Exception as e:
        print(f"Error during MT5/API testing: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
