#!/usr/bin/env python3
"""
Test script for backtesting functionality.
"""

import pandas as pd
import numpy as np
from pathlib import Path
import sys
import json

# Add src to path
sys.path.append(str(Path(__file__).parent / "src" / "backtesting"))

# Import modules directly
from backtest_engine import BacktestEngine, simple_moving_average_strategy, rsi_strategy
from performance_metrics import PerformanceMetrics


def create_sample_price_data():
    """Create sample price data for backtesting."""
    np.random.seed(42)
    
    # Create realistic price data
    n_samples = 500
    dates = pd.date_range('2020-01-01', periods=n_samples, freq='D')
    
    # Generate price series with trend and volatility
    price = 100
    prices = [price]
    
    for i in range(n_samples - 1):
        # Add slight upward trend
        trend = 0.0002  # 0.02% daily trend
        volatility = 0.02  # 2% daily volatility
        
        change = np.random.normal(trend, volatility)
        price = price * (1 + change)
        prices.append(price)
    
    # Create OHLCV data
    df = pd.DataFrame({
        'timestamp': dates,
        'open': prices,
        'high': [p * (1 + abs(np.random.normal(0, 0.005))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.005))) for p in prices],
        'close': prices,
        'volume': np.random.randint(10000, 100000, n_samples)
    })
    
    print(f"Created sample price data with {len(df)} days")
    print(f"Price range: ${df['close'].min():.2f} to ${df['close'].max():.2f}")
    print(f"Total return: {(df['close'].iloc[-1] / df['close'].iloc[0] - 1):.2%}")
    
    return df


def test_backtest_engine():
    """Test the backtesting engine."""
    print("\n" + "="*50)
    print("Testing Backtesting Engine")
    print("="*50)
    
    # Create sample data
    data = create_sample_price_data()
    
    # Initialize backtest engine
    engine = BacktestEngine(
        initial_capital=10000.0,
        commission=0.001,  # 0.1%
        slippage=0.0001    # 0.01%
    )
    
    print(f"\nBacktest Configuration:")
    print(f"  Initial Capital: ${engine.initial_capital:,.2f}")
    print(f"  Commission: {engine.commission:.3%}")
    print(f"  Slippage: {engine.slippage:.4%}")
    
    # Test simple moving average strategy
    print(f"\nTesting Simple Moving Average Strategy...")
    
    def sma_strategy_wrapper(data):
        return simple_moving_average_strategy(data, short_window=10, long_window=20)
    
    results = engine.run_backtest(data, sma_strategy_wrapper, symbol='TEST')
    
    print(f"\nBacktest Results:")
    print(f"  Final Portfolio Value: ${results['final_value']:,.2f}")
    print(f"  Total Return: {results['total_return']:.2%}")
    print(f"  Total Trades: {len(results['trades'])}")
    
    # Print key metrics
    metrics = results['metrics']
    if metrics:
        print(f"\nPerformance Metrics:")
        print(f"  Sharpe Ratio: {metrics.get('sharpe_ratio', 0):.3f}")
        print(f"  Maximum Drawdown: {metrics.get('max_drawdown', 0):.2%}")
        print(f"  Win Rate: {metrics.get('win_rate', 0):.2%}")
        print(f"  Profit Factor: {metrics.get('profit_factor', 0):.3f}")
    
    # Test RSI strategy
    print(f"\nTesting RSI Strategy...")
    engine.reset()
    
    def rsi_strategy_wrapper(data):
        return rsi_strategy(data, rsi_period=14, oversold=30, overbought=70)
    
    rsi_results = engine.run_backtest(data, rsi_strategy_wrapper, symbol='TEST')
    
    print(f"\nRSI Strategy Results:")
    print(f"  Final Portfolio Value: ${rsi_results['final_value']:,.2f}")
    print(f"  Total Return: {rsi_results['total_return']:.2%}")
    print(f"  Total Trades: {len(rsi_results['trades'])}")
    
    rsi_metrics = rsi_results['metrics']
    if rsi_metrics:
        print(f"  Sharpe Ratio: {rsi_metrics.get('sharpe_ratio', 0):.3f}")
        print(f"  Maximum Drawdown: {rsi_metrics.get('max_drawdown', 0):.2%}")
        print(f"  Win Rate: {rsi_metrics.get('win_rate', 0):.2%}")
    
    return results, rsi_results


def test_performance_metrics():
    """Test performance metrics calculations."""
    print("\n" + "="*50)
    print("Testing Performance Metrics")
    print("="*50)
    
    # Create sample equity curve
    np.random.seed(42)
    dates = pd.date_range('2020-01-01', periods=252, freq='D')  # 1 year
    
    # Generate returns with some volatility
    returns = np.random.normal(0.0008, 0.02, 252)  # ~20% annual return, 20% volatility
    
    # Create equity curve
    equity_values = [10000]  # Start with $10,000
    for ret in returns:
        equity_values.append(equity_values[-1] * (1 + ret))
    
    equity_curve = pd.Series(equity_values[1:], index=dates)
    
    print(f"Sample equity curve:")
    print(f"  Start Value: ${equity_curve.iloc[0]:,.2f}")
    print(f"  End Value: ${equity_curve.iloc[-1]:,.2f}")
    print(f"  Total Return: {(equity_curve.iloc[-1] / equity_curve.iloc[0] - 1):.2%}")
    
    # Test individual metrics
    print(f"\nTesting Individual Metrics:")
    
    returns_series = PerformanceMetrics.calculate_returns(equity_curve)
    
    # Basic metrics
    total_ret = PerformanceMetrics.total_return(equity_curve.iloc[0], equity_curve.iloc[-1])
    ann_ret = PerformanceMetrics.annualized_return(total_ret, 252)
    vol = PerformanceMetrics.volatility(returns_series)
    sharpe = PerformanceMetrics.sharpe_ratio(returns_series)
    
    print(f"  Total Return: {total_ret:.2%}")
    print(f"  Annualized Return: {ann_ret:.2%}")
    print(f"  Volatility: {vol:.2%}")
    print(f"  Sharpe Ratio: {sharpe:.3f}")
    
    # Drawdown metrics
    dd_metrics = PerformanceMetrics.maximum_drawdown(equity_curve)
    print(f"  Maximum Drawdown: {dd_metrics['max_drawdown']:.2%}")
    print(f"  Drawdown Duration: {dd_metrics['drawdown_duration_days']} days")
    
    # Risk metrics
    var_5 = PerformanceMetrics.value_at_risk(returns_series, 0.05)
    cvar_5 = PerformanceMetrics.conditional_value_at_risk(returns_series, 0.05)
    
    print(f"  VaR (5%): {var_5:.2%}")
    print(f"  CVaR (5%): {cvar_5:.2%}")
    
    # Test comprehensive metrics
    print(f"\nTesting Comprehensive Metrics:")
    
    comprehensive_metrics = PerformanceMetrics.calculate_comprehensive_metrics(equity_curve)
    
    print(f"Comprehensive metrics calculated successfully!")
    print(f"Metrics categories: {list(comprehensive_metrics.keys())}")
    
    # Generate performance report
    print(f"\nGenerating Performance Report:")
    report = PerformanceMetrics.generate_performance_report(comprehensive_metrics)
    print(report)
    
    return comprehensive_metrics


def test_trade_metrics():
    """Test trade-specific metrics."""
    print("\n" + "="*50)
    print("Testing Trade Metrics")
    print("="*50)
    
    # Create sample trades
    sample_trades = [
        {'symbol': 'TEST', 'action': 'buy', 'quantity': 100, 'price': 100.0, 'timestamp': '2020-01-01'},
        {'symbol': 'TEST', 'action': 'sell', 'quantity': 100, 'price': 105.0, 'timestamp': '2020-01-05'},
        {'symbol': 'TEST', 'action': 'buy', 'quantity': 200, 'price': 102.0, 'timestamp': '2020-01-10'},
        {'symbol': 'TEST', 'action': 'sell', 'quantity': 200, 'price': 98.0, 'timestamp': '2020-01-15'},
        {'symbol': 'TEST', 'action': 'buy', 'quantity': 150, 'price': 95.0, 'timestamp': '2020-01-20'},
        {'symbol': 'TEST', 'action': 'sell', 'quantity': 150, 'price': 110.0, 'timestamp': '2020-01-25'},
    ]
    
    print(f"Sample trades: {len(sample_trades)} trades")
    
    # Calculate trade metrics
    trade_metrics = PerformanceMetrics.calculate_trade_metrics(sample_trades)
    
    print(f"\nTrade Metrics:")
    print(f"  Total Trades: {trade_metrics['total_trades']}")
    print(f"  Profitable Trades: {trade_metrics['profitable_trades']}")
    print(f"  Losing Trades: {trade_metrics['losing_trades']}")
    print(f"  Win Rate: {trade_metrics['win_rate']:.2%}")
    print(f"  Profit Factor: {trade_metrics['profit_factor']:.3f}")
    print(f"  Average Win: ${trade_metrics['avg_win']:.2f}")
    print(f"  Average Loss: ${trade_metrics['avg_loss']:.2f}")
    print(f"  Total P&L: ${trade_metrics['total_pnl']:.2f}")
    
    return trade_metrics


def test_strategy_comparison():
    """Test comparison between different strategies."""
    print("\n" + "="*50)
    print("Testing Strategy Comparison")
    print("="*50)
    
    # Create sample data
    data = create_sample_price_data()
    
    strategies = {
        'SMA_10_20': lambda d: simple_moving_average_strategy(d, 10, 20),
        'SMA_5_15': lambda d: simple_moving_average_strategy(d, 5, 15),
        'RSI_30_70': lambda d: rsi_strategy(d, 14, 30, 70),
        'RSI_20_80': lambda d: rsi_strategy(d, 14, 20, 80),
    }
    
    results = {}
    
    for strategy_name, strategy_func in strategies.items():
        print(f"\nTesting {strategy_name}...")
        
        engine = BacktestEngine(initial_capital=10000.0)
        result = engine.run_backtest(data, strategy_func, symbol='TEST')
        
        results[strategy_name] = {
            'final_value': result['final_value'],
            'total_return': result['total_return'],
            'total_trades': len(result['trades']),
            'metrics': result['metrics']
        }
        
        print(f"  Final Value: ${result['final_value']:,.2f}")
        print(f"  Total Return: {result['total_return']:.2%}")
        print(f"  Trades: {len(result['trades'])}")
        print(f"  Sharpe: {result['metrics'].get('sharpe_ratio', 0):.3f}")
    
    # Compare strategies
    print(f"\nStrategy Comparison Summary:")
    print(f"{'Strategy':<12} {'Return':<8} {'Sharpe':<8} {'Trades':<8} {'Final Value':<12}")
    print("-" * 60)
    
    for name, result in results.items():
        print(f"{name:<12} {result['total_return']:>7.1%} {result['metrics'].get('sharpe_ratio', 0):>7.2f} {result['total_trades']:>7d} ${result['final_value']:>10,.0f}")
    
    # Find best strategy
    best_strategy = max(results.items(), key=lambda x: x[1]['total_return'])
    print(f"\nBest Strategy: {best_strategy[0]} with {best_strategy[1]['total_return']:.2%} return")
    
    return results


def main():
    """Main test function."""
    print("Backtesting System Test")
    print("="*50)
    
    try:
        # Test backtest engine
        sma_results, rsi_results = test_backtest_engine()
        
        # Test performance metrics
        perf_metrics = test_performance_metrics()
        
        # Test trade metrics
        trade_metrics = test_trade_metrics()
        
        # Test strategy comparison
        strategy_comparison = test_strategy_comparison()
        
        print("\n" + "="*50)
        print("Backtesting System Test Completed Successfully!")
        print("="*50)
        
        # Save test results
        output_dir = Path("data/processed")
        output_dir.mkdir(exist_ok=True)
        
        test_summary = {
            'sma_strategy': {
                'final_value': sma_results['final_value'],
                'total_return': sma_results['total_return'],
                'total_trades': len(sma_results['trades'])
            },
            'rsi_strategy': {
                'final_value': rsi_results['final_value'],
                'total_return': rsi_results['total_return'],
                'total_trades': len(rsi_results['trades'])
            },
            'performance_metrics_tested': True,
            'trade_metrics_tested': True,
            'strategy_comparison_count': len(strategy_comparison)
        }
        
        with open(output_dir / "backtesting_test_summary.json", 'w') as f:
            json.dump(test_summary, f, indent=2, default=str)
        
        print(f"\nTest summary saved to: {output_dir / 'backtesting_test_summary.json'}")
        
        # Save sample backtest results
        sma_results['equity_curve'] = [{'timestamp': str(point['timestamp']), 'value': point['value']} for point in sma_results['equity_curve']]
        sma_results['trades'] = [dict(trade) for trade in sma_results['trades']]
        
        with open(output_dir / "sample_backtest_results.json", 'w') as f:
            json.dump(sma_results, f, indent=2, default=str)
        
        print(f"Sample backtest results saved to: {output_dir / 'sample_backtest_results.json'}")
        
    except Exception as e:
        print(f"Error during backtesting test: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
