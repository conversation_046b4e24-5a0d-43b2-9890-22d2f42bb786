#!/usr/bin/env python3
"""
Test script for monitoring and experiment tracking functionality.
"""

import pandas as pd
import numpy as np
from pathlib import Path
import sys
import json
import time
from datetime import datetime, timedelta

# Add src to path
sys.path.append(str(Path(__file__).parent / "src" / "monitoring"))

# Import monitoring modules directly
from experiment_tracker import ExperimentTracker
from dashboard import TradingDashboard, SystemMonitor


def test_experiment_tracker():
    """Test the experiment tracking functionality."""
    print("\n" + "="*50)
    print("Testing Experiment Tracker")
    print("="*50)
    
    # Initialize experiment tracker
    tracker = ExperimentTracker("data/test_experiments.db")
    
    print("Experiment tracker initialized")
    
    # Create a new experiment
    print("\nCreating new experiment...")
    exp_id = tracker.create_experiment(
        name="LSTM Trading Model Test",
        description="Testing LSTM model with different hyperparameters",
        model_type="LSTM",
        dataset="EURUSD_M15_2020-2023"
    )
    
    print(f"Created experiment: {exp_id}")
    
    # Log model runs
    print("\nLogging model runs...")
    
    # First model run
    model_config_1 = {
        'model_type': 'LSTM',
        'units': [50, 50],
        'dropout': 0.2,
        'epochs': 100,
        'batch_size': 32,
        'sequence_length': 60
    }
    
    training_metrics_1 = {
        'loss': 0.0045,
        'mae': 0.0032,
        'val_loss': 0.0052,
        'val_mae': 0.0038,
        'epochs_trained': 85
    }
    
    test_metrics_1 = {
        'mse': 0.0048,
        'mae': 0.0035,
        'r2': 0.72
    }
    
    run_id_1 = tracker.log_model_run(
        exp_id, "LSTM_50_50_dropout_0.2", model_config_1,
        training_metrics_1, test_metrics=test_metrics_1
    )
    
    # Second model run
    model_config_2 = {
        'model_type': 'LSTM',
        'units': [64, 32],
        'dropout': 0.3,
        'epochs': 100,
        'batch_size': 16,
        'sequence_length': 60
    }
    
    training_metrics_2 = {
        'loss': 0.0041,
        'mae': 0.0029,
        'val_loss': 0.0049,
        'val_mae': 0.0034,
        'epochs_trained': 92
    }
    
    test_metrics_2 = {
        'mse': 0.0044,
        'mae': 0.0031,
        'r2': 0.76
    }
    
    run_id_2 = tracker.log_model_run(
        exp_id, "LSTM_64_32_dropout_0.3", model_config_2,
        training_metrics_2, test_metrics=test_metrics_2
    )
    
    print(f"Logged model runs: {run_id_1}, {run_id_2}")
    
    # Log backtest results
    print("\nLogging backtest results...")
    
    backtest_results_1 = {
        'initial_capital': 10000.0,
        'final_value': 12500.0,
        'total_return': 0.25,
        'sharpe_ratio': 1.45,
        'max_drawdown': -0.08,
        'total_trades': 156,
        'win_rate': 0.58,
        'profit_factor': 1.32
    }
    
    backtest_params_1 = {
        'strategy': 'LSTM_signals',
        'confidence_threshold': 0.6,
        'position_size': 0.02,
        'stop_loss': 0.02,
        'take_profit': 0.04
    }
    
    bt_id_1 = tracker.log_backtest_result(
        exp_id, "LSTM_Strategy_v1", "EURUSD", "M15",
        "2022-01-01", "2023-12-31", backtest_results_1, backtest_params_1
    )
    
    # Log live performance data
    print("\nLogging live performance data...")
    
    # Simulate 30 days of live trading data
    base_date = datetime.now() - timedelta(days=30)
    portfolio_value = 10000.0
    
    for i in range(30):
        date = base_date + timedelta(days=i)
        
        # Simulate daily performance
        daily_return = np.random.normal(0.001, 0.02)  # 0.1% mean, 2% volatility
        portfolio_value *= (1 + daily_return)
        
        cumulative_return = (portfolio_value - 10000.0) / 10000.0
        drawdown = min(0, cumulative_return - max(0, cumulative_return))
        
        tracker.log_live_performance(
            exp_id, date.strftime('%Y-%m-%d'),
            portfolio_value, daily_return, cumulative_return,
            drawdown, np.random.randint(0, 5), 0.55, daily_return * 10000
        )
    
    print("Logged 30 days of live performance data")
    
    # Test data retrieval
    print("\nTesting data retrieval...")
    
    # Get experiment details
    experiment = tracker.get_experiment(exp_id)
    print(f"Experiment: {experiment['name']}")
    
    # Get model runs
    model_runs = tracker.get_model_runs(exp_id)
    print(f"Model runs: {len(model_runs)}")
    
    # Get backtest results
    backtest_results = tracker.get_backtest_results(exp_id)
    print(f"Backtest results: {len(backtest_results)}")
    
    # Get live performance
    live_performance = tracker.get_live_performance(exp_id, 30)
    print(f"Live performance records: {len(live_performance)}")
    
    # Generate experiment report
    print("\nGenerating experiment report...")
    report = tracker.generate_experiment_report(exp_id)
    
    print(f"Report summary:")
    summary = report['summary']
    print(f"  Total model runs: {summary['total_model_runs']}")
    print(f"  Total backtests: {summary['total_backtests']}")
    print(f"  Live trading days: {summary['live_trading_days']}")
    print(f"  Avg backtest return: {summary['avg_backtest_return']:.2%}")
    print(f"  Avg Sharpe ratio: {summary['avg_sharpe_ratio']:.2f}")
    print(f"  Total live return: {summary['total_live_return']:.2%}")
    
    # Test database stats
    stats = tracker.get_database_stats()
    print(f"\nDatabase stats: {stats}")
    
    return tracker, exp_id


def test_trading_dashboard():
    """Test the trading dashboard functionality."""
    print("\n" + "="*50)
    print("Testing Trading Dashboard")
    print("="*50)
    
    # Initialize dashboard
    dashboard = TradingDashboard()
    
    print("Trading dashboard initialized")
    
    # Simulate trading metrics updates
    print("\nSimulating trading metrics updates...")
    
    base_time = datetime.now() - timedelta(hours=24)
    portfolio_value = 10000.0
    
    for i in range(24):  # 24 hours of data
        timestamp = base_time + timedelta(hours=i)
        
        # Simulate trading metrics
        daily_return = np.random.normal(0.0005, 0.015)  # Small positive drift
        portfolio_value *= (1 + daily_return)
        
        metrics = {
            'portfolio_value': portfolio_value,
            'account_balance': portfolio_value * 0.8,  # 80% invested
            'daily_pnl': daily_return * portfolio_value,
            'daily_return': daily_return,
            'current_drawdown': max(-0.05, np.random.normal(-0.01, 0.02)),
            'active_positions': np.random.randint(2, 8),
            'trades_today': np.random.randint(0, 5),
            'winning_trades': np.random.randint(0, 3),
            'win_rate': 0.55 + np.random.normal(0, 0.1),
            'monthly_return': 0.03 + np.random.normal(0, 0.01)
        }
        
        dashboard.update_trading_metrics(metrics)
    
    print(f"Updated dashboard with 24 hours of trading data")
    
    # Simulate system status updates
    print("\nSimulating system status updates...")
    
    system_status = {
        'trading_active': True,
        'mt5_connected': True,
        'cpu_usage': 45.2,
        'memory_usage': 62.8,
        'last_trade_time': datetime.now() - timedelta(minutes=15),
        'market_open': True
    }
    
    dashboard.update_system_status(system_status)
    
    # Test dashboard data retrieval
    print("\nTesting dashboard data retrieval...")
    
    dashboard_data = dashboard.get_dashboard_data()
    
    print(f"Dashboard data keys: {list(dashboard_data.keys())}")
    
    summary = dashboard_data['summary']
    print(f"\nSummary statistics:")
    print(f"  Total trades: {summary['total_trades']}")
    print(f"  Win rate: {summary['win_rate']:.1%}")
    print(f"  Total P&L: ${summary['total_pnl']:,.2f}")
    print(f"  Current drawdown: {summary['current_drawdown']:.2%}")
    print(f"  Portfolio value: ${summary['portfolio_value']:,.2f}")
    
    print(f"\nActive alerts: {len(dashboard_data['active_alerts'])}")
    print(f"Charts data points: {len(dashboard_data['charts']['equity_curve'])}")
    
    # Test performance report generation
    print("\nGenerating performance report...")
    
    perf_report = dashboard.generate_performance_report(7)  # Last 7 days
    
    if 'error' not in perf_report:
        print(f"Performance report (7 days):")
        perf = perf_report['performance']
        print(f"  Total return: {perf['total_return']:.2%}")
        print(f"  Volatility: {perf['volatility']:.2%}")
        print(f"  Sharpe ratio: {perf['sharpe_ratio']:.2f}")
        print(f"  Max drawdown: {perf['max_drawdown']:.2%}")
        
        trading = perf_report['trading']
        print(f"  Total trades: {trading['total_trades']}")
        print(f"  Win rate: {trading['win_rate']:.1%}")
    
    # Test alert system
    print("\nTesting alert system...")
    
    # Trigger some alerts by updating with extreme values
    extreme_metrics = {
        'portfolio_value': 8500.0,  # Significant loss
        'current_drawdown': -0.15,  # High drawdown
        'daily_pnl': -500.0,        # Large daily loss
        'win_rate': 0.30            # Low win rate
    }
    
    dashboard.update_trading_metrics(extreme_metrics)
    
    extreme_system = {
        'cpu_usage': 85.0,          # High CPU
        'memory_usage': 90.0,       # High memory
        'mt5_connected': False      # Connection lost
    }
    
    dashboard.update_system_status(extreme_system)
    
    # Check alerts
    alert_summary = dashboard.get_alert_summary()
    print(f"Alert summary: {alert_summary}")
    
    return dashboard


def test_system_monitor():
    """Test the system monitoring functionality."""
    print("\n" + "="*50)
    print("Testing System Monitor")
    print("="*50)
    
    # Test system metrics
    print("Getting system metrics...")
    
    system_metrics = SystemMonitor.get_system_metrics()
    
    print(f"System metrics:")
    for key, value in system_metrics.items():
        if isinstance(value, float):
            if 'usage' in key:
                print(f"  {key}: {value:.1f}%")
            elif 'bytes' in key or 'free' in key or 'available' in key:
                print(f"  {key}: {value:,.0f} bytes")
            else:
                print(f"  {key}: {value:.2f}")
        else:
            print(f"  {key}: {value}")
    
    # Test dependency check
    print("\nChecking dependencies...")
    
    dependencies = SystemMonitor.check_dependencies()
    
    print(f"Dependencies status:")
    for dep, available in dependencies.items():
        status = "✓" if available else "✗"
        print(f"  {dep}: {status}")
    
    return system_metrics, dependencies


def test_integration():
    """Test integration between experiment tracker and dashboard."""
    print("\n" + "="*50)
    print("Testing Integration")
    print("="*50)
    
    # This would test how the dashboard can pull data from experiment tracker
    # and how they work together in a real trading environment
    
    print("Integration test - combining experiment tracking with dashboard monitoring")
    
    # Simulate a complete workflow
    tracker = ExperimentTracker("data/integration_test.db")
    dashboard = TradingDashboard()
    
    # Create experiment
    exp_id = tracker.create_experiment(
        "Live Trading Test",
        "Testing live trading with monitoring",
        "Ensemble",
        "EURUSD_Live"
    )
    
    # Simulate live trading session
    print("Simulating live trading session...")
    
    for hour in range(6):  # 6 hours of trading
        # Update dashboard with trading metrics
        metrics = {
            'portfolio_value': 10000 + hour * 50 + np.random.normal(0, 20),
            'daily_pnl': np.random.normal(10, 30),
            'trades_today': hour + np.random.randint(0, 2),
            'win_rate': 0.55 + np.random.normal(0, 0.05)
        }
        
        dashboard.update_trading_metrics(metrics)
        
        # Log performance to experiment tracker
        if hour > 0:  # Skip first hour
            tracker.log_live_performance(
                exp_id, 
                (datetime.now() - timedelta(hours=6-hour)).strftime('%Y-%m-%d'),
                metrics['portfolio_value'],
                metrics['daily_pnl'] / 10000,  # Convert to return
                (metrics['portfolio_value'] - 10000) / 10000,  # Cumulative return
                0,  # Drawdown
                metrics['trades_today'],
                metrics['win_rate'],
                metrics['daily_pnl']
            )
    
    print("Live trading session simulation completed")
    
    # Generate combined report
    exp_report = tracker.generate_experiment_report(exp_id)
    dashboard_data = dashboard.get_dashboard_data()
    
    print(f"\nCombined results:")
    print(f"  Experiment live days: {exp_report['summary']['live_trading_days']}")
    print(f"  Dashboard data points: {len(dashboard_data['charts']['equity_curve'])}")
    print(f"  Active alerts: {len(dashboard_data['active_alerts'])}")
    
    return exp_report, dashboard_data


def main():
    """Main test function."""
    print("Monitoring and Experiment Tracking Test")
    print("="*50)
    
    try:
        # Test experiment tracker
        tracker, exp_id = test_experiment_tracker()
        
        # Test trading dashboard
        dashboard = test_trading_dashboard()
        
        # Test system monitor
        system_metrics, dependencies = test_system_monitor()
        
        # Test integration
        exp_report, dashboard_data = test_integration()
        
        print("\n" + "="*50)
        print("Monitoring and Experiment Tracking Test Completed Successfully!")
        print("="*50)
        
        # Save test summary
        output_dir = Path("data/processed")
        output_dir.mkdir(exist_ok=True)
        
        test_summary = {
            'experiment_tracker_tested': True,
            'dashboard_tested': True,
            'system_monitor_tested': True,
            'integration_tested': True,
            'experiment_id': exp_id,
            'dashboard_alerts': len(dashboard_data['active_alerts']),
            'system_dependencies': dependencies,
            'timestamp': datetime.now().isoformat()
        }
        
        with open(output_dir / "monitoring_test_summary.json", 'w') as f:
            json.dump(test_summary, f, indent=2, default=str)
        
        print(f"\nTest summary saved to: {output_dir / 'monitoring_test_summary.json'}")
        
        # Export sample data
        tracker.export_experiment_data(exp_id, str(output_dir / "sample_experiment_export.json"))
        dashboard.export_dashboard_data(str(output_dir / "sample_dashboard_export.json"))
        
        print(f"Sample data exported to data/processed/")
        
    except Exception as e:
        print(f"Error during monitoring testing: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
