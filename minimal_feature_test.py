#!/usr/bin/env python3
"""
Minimal test script for core feature engineering functionality.
"""

import pandas as pd
import numpy as np
from pathlib import Path


def sma(data, window):
    """Simple Moving Average."""
    return data.rolling(window=window).mean()


def ema(data, window):
    """Exponential Moving Average."""
    return data.ewm(span=window).mean()


def rsi(data, window=14):
    """Relative Strength Index."""
    delta = data.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
    
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    
    return rsi


def macd(data, fast=12, slow=26, signal=9):
    """MACD."""
    ema_fast = ema(data, fast)
    ema_slow = ema(data, slow)
    
    macd_line = ema_fast - ema_slow
    signal_line = ema(macd_line, signal)
    histogram = macd_line - signal_line
    
    return {
        'macd': macd_line,
        'signal': signal_line,
        'histogram': histogram
    }


def bollinger_bands(data, window=20, std_dev=2):
    """Bollinger Bands."""
    sma_val = sma(data, window)
    std = data.rolling(window=window).std()
    
    upper_band = sma_val + (std * std_dev)
    lower_band = sma_val - (std * std_dev)
    
    return {
        'upper': upper_band,
        'middle': sma_val,
        'lower': lower_band,
        'width': upper_band - lower_band,
        'percent_b': (data - lower_band) / (upper_band - lower_band)
    }


def atr(high, low, close, window=14):
    """Average True Range."""
    high_low = high - low
    high_close_prev = np.abs(high - close.shift(1))
    low_close_prev = np.abs(low - close.shift(1))
    
    true_range = pd.concat([high_low, high_close_prev, low_close_prev], axis=1).max(axis=1)
    atr = true_range.rolling(window=window).mean()
    
    return atr


def returns(data, periods=1):
    """Simple returns."""
    return data.pct_change(periods=periods)


def log_returns(data, periods=1):
    """Log returns."""
    return np.log(data / data.shift(periods))


def volatility(data, window=20):
    """Rolling volatility."""
    ret = returns(data)
    return ret.rolling(window=window).std()


def z_score(data, window):
    """Rolling z-score."""
    rolling_mean = data.rolling(window=window).mean()
    rolling_std = data.rolling(window=window).std()
    
    return (data - rolling_mean) / rolling_std


def momentum(data, window):
    """Momentum."""
    return data / data.shift(window) - 1


def test_feature_engineering():
    """Test feature engineering functionality."""
    print("Minimal Feature Engineering Test")
    print("="*50)
    
    # Create sample data
    np.random.seed(42)
    dates = pd.date_range('2020-01-01', periods=100, freq='D')
    
    # Generate realistic price data
    price = 100
    prices = [price]
    
    for _ in range(99):
        change = np.random.normal(0, 0.02)  # 2% daily volatility
        price = price * (1 + change)
        prices.append(price)
    
    df = pd.DataFrame({
        'timestamp': dates,
        'open': prices,
        'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
        'close': prices,
        'volume': np.random.randint(1000, 10000, 100)
    })
    
    print(f"Sample data shape: {df.shape}")
    print(f"Price range: {df['close'].min():.2f} to {df['close'].max():.2f}")
    
    # Test technical indicators
    print("\nTesting Technical Indicators...")
    
    # SMA
    df['sma_20'] = sma(df['close'], 20)
    print(f"SMA 20 (last 5): {df['sma_20'].tail().values}")
    
    # EMA
    df['ema_12'] = ema(df['close'], 12)
    print(f"EMA 12 (last 5): {df['ema_12'].tail().values}")
    
    # RSI
    df['rsi'] = rsi(df['close'])
    print(f"RSI (last 5): {df['rsi'].tail().values}")
    
    # MACD
    macd_data = macd(df['close'])
    df['macd'] = macd_data['macd']
    df['macd_signal'] = macd_data['signal']
    df['macd_histogram'] = macd_data['histogram']
    print(f"MACD (last 5): {df['macd'].tail().values}")
    
    # Bollinger Bands
    bb_data = bollinger_bands(df['close'])
    df['bb_upper'] = bb_data['upper']
    df['bb_lower'] = bb_data['lower']
    df['bb_percent_b'] = bb_data['percent_b']
    print(f"BB %B (last 5): {df['bb_percent_b'].tail().values}")
    
    # ATR
    df['atr'] = atr(df['high'], df['low'], df['close'])
    print(f"ATR (last 5): {df['atr'].tail().values}")
    
    # Test statistical features
    print("\nTesting Statistical Features...")
    
    # Returns
    df['returns'] = returns(df['close'])
    print(f"Returns (last 5): {df['returns'].tail().values}")
    
    # Log returns
    df['log_returns'] = log_returns(df['close'])
    print(f"Log Returns (last 5): {df['log_returns'].tail().values}")
    
    # Volatility
    df['volatility'] = volatility(df['close'])
    print(f"Volatility (last 5): {df['volatility'].tail().values}")
    
    # Z-Score
    df['z_score'] = z_score(df['close'], 20)
    print(f"Z-Score (last 5): {df['z_score'].tail().values}")
    
    # Momentum
    df['momentum'] = momentum(df['close'], 10)
    print(f"Momentum (last 5): {df['momentum'].tail().values}")
    
    # Add lagged features
    print("\nAdding Lagged Features...")
    for lag in [1, 2, 3, 5]:
        df[f'close_lag_{lag}'] = df['close'].shift(lag)
        df[f'volume_lag_{lag}'] = df['volume'].shift(lag)
    
    # Add time features
    print("Adding Time Features...")
    df['hour'] = df['timestamp'].dt.hour
    df['day_of_week'] = df['timestamp'].dt.dayofweek
    df['month'] = df['timestamp'].dt.month
    
    # Cyclical encoding
    df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
    df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
    df['day_sin'] = np.sin(2 * np.pi * df['day_of_week'] / 7)
    df['day_cos'] = np.cos(2 * np.pi * df['day_of_week'] / 7)
    
    print(f"\nFinal dataset shape: {df.shape}")
    print(f"Total features created: {df.shape[1] - 6}")  # Subtract original columns
    
    # Check for missing values
    missing_counts = df.isnull().sum()
    features_with_missing = missing_counts[missing_counts > 0]
    print(f"Features with missing values: {len(features_with_missing)}")
    
    if len(features_with_missing) > 0:
        print("Top features with missing values:")
        for feature, count in features_with_missing.head(10).items():
            print(f"  {feature}: {count} ({count/len(df)*100:.1f}%)")
    
    # Save results
    output_dir = Path("data/processed")
    output_dir.mkdir(exist_ok=True)
    
    df.to_csv(output_dir / "minimal_features_test.csv", index=False)
    print(f"\nSaved test results to: {output_dir / 'minimal_features_test.csv'}")
    
    # Feature summary
    print("\nFeature Summary:")
    all_features = list(df.columns)
    original_features = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
    created_features = [f for f in all_features if f not in original_features]
    
    # Categorize features
    tech_indicators = [f for f in created_features if any(indicator in f for indicator in ['sma', 'ema', 'rsi', 'macd', 'bb', 'atr'])]
    stat_features = [f for f in created_features if any(stat in f for stat in ['returns', 'volatility', 'z_score', 'momentum'])]
    lag_features = [f for f in created_features if 'lag' in f]
    time_features = [f for f in created_features if any(time_word in f for time_word in ['hour', 'day', 'month', 'sin', 'cos'])]
    
    print(f"  Technical Indicators: {len(tech_indicators)}")
    print(f"    {tech_indicators}")
    print(f"  Statistical Features: {len(stat_features)}")
    print(f"    {stat_features}")
    print(f"  Lagged Features: {len(lag_features)}")
    print(f"    {lag_features[:10]}{'...' if len(lag_features) > 10 else ''}")
    print(f"  Time Features: {len(time_features)}")
    print(f"    {time_features}")
    
    # Basic statistics for key features
    print("\nBasic Statistics for Key Features:")
    key_features = ['close', 'sma_20', 'ema_12', 'rsi', 'macd', 'bb_percent_b', 'atr', 'volatility']
    available_key_features = [f for f in key_features if f in df.columns]
    
    if available_key_features:
        stats_df = df[available_key_features].describe()
        print(stats_df.round(4))
    
    print("\n" + "="*50)
    print("Minimal Feature Engineering Test Completed Successfully!")
    print("="*50)
    
    return df


if __name__ == "__main__":
    test_feature_engineering()
