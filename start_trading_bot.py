#!/usr/bin/env python3
"""
AI Trading Bot Startup Script
Generated on: 2025-07-31 19:48:07
"""

import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.append(str(project_root / "src"))

# Import main components
from api.main import app
from trading.mt5_connector import MT5Connector
from monitoring.dashboard import TradingDashboard
from risk_management.risk_manager import RiskManager

def main():
    print("Starting AI Trading Bot...")
    
    # Initialize components
    mt5_connector = MT5Connector()
    dashboard = TradingDashboard()
    risk_manager = RiskManager()
    
    # Connect to MT5
    if mt5_connector.connect():
        print("[SUCCESS] Connected to MetaTrader 5")
    else:
        print("[WARNING] MT5 connection failed - running in simulation mode")

    # Start API server
    import uvicorn
    print("[INFO] Starting API server on http://localhost:8000")
    uvicorn.run(app, host="0.0.0.0", port=8000)

if __name__ == "__main__":
    main()
