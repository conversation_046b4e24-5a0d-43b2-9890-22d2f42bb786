#!/usr/bin/env python3
"""
AI Trading Bot - Automatic Trading Startup Script
"""

import sys
import os
import json
import asyncio
import signal
import logging
from pathlib import Path
from datetime import datetime
from typing import Optional

# Add project root to path
project_root = Path(__file__).parent
sys.path.append(str(project_root / "src"))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/trading_bot.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# Import components
from trading.auto_trader import AutoTrader
from api.main import app
import uvicorn


class TradingBotManager:
    """
    Main trading bot manager with automatic trading capabilities.
    """

    def __init__(self, config_path: str = "config/config_template.json"):
        """Initialize the trading bot manager."""
        self.config_path = Path(config_path)
        self.config = self.load_configuration()
        self.running = False
        self.auto_trader = None
        self.api_server = None

    def load_configuration(self) -> dict:
        """Load configuration from file."""
        try:
            if self.config_path.exists():
                with open(self.config_path, 'r') as f:
                    config = json.load(f)
                logger.info(f"Configuration loaded from {self.config_path}")
                return config
            else:
                logger.warning(f"Configuration file not found: {self.config_path}")
                return self.get_default_config()
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            return self.get_default_config()

    def get_default_config(self) -> dict:
        """Get default configuration with high-risk settings."""
        return {
            "trading_bot": {"name": "AI Trading Bot", "version": "1.0.0"},
            "trading": {
                "auto_trading_enabled": True,
                "symbols": ["EURUSD", "GBPUSD", "USDJPY"],
                "timeframe": "M15",
                "signal_interval": 300,
                "min_signal_strength": 0.6,
                "max_trades_per_day": 10
            },
            "risk_management": {
                "position_sizing": {"risk_per_trade": 0.30},  # 30% risk!
                "portfolio_limits": {
                    "max_daily_loss": 0.50,
                    "max_drawdown": 0.70,
                    "max_positions": 5
                }
            },
            "api": {"enabled": True, "host": "0.0.0.0", "port": 8000}
        }

    def setup_signal_handlers(self):
        """Set up signal handlers for graceful shutdown."""
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, shutting down...")
            self.shutdown()
            sys.exit(0)

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

    async def start_api_server(self):
        """Start the FastAPI server."""
        api_config = self.config.get('api', {})

        if not api_config.get('enabled', True):
            logger.info("API server disabled in configuration")
            return

        try:
            host = api_config.get('host', '0.0.0.0')
            port = api_config.get('port', 8000)

            logger.info(f"🚀 Starting API server on http://{host}:{port}")

            # Configure uvicorn
            config = uvicorn.Config(
                app=app,
                host=host,
                port=port,
                log_level="info"
            )

            server = uvicorn.Server(config)
            self.api_server = server

            # Run server in background
            await server.serve()

        except Exception as e:
            logger.error(f"Error starting API server: {e}")

    async def start_auto_trading(self):
        """Start automatic trading."""
        trading_config = self.config.get('trading', {})

        if not trading_config.get('auto_trading_enabled', False):
            logger.info("⚠️ Automatic trading is DISABLED in configuration")
            logger.info("💡 Set 'auto_trading_enabled': true to enable auto-trading")
            return

        try:
            logger.info("🤖 Initializing Automatic Trading Engine...")

            # Initialize auto-trader with configuration
            self.auto_trader = AutoTrader(self.config)

            # Start auto-trading
            await self.auto_trader.start_auto_trading()

        except Exception as e:
            logger.error(f"❌ Error starting auto-trading: {e}")

    async def start(self, enable_auto_trading: bool = True):
        """Start the trading bot."""
        logger.info("="*80)
        logger.info("🤖 AI TRADING BOT - AUTOMATIC TRADING MODE")
        logger.info("="*80)
        logger.info("⚠️  HIGH RISK CONFIGURATION ACTIVE:")
        logger.info("   • 30% risk per trade")
        logger.info("   • 50% max daily loss")
        logger.info("   • 70% max drawdown")
        logger.info("   • Automatic trading enabled")
        logger.info("="*80)

        try:
            # Set up signal handlers
            self.setup_signal_handlers()

            # Set running flag
            self.running = True

            # Start API server in background
            api_task = asyncio.create_task(self.start_api_server())

            # Start auto-trading if enabled
            if enable_auto_trading:
                await self.start_auto_trading()
            else:
                logger.info("📊 Running in monitoring mode only")
                logger.info("🌐 Dashboard available at: http://localhost:8000")
                logger.info("📖 API docs available at: http://localhost:8000/docs")

                # Keep running for API server
                while self.running:
                    await asyncio.sleep(1)

        except KeyboardInterrupt:
            logger.info("Shutdown requested by user")
            self.shutdown()
        except Exception as e:
            logger.error(f"Error starting trading bot: {e}")
            self.shutdown()
            raise

    def shutdown(self):
        """Gracefully shutdown the trading bot."""
        logger.info("🛑 Shutting down AI Trading Bot...")

        self.running = False

        try:
            # Stop auto-trading
            if self.auto_trader:
                self.auto_trader.stop_auto_trading()
                logger.info("✅ Auto-trading stopped")

            # Stop API server
            if self.api_server:
                self.api_server.should_exit = True
                logger.info("✅ API server stopped")

            logger.info("✅ AI Trading Bot shutdown complete")

        except Exception as e:
            logger.error(f"Error during shutdown: {e}")


def main():
    """Main entry point."""
    import argparse

    parser = argparse.ArgumentParser(description="AI Trading Bot with Automatic Trading")
    parser.add_argument("--config", default="config/config_template.json",
                       help="Configuration file path")
    parser.add_argument("--no-auto-trading", action="store_true",
                       help="Disable automatic trading (monitoring only)")
    parser.add_argument("--demo", action="store_true",
                       help="Run in demo mode")

    args = parser.parse_args()

    # Create and start trading bot
    try:
        bot_manager = TradingBotManager(args.config)

        # Show startup message
        print("🤖 AI Trading Bot - Automatic Trading Mode")
        print("="*60)
        print("⚠️  WARNING: HIGH RISK CONFIGURATION!")
        print("   • 30% risk per trade")
        print("   • Automatic trading enabled")
        print("   • Monitor closely!")
        print("="*60)

        if args.no_auto_trading:
            print("📊 Starting in monitoring mode only...")
            enable_auto_trading = False
        else:
            print("🚀 Starting automatic trading...")
            enable_auto_trading = True

        # Run the bot
        asyncio.run(bot_manager.start(enable_auto_trading))

    except KeyboardInterrupt:
        logger.info("Shutdown requested")
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
