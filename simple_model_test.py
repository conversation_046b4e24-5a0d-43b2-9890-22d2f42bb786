#!/usr/bin/env python3
"""
Simple test script for model training functionality.
"""

import pandas as pd
import numpy as np
from pathlib import Path
import sys
import json

# Add src to path
sys.path.append(str(Path(__file__).parent / "src" / "models"))

# Import modules directly
from supervised_models import SupervisedModels


def create_sample_data():
    """Create sample data for testing."""
    np.random.seed(42)
    
    # Create base price data
    n_samples = 200
    dates = pd.date_range('2020-01-01', periods=n_samples, freq='H')
    
    price = 100
    prices = [price]
    
    for _ in range(n_samples - 1):
        change = np.random.normal(0, 0.02)
        price = price * (1 + change)
        prices.append(price)
    
    # Create OHLCV data
    df = pd.DataFrame({
        'timestamp': dates,
        'open': prices,
        'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
        'close': prices,
        'volume': np.random.randint(1000, 10000, n_samples)
    })
    
    # Add some basic features
    df['sma_10'] = df['close'].rolling(10).mean()
    df['sma_20'] = df['close'].rolling(20).mean()
    df['ema_12'] = df['close'].ewm(span=12).mean()
    df['returns'] = df['close'].pct_change()
    df['volatility'] = df['returns'].rolling(10).std()
    
    # Lagged features
    for lag in [1, 2, 3]:
        df['close_lag_' + str(lag)] = df['close'].shift(lag)
    
    # Time features
    df['hour'] = df['timestamp'].dt.hour
    df['day_of_week'] = df['timestamp'].dt.dayofweek
    
    # Remove rows with NaN values
    df = df.dropna().reset_index(drop=True)
    
    print(f"Created sample data with {df.shape[0]} rows and {df.shape[1]} columns")
    
    return df


def test_supervised_models():
    """Test supervised learning models."""
    print("\n" + "="*50)
    print("Testing Supervised Learning Models")
    print("="*50)
    
    # Create sample data
    df = create_sample_data()
    
    # Initialize supervised models
    sup_models = SupervisedModels()
    
    # Prepare data
    print("\nPreparing data for supervised learning...")
    X, y = sup_models.prepare_data(df, target_column='close', prediction_horizon=1, task_type='regression')
    print(f"Data shape: X={X.shape}, y={y.shape}")
    
    # Split data
    X_train, X_val, X_test, y_train, y_val, y_test = sup_models.split_data(X, y)
    print(f"Train: {X_train.shape}, Val: {X_val.shape}, Test: {X_test.shape}")
    
    results = {}
    
    # Test Linear Regression
    print("\nTesting Linear Regression...")
    try:
        lr_result = sup_models.train_linear_regression(X_train, y_train)
        print(f"Linear Regression Training: {lr_result}")
        
        # Test predictions
        lr_pred = sup_models.predict('linear_regression', X_test)
        print(f"Linear Regression Predictions shape: {lr_pred.shape}")
        
        # Evaluate
        lr_eval = sup_models.evaluate_model('linear_regression', X_test, y_test)
        print(f"Linear Regression Evaluation: {lr_eval}")
        
        results['linear_regression'] = {
            'training': lr_result,
            'evaluation': lr_eval,
            'predictions_shape': lr_pred.shape
        }
        
    except Exception as e:
        print(f"Linear Regression failed: {e}")
        results['linear_regression'] = {'error': str(e)}
    
    # Test Random Forest
    print("\nTesting Random Forest...")
    try:
        rf_result = sup_models.train_random_forest(X_train, y_train, n_estimators=20, max_depth=5)
        print(f"Random Forest Training: {rf_result}")
        
        if 'random_forest' in sup_models.models:
            rf_pred = sup_models.predict('random_forest', X_test)
            rf_eval = sup_models.evaluate_model('random_forest', X_test, y_test)
            print(f"Random Forest Evaluation: {rf_eval}")
            
            results['random_forest'] = {
                'training': rf_result,
                'evaluation': rf_eval,
                'predictions_shape': rf_pred.shape
            }
        else:
            results['random_forest'] = {'training': rf_result}
            
    except Exception as e:
        print(f"Random Forest failed: {e}")
        results['random_forest'] = {'error': str(e)}
    
    # Test XGBoost/Gradient Boosting
    print("\nTesting Gradient Boosting...")
    try:
        xgb_result = sup_models.train_gradient_boosting(X_train, y_train, n_estimators=20)
        print(f"Gradient Boosting Training: {xgb_result}")
        
        # Find the actual model name
        model_name = None
        for name in ['xgboost', 'gradient_boosting']:
            if name in sup_models.models:
                model_name = name
                break
        
        if model_name:
            xgb_pred = sup_models.predict(model_name, X_test)
            xgb_eval = sup_models.evaluate_model(model_name, X_test, y_test)
            print(f"Gradient Boosting Evaluation: {xgb_eval}")
            
            results['gradient_boosting'] = {
                'training': xgb_result,
                'evaluation': xgb_eval,
                'predictions_shape': xgb_pred.shape,
                'model_name': model_name
            }
        else:
            results['gradient_boosting'] = {'training': xgb_result}
            
    except Exception as e:
        print(f"Gradient Boosting failed: {e}")
        results['gradient_boosting'] = {'error': str(e)}
    
    # Test classification task
    print("\nTesting Classification Task...")
    try:
        X_class, y_class = sup_models.prepare_data(df, target_column='close', prediction_horizon=1, task_type='classification')
        X_train_class, X_val_class, X_test_class, y_train_class, y_val_class, y_test_class = sup_models.split_data(X_class, y_class)
        
        print(f"Classification data shape: X={X_class.shape}, y={y_class.shape}")
        print(f"Class distribution: {np.bincount(y_class.astype(int))}")
        
        # Test Random Forest for classification
        rf_class_result = sup_models.train_random_forest(X_train_class, y_train_class, n_estimators=20, task_type='classification')
        print(f"Random Forest Classification: {rf_class_result}")
        
        if 'random_forest' in sup_models.models:
            rf_class_eval = sup_models.evaluate_model('random_forest', X_test_class, y_test_class, task_type='classification')
            print(f"Random Forest Classification Evaluation: {rf_class_eval}")
            
            results['classification'] = {
                'training': rf_class_result,
                'evaluation': rf_class_eval
            }
        
    except Exception as e:
        print(f"Classification test failed: {e}")
        results['classification'] = {'error': str(e)}
    
    # Model summary
    summary = sup_models.get_model_summary()
    print(f"\nModel Summary: {summary}")
    
    results['summary'] = summary
    
    return results


def main():
    """Main test function."""
    print("Simple Model Training Test")
    print("="*50)
    
    try:
        # Test supervised models
        results = test_supervised_models()
        
        print("\n" + "="*50)
        print("Model Training Test Completed!")
        print("="*50)
        
        # Print summary
        successful_models = []
        failed_models = []
        
        for model_name, result in results.items():
            if model_name == 'summary':
                continue
            
            if 'error' in result:
                failed_models.append(model_name)
                print(f"❌ {model_name}: {result['error']}")
            else:
                successful_models.append(model_name)
                print(f"✅ {model_name}: Success")
                if 'evaluation' in result:
                    eval_metrics = result['evaluation']
                    if 'r2' in eval_metrics:
                        print(f"   R² Score: {eval_metrics['r2']:.4f}")
                    if 'rmse' in eval_metrics:
                        print(f"   RMSE: {eval_metrics['rmse']:.4f}")
                    if 'accuracy' in eval_metrics:
                        print(f"   Accuracy: {eval_metrics['accuracy']:.4f}")
        
        print(f"\nSuccessful models: {len(successful_models)}")
        print(f"Failed models: {len(failed_models)}")
        
        # Save test results
        output_dir = Path("data/processed")
        output_dir.mkdir(exist_ok=True)
        
        test_summary = {
            'successful_models': successful_models,
            'failed_models': failed_models,
            'total_tested': len(successful_models) + len(failed_models),
            'success_rate': len(successful_models) / (len(successful_models) + len(failed_models)) if (len(successful_models) + len(failed_models)) > 0 else 0
        }
        
        with open(output_dir / "simple_model_test_summary.json", 'w') as f:
            json.dump(test_summary, f, indent=2, default=str)
        
        print(f"\nTest summary saved to: {output_dir / 'simple_model_test_summary.json'}")
        
    except Exception as e:
        print(f"Error during model training test: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
