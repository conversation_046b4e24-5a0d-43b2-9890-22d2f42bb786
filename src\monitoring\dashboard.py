"""
Monitoring dashboard for trading bot performance and system health.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any, Union
from datetime import datetime, timedelta
import json
from pathlib import Path
import time


class TradingDashboard:
    """
    Real-time monitoring dashboard for trading bot performance.
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """
        Initialize TradingDashboard.
        
        Args:
            config: Dashboard configuration
        """
        self.config = config or self._get_default_config()
        self.metrics_history = []
        self.alerts = []
        self.system_status = {}
        
    def _get_default_config(self) -> Dict:
        """Get default dashboard configuration."""
        return {
            'refresh_interval': 60,  # seconds
            'alert_thresholds': {
                'max_drawdown': 0.10,  # 10%
                'daily_loss': 0.05,    # 5%
                'win_rate_min': 0.40,  # 40%
                'system_cpu': 80,      # 80%
                'system_memory': 85,   # 85%
                'connection_timeout': 30  # seconds
            },
            'display_periods': {
                'real_time': 1,      # 1 day
                'short_term': 7,     # 1 week
                'medium_term': 30,   # 1 month
                'long_term': 90      # 3 months
            }
        }
    
    def update_trading_metrics(self, metrics: Dict[str, Any]):
        """
        Update trading performance metrics.
        
        Args:
            metrics: Dictionary containing trading metrics
        """
        timestamp = datetime.now()
        
        # Add timestamp to metrics
        metrics_with_time = {
            'timestamp': timestamp,
            **metrics
        }
        
        # Store in history
        self.metrics_history.append(metrics_with_time)
        
        # Keep only recent history (last 1000 records)
        if len(self.metrics_history) > 1000:
            self.metrics_history = self.metrics_history[-1000:]
        
        # Check for alerts
        self._check_trading_alerts(metrics)
    
    def update_system_status(self, status: Dict[str, Any]):
        """
        Update system health status.
        
        Args:
            status: Dictionary containing system status
        """
        self.system_status = {
            'timestamp': datetime.now(),
            **status
        }
        
        # Check for system alerts
        self._check_system_alerts(status)
    
    def _check_trading_alerts(self, metrics: Dict[str, Any]):
        """Check trading metrics for alert conditions."""
        thresholds = self.config['alert_thresholds']
        
        # Check drawdown
        if metrics.get('current_drawdown', 0) < -thresholds['max_drawdown']:
            self._create_alert(
                'HIGH',
                'Maximum Drawdown Exceeded',
                f"Current drawdown: {metrics.get('current_drawdown', 0):.2%}"
            )
        
        # Check daily loss
        if metrics.get('daily_pnl', 0) < -thresholds['daily_loss'] * metrics.get('account_balance', 1):
            self._create_alert(
                'HIGH',
                'Daily Loss Limit Exceeded',
                f"Daily P&L: ${metrics.get('daily_pnl', 0):,.2f}"
            )
        
        # Check win rate
        if metrics.get('win_rate', 1) < thresholds['win_rate_min']:
            self._create_alert(
                'MEDIUM',
                'Low Win Rate',
                f"Win rate: {metrics.get('win_rate', 0):.1%}"
            )
    
    def _check_system_alerts(self, status: Dict[str, Any]):
        """Check system status for alert conditions."""
        thresholds = self.config['alert_thresholds']
        
        # Check CPU usage
        if status.get('cpu_usage', 0) > thresholds['system_cpu']:
            self._create_alert(
                'MEDIUM',
                'High CPU Usage',
                f"CPU usage: {status.get('cpu_usage', 0):.1f}%"
            )
        
        # Check memory usage
        if status.get('memory_usage', 0) > thresholds['system_memory']:
            self._create_alert(
                'MEDIUM',
                'High Memory Usage',
                f"Memory usage: {status.get('memory_usage', 0):.1f}%"
            )
        
        # Check connection status
        if not status.get('mt5_connected', True):
            self._create_alert(
                'HIGH',
                'MT5 Connection Lost',
                "MetaTrader 5 connection is down"
            )
    
    def _create_alert(self, severity: str, title: str, message: str):
        """Create a new alert."""
        alert = {
            'id': len(self.alerts) + 1,
            'timestamp': datetime.now(),
            'severity': severity,
            'title': title,
            'message': message,
            'acknowledged': False
        }
        
        self.alerts.append(alert)
        
        # Keep only recent alerts (last 100)
        if len(self.alerts) > 100:
            self.alerts = self.alerts[-100:]
        
        print(f"ALERT [{severity}]: {title} - {message}")
    
    def acknowledge_alert(self, alert_id: int):
        """Acknowledge an alert."""
        for alert in self.alerts:
            if alert['id'] == alert_id:
                alert['acknowledged'] = True
                alert['acknowledged_at'] = datetime.now()
                break
    
    def get_dashboard_data(self) -> Dict[str, Any]:
        """Get comprehensive dashboard data."""
        current_time = datetime.now()
        
        # Get recent metrics
        recent_metrics = self._get_recent_metrics(hours=24)
        
        # Calculate summary statistics
        summary_stats = self._calculate_summary_stats(recent_metrics)
        
        # Get active alerts
        active_alerts = [alert for alert in self.alerts if not alert['acknowledged']]
        
        # Get performance charts data
        charts_data = self._get_charts_data()
        
        return {
            'timestamp': current_time,
            'summary': summary_stats,
            'system_status': self.system_status,
            'active_alerts': active_alerts,
            'recent_alerts': self.alerts[-10:],  # Last 10 alerts
            'charts': charts_data,
            'trading_status': self._get_trading_status()
        }
    
    def _get_recent_metrics(self, hours: int = 24) -> List[Dict]:
        """Get metrics from the last N hours."""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        return [
            metric for metric in self.metrics_history
            if metric['timestamp'] > cutoff_time
        ]
    
    def _calculate_summary_stats(self, metrics: List[Dict]) -> Dict[str, Any]:
        """Calculate summary statistics from metrics."""
        if not metrics:
            return {
                'total_trades': 0,
                'win_rate': 0,
                'total_pnl': 0,
                'current_drawdown': 0,
                'account_balance': 0,
                'active_positions': 0
            }
        
        latest = metrics[-1] if metrics else {}
        
        # Calculate aggregated stats
        total_trades = sum(m.get('trades_today', 0) for m in metrics)
        winning_trades = sum(m.get('winning_trades', 0) for m in metrics)
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        total_pnl = sum(m.get('daily_pnl', 0) for m in metrics)
        
        return {
            'total_trades': total_trades,
            'win_rate': win_rate,
            'total_pnl': total_pnl,
            'current_drawdown': latest.get('current_drawdown', 0),
            'account_balance': latest.get('account_balance', 0),
            'active_positions': latest.get('active_positions', 0),
            'portfolio_value': latest.get('portfolio_value', 0),
            'daily_return': latest.get('daily_return', 0),
            'monthly_return': latest.get('monthly_return', 0)
        }
    
    def _get_charts_data(self) -> Dict[str, Any]:
        """Get data for dashboard charts."""
        # Equity curve data
        equity_data = []
        pnl_data = []
        drawdown_data = []
        
        for metric in self.metrics_history[-100:]:  # Last 100 points
            equity_data.append({
                'timestamp': metric['timestamp'].isoformat(),
                'value': metric.get('portfolio_value', 0)
            })
            
            pnl_data.append({
                'timestamp': metric['timestamp'].isoformat(),
                'value': metric.get('daily_pnl', 0)
            })
            
            drawdown_data.append({
                'timestamp': metric['timestamp'].isoformat(),
                'value': metric.get('current_drawdown', 0)
            })
        
        # Performance by symbol
        symbol_performance = self._calculate_symbol_performance()
        
        return {
            'equity_curve': equity_data,
            'daily_pnl': pnl_data,
            'drawdown': drawdown_data,
            'symbol_performance': symbol_performance
        }
    
    def _calculate_symbol_performance(self) -> List[Dict]:
        """Calculate performance by trading symbol."""
        # This would typically come from position data
        # For now, return sample data
        return [
            {'symbol': 'EURUSD', 'pnl': 150.0, 'trades': 12, 'win_rate': 0.58},
            {'symbol': 'GBPUSD', 'pnl': -75.0, 'trades': 8, 'win_rate': 0.38},
            {'symbol': 'USDJPY', 'pnl': 200.0, 'trades': 15, 'win_rate': 0.67},
            {'symbol': 'EURJPY', 'pnl': 50.0, 'trades': 6, 'win_rate': 0.50}
        ]
    
    def _get_trading_status(self) -> Dict[str, Any]:
        """Get current trading status."""
        return {
            'is_trading': self.system_status.get('trading_active', False),
            'last_trade_time': self.system_status.get('last_trade_time'),
            'next_signal_check': self.system_status.get('next_signal_check'),
            'market_hours': self.system_status.get('market_open', True),
            'connection_status': self.system_status.get('mt5_connected', False)
        }
    
    def generate_performance_report(self, period_days: int = 30) -> Dict[str, Any]:
        """Generate detailed performance report."""
        cutoff_date = datetime.now() - timedelta(days=period_days)
        
        period_metrics = [
            m for m in self.metrics_history
            if m['timestamp'] > cutoff_date
        ]
        
        if not period_metrics:
            return {'error': 'No data available for the specified period'}
        
        # Calculate performance metrics
        start_value = period_metrics[0].get('portfolio_value', 0)
        end_value = period_metrics[-1].get('portfolio_value', 0)
        total_return = (end_value - start_value) / start_value if start_value > 0 else 0
        
        daily_returns = []
        for i in range(1, len(period_metrics)):
            prev_value = period_metrics[i-1].get('portfolio_value', 0)
            curr_value = period_metrics[i].get('portfolio_value', 0)
            if prev_value > 0:
                daily_returns.append((curr_value - prev_value) / prev_value)
        
        if daily_returns:
            volatility = np.std(daily_returns) * np.sqrt(252)  # Annualized
            sharpe_ratio = np.mean(daily_returns) / np.std(daily_returns) * np.sqrt(252) if np.std(daily_returns) > 0 else 0
        else:
            volatility = sharpe_ratio = 0
        
        # Calculate maximum drawdown
        portfolio_values = [m.get('portfolio_value', 0) for m in period_metrics]
        if portfolio_values:
            peak = portfolio_values[0]
            max_dd = 0
            for value in portfolio_values:
                if value > peak:
                    peak = value
                dd = (value - peak) / peak if peak > 0 else 0
                if dd < max_dd:
                    max_dd = dd
        else:
            max_dd = 0
        
        # Trading statistics
        total_trades = sum(m.get('trades_today', 0) for m in period_metrics)
        winning_trades = sum(m.get('winning_trades', 0) for m in period_metrics)
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        total_pnl = sum(m.get('daily_pnl', 0) for m in period_metrics)
        
        return {
            'period': {
                'start_date': period_metrics[0]['timestamp'].date(),
                'end_date': period_metrics[-1]['timestamp'].date(),
                'days': period_days
            },
            'performance': {
                'total_return': total_return,
                'annualized_return': total_return * (365 / period_days),
                'volatility': volatility,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_dd,
                'start_value': start_value,
                'end_value': end_value,
                'total_pnl': total_pnl
            },
            'trading': {
                'total_trades': total_trades,
                'winning_trades': winning_trades,
                'losing_trades': total_trades - winning_trades,
                'win_rate': win_rate,
                'avg_daily_trades': total_trades / period_days if period_days > 0 else 0
            },
            'risk': {
                'max_drawdown': max_dd,
                'volatility': volatility,
                'var_95': np.percentile(daily_returns, 5) if daily_returns else 0,
                'worst_day': min(daily_returns) if daily_returns else 0,
                'best_day': max(daily_returns) if daily_returns else 0
            }
        }
    
    def export_dashboard_data(self, filepath: str = "dashboard_export.json"):
        """Export dashboard data to file."""
        dashboard_data = self.get_dashboard_data()
        
        # Convert datetime objects to strings
        def convert_datetime(obj):
            if isinstance(obj, dict):
                return {k: convert_datetime(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_datetime(item) for item in obj]
            elif isinstance(obj, datetime):
                return obj.isoformat()
            else:
                return obj
        
        dashboard_data = convert_datetime(dashboard_data)
        
        with open(filepath, 'w') as f:
            json.dump(dashboard_data, f, indent=2, default=str)
        
        print(f"Dashboard data exported to: {filepath}")
    
    def get_alert_summary(self) -> Dict[str, Any]:
        """Get summary of alerts."""
        total_alerts = len(self.alerts)
        active_alerts = len([a for a in self.alerts if not a['acknowledged']])
        
        # Count by severity
        severity_counts = {'HIGH': 0, 'MEDIUM': 0, 'LOW': 0}
        for alert in self.alerts:
            if not alert['acknowledged']:
                severity_counts[alert['severity']] += 1
        
        # Recent alert trend
        recent_alerts = [
            a for a in self.alerts
            if a['timestamp'] > datetime.now() - timedelta(hours=24)
        ]
        
        return {
            'total_alerts': total_alerts,
            'active_alerts': active_alerts,
            'acknowledged_alerts': total_alerts - active_alerts,
            'severity_breakdown': severity_counts,
            'alerts_last_24h': len(recent_alerts),
            'most_recent': self.alerts[-1] if self.alerts else None
        }
    
    def clear_old_data(self, days_to_keep: int = 30):
        """Clear old metrics and alerts."""
        cutoff_date = datetime.now() - timedelta(days=days_to_keep)
        
        # Clear old metrics
        self.metrics_history = [
            m for m in self.metrics_history
            if m['timestamp'] > cutoff_date
        ]
        
        # Clear old acknowledged alerts
        self.alerts = [
            a for a in self.alerts
            if a['timestamp'] > cutoff_date or not a['acknowledged']
        ]
        
        print(f"Cleared data older than {days_to_keep} days")


class SystemMonitor:
    """
    System health monitoring for the trading bot.
    """
    
    @staticmethod
    def get_system_metrics() -> Dict[str, Any]:
        """Get current system metrics."""
        try:
            import psutil
            
            # CPU and memory usage
            cpu_usage = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # Network statistics
            network = psutil.net_io_counters()
            
            return {
                'cpu_usage': cpu_usage,
                'memory_usage': memory.percent,
                'memory_available': memory.available,
                'disk_usage': disk.percent,
                'disk_free': disk.free,
                'network_sent': network.bytes_sent,
                'network_recv': network.bytes_recv,
                'timestamp': datetime.now()
            }
        except ImportError:
            # Fallback if psutil is not available
            return {
                'cpu_usage': 0,
                'memory_usage': 0,
                'memory_available': 0,
                'disk_usage': 0,
                'disk_free': 0,
                'network_sent': 0,
                'network_recv': 0,
                'timestamp': datetime.now(),
                'note': 'psutil not available - using placeholder values'
            }
    
    @staticmethod
    def check_dependencies() -> Dict[str, bool]:
        """Check if required dependencies are available."""
        dependencies = {
            'pandas': False,
            'numpy': False,
            'sqlite3': False,
            'json': False,
            'pathlib': False
        }
        
        for dep in dependencies:
            try:
                __import__(dep)
                dependencies[dep] = True
            except ImportError:
                dependencies[dep] = False
        
        return dependencies
