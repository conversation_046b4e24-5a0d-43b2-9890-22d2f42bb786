#!/usr/bin/env python3
"""
Environment setup script for The Ultimate AI Trading Bot.
This script sets up the development environment and validates the installation.
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def run_command(command, check=True):
    """Run a shell command and return the result."""
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            check=check, 
            capture_output=True, 
            text=True
        )
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.CalledProcessError as e:
        return False, e.stdout, e.stderr

def check_python_version():
    """Check if Python version is compatible."""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8 or higher is required")
        return False
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} detected")
    return True

def create_virtual_environment():
    """Create a virtual environment if it doesn't exist."""
    venv_path = Path("venv")
    if venv_path.exists():
        print("✅ Virtual environment already exists")
        return True
    
    print("📦 Creating virtual environment...")
    success, stdout, stderr = run_command(f"{sys.executable} -m venv venv")
    if success:
        print("✅ Virtual environment created successfully")
        return True
    else:
        print(f"❌ Failed to create virtual environment: {stderr}")
        return False

def get_activation_command():
    """Get the appropriate activation command for the platform."""
    if platform.system() == "Windows":
        return "venv\\Scripts\\activate"
    else:
        return "source venv/bin/activate"

def install_requirements():
    """Install required packages."""
    print("📦 Installing requirements...")
    
    # Get the appropriate pip command
    if platform.system() == "Windows":
        pip_cmd = "venv\\Scripts\\pip"
    else:
        pip_cmd = "venv/bin/pip"
    
    # Upgrade pip first
    success, stdout, stderr = run_command(f"{pip_cmd} install --upgrade pip")
    if not success:
        print(f"⚠️  Warning: Could not upgrade pip: {stderr}")
    
    # Install requirements
    success, stdout, stderr = run_command(f"{pip_cmd} install -r requirements.txt")
    if success:
        print("✅ Requirements installed successfully")
        return True
    else:
        print(f"❌ Failed to install requirements: {stderr}")
        return False

def validate_installation():
    """Validate that key packages are installed correctly."""
    print("🔍 Validating installation...")
    
    if platform.system() == "Windows":
        python_cmd = "venv\\Scripts\\python"
    else:
        python_cmd = "venv/bin/python"
    
    test_imports = [
        "pandas",
        "numpy",
        "sklearn",
        "tensorflow",
        "torch",
        "backtrader",
        "fastapi",
        "mlflow"
    ]
    
    failed_imports = []
    for package in test_imports:
        success, stdout, stderr = run_command(
            f"{python_cmd} -c \"import {package}; print(f'{package} imported successfully')\"",
            check=False
        )
        if success:
            print(f"✅ {package}")
        else:
            print(f"❌ {package}")
            failed_imports.append(package)
    
    if failed_imports:
        print(f"\n⚠️  Some packages failed to import: {', '.join(failed_imports)}")
        print("You may need to install additional system dependencies.")
        return False
    
    print("✅ All key packages validated successfully")
    return True

def setup_git_repository():
    """Initialize git repository if not already done."""
    if Path(".git").exists():
        print("✅ Git repository already initialized")
        return True
    
    print("📝 Initializing git repository...")
    success, stdout, stderr = run_command("git init")
    if success:
        # Create .gitignore
        gitignore_content = """
# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo

# Data
data/raw/*.csv
data/processed/*
!data/raw/.gitkeep
!data/processed/.gitkeep

# Models
models/*.pkl
models/*.h5
models/*.pt

# Logs
logs/
*.log

# MLflow
mlruns/
mlflow.db

# Environment variables
.env
.env.local

# OS
.DS_Store
Thumbs.db
"""
        with open(".gitignore", "w") as f:
            f.write(gitignore_content.strip())
        
        print("✅ Git repository initialized with .gitignore")
        return True
    else:
        print(f"⚠️  Could not initialize git repository: {stderr}")
        return False

def create_placeholder_files():
    """Create placeholder files for empty directories."""
    placeholder_dirs = [
        "data/raw",
        "data/processed", 
        "data/external",
        "logs",
        "models"
    ]
    
    for dir_path in placeholder_dirs:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
        gitkeep_path = Path(dir_path) / ".gitkeep"
        if not gitkeep_path.exists():
            gitkeep_path.touch()
    
    print("✅ Placeholder files created")

def main():
    """Main setup function."""
    print("🚀 Setting up The Ultimate AI Trading Bot environment...\n")
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Create virtual environment
    if not create_virtual_environment():
        sys.exit(1)
    
    # Install requirements
    if not install_requirements():
        sys.exit(1)
    
    # Validate installation
    if not validate_installation():
        print("\n⚠️  Some packages failed validation. The bot may not work correctly.")
        print("Please check the error messages above and install missing dependencies.")
    
    # Setup git repository
    setup_git_repository()
    
    # Create placeholder files
    create_placeholder_files()
    
    print("\n🎉 Environment setup completed successfully!")
    print(f"\nTo activate the environment, run:")
    print(f"  {get_activation_command()}")
    print(f"\nTo start developing, you can now run:")
    print(f"  python scripts/prepare_data.py")
    print(f"  python scripts/train_models.py")

if __name__ == "__main__":
    main()
