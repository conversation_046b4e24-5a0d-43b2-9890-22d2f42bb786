#!/usr/bin/env python3
"""
AI Trading Bot - BTC Auto-Trading (Simple Version)
"""

import sys
import os
import time
from pathlib import Path
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent
sys.path.append(str(project_root / "src"))

def test_mt5_connection():
    """Test MT5 connection for BTC trading."""
    print("AI Trading Bot - BTC Auto-Trading")
    print("="*60)
    print("Target Symbol: BTCUSDm ONLY")
    print("Risk Configuration: 30% per trade")
    print("="*60)
    
    try:
        # Import MT5
        import MetaTrader5 as mt5
        print("[INFO] MetaTrader5 module loaded")
    except ImportError:
        print("[ERROR] MetaTrader5 module not available")
        return False
    
    # Test connection
    print("[INFO] Connecting to MetaTrader 5...")
    
    if not mt5.initialize():
        print("[ERROR] Failed to initialize MT5")
        print("[INFO] Make sure MetaTrader 5 is running and logged in")
        return False
    
    print("[SUCCESS] Connected to MT5")
    
    # Get account info
    account_info = mt5.account_info()
    if account_info is None:
        print("[ERROR] Failed to get account info")
        mt5.shutdown()
        return False
    
    print(f"[INFO] Account: {account_info.login}")
    print(f"[INFO] Balance: ${account_info.balance:,.2f}")
    print(f"[INFO] Server: {account_info.server}")
    
    # Check auto-trading permissions
    terminal_info = mt5.terminal_info()
    if terminal_info is None:
        print("[ERROR] Failed to get terminal info")
        mt5.shutdown()
        return False
    
    auto_trading_ready = (
        terminal_info.trade_allowed and
        not terminal_info.tradeapi_disabled and
        account_info.trade_allowed and
        account_info.trade_expert
    )
    
    print(f"[INFO] Auto-trading ready: {auto_trading_ready}")
    
    if not auto_trading_ready:
        print("[WARNING] Auto-trading not enabled!")
        print("[INFO] Enable in MT5: Tools -> Options -> Expert Advisors")
        print("[INFO] Check 'Allow automated trading'")
    
    # Test BTCUSDm symbol
    print("[INFO] Testing BTCUSDm symbol...")
    
    symbol_info = mt5.symbol_info("BTCUSDm")
    if symbol_info is None:
        print("[ERROR] BTCUSDm symbol not found")
        print("[INFO] Make sure BTCUSDm is available in your account")
        mt5.shutdown()
        return False
    
    # Get current price
    tick = mt5.symbol_info_tick("BTCUSDm")
    if tick is None:
        print("[ERROR] Cannot get BTCUSDm price")
        mt5.shutdown()
        return False
    
    print(f"[SUCCESS] BTCUSDm price: ${tick.bid:,.2f}")
    
    # Test order capabilities (without placing actual order)
    print("[INFO] Testing order capabilities...")
    
    # Calculate test position size (30% risk)
    balance = account_info.balance
    risk_amount = balance * 0.30  # 30% risk
    current_price = tick.bid
    
    # Assume 2% stop loss for calculation
    stop_distance = current_price * 0.02
    position_size = risk_amount / stop_distance
    
    print(f"[INFO] Test calculation for 30% risk:")
    print(f"[INFO]   Account balance: ${balance:,.2f}")
    print(f"[INFO]   Risk amount (30%): ${risk_amount:,.2f}")
    print(f"[INFO]   Current BTC price: ${current_price:,.2f}")
    print(f"[INFO]   Calculated position size: {position_size:.4f}")
    
    mt5.shutdown()
    return True

def start_btc_monitoring():
    """Start BTC price monitoring."""
    print("\n[INFO] Starting BTC monitoring mode...")
    print("[INFO] Press Ctrl+C to stop")
    
    try:
        import MetaTrader5 as mt5
        
        if not mt5.initialize():
            print("[ERROR] Cannot initialize MT5")
            return
        
        print("[INFO] BTC monitoring started")
        
        for i in range(10):  # Monitor for 10 cycles
            try:
                # Get current price
                tick = mt5.symbol_info_tick("BTCUSDm")
                if tick:
                    timestamp = datetime.now().strftime("%H:%M:%S")
                    print(f"[{timestamp}] BTCUSDm: ${tick.bid:,.2f}")
                else:
                    print("[WARNING] Cannot get BTCUSDm price")
                
                # Get account info
                account_info = mt5.account_info()
                if account_info:
                    print(f"[{timestamp}] Balance: ${account_info.balance:,.2f}")
                
                # Check for open positions
                positions = mt5.positions_get(symbol="BTCUSDm")
                if positions:
                    print(f"[{timestamp}] Open BTC positions: {len(positions)}")
                    for pos in positions:
                        profit = pos.profit
                        print(f"[{timestamp}]   Position profit: ${profit:,.2f}")
                else:
                    print(f"[{timestamp}] No open BTC positions")
                
                print("-" * 50)
                time.sleep(30)  # Wait 30 seconds
                
            except KeyboardInterrupt:
                print("\n[INFO] Monitoring stopped by user")
                break
            except Exception as e:
                print(f"[ERROR] Error in monitoring: {e}")
                time.sleep(10)
        
        mt5.shutdown()
        print("[INFO] BTC monitoring completed")
        
    except Exception as e:
        print(f"[ERROR] Error starting monitoring: {e}")

def show_trading_instructions():
    """Show instructions for live trading."""
    print("\n" + "="*60)
    print("BTC AUTO-TRADING INSTRUCTIONS")
    print("="*60)
    
    print("CURRENT CONFIGURATION:")
    print("  Symbol: BTCUSDm ONLY")
    print("  Risk per trade: 30% (EXTREME RISK!)")
    print("  Max daily loss: 50%")
    print("  Max drawdown: 70%")
    print("  Trailing stops: 5% distance")
    
    print("\nTO START LIVE AUTO-TRADING:")
    print("  1. Ensure MT5 is running and logged in")
    print("  2. Enable auto-trading in MT5 settings")
    print("  3. Run: python start_trading_bot.py")
    print("  4. Monitor dashboard: http://localhost:8000")
    
    print("\nSAFETY WARNINGS:")
    print("  - 30% risk can wipe out account in 3-4 losing trades")
    print("  - Start with demo account or very small real account")
    print("  - Monitor all trades closely")
    print("  - Be prepared for rapid account changes")
    
    print("\nEMERGENCY STOP:")
    print("  - Press Ctrl+C to stop the bot")
    print("  - Or disable auto-trading in MT5")
    print("  - Bot will stop placing new trades")

def main():
    """Main function."""
    try:
        # Test MT5 connection and BTC symbol
        if test_mt5_connection():
            print("\n[SUCCESS] BTC trading setup is ready!")
            
            # Ask user what to do next
            print("\nOptions:")
            print("1. Start BTC price monitoring (demo)")
            print("2. Show live trading instructions")
            print("3. Exit")
            
            try:
                choice = input("\nEnter choice (1-3): ").strip()
                
                if choice == "1":
                    start_btc_monitoring()
                elif choice == "2":
                    show_trading_instructions()
                else:
                    print("[INFO] Exiting...")
            except:
                print("\n[INFO] Exiting...")
        else:
            print("\n[ERROR] BTC trading setup failed")
            print("[INFO] Please fix the issues above")
        
    except KeyboardInterrupt:
        print("\n[INFO] Interrupted by user")
    except Exception as e:
        print(f"[ERROR] Unexpected error: {e}")

if __name__ == "__main__":
    main()
