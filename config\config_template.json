{"trading_bot": {"name": "AI Trading Bot", "version": "1.0.0", "environment": "production", "debug": false}, "mt5": {"server": "YourBroker-Live", "login": 12345678, "password": "your_password", "path": "C:\\Program Files\\MetaTrader 5\\terminal64.exe", "timeout": 60000, "symbols": ["EURUSD", "GBPUSD", "USDJPY", "EURJPY", "GBPJPY"], "timeframes": {"M1": 1, "M5": 5, "M15": 15, "M30": 30, "H1": 60, "H4": 240, "D1": 1440}}, "data": {"sources": {"primary": "mt5", "backup": "csv", "external_api": false}, "storage": {"raw_data_path": "data/raw", "processed_data_path": "data/processed", "backup_path": "data/backup"}, "collection": {"auto_update": true, "update_interval": 300, "history_days": 365, "max_bars": 10000}}, "features": {"technical_indicators": {"sma_periods": [5, 10, 20, 50, 200], "ema_periods": [12, 26, 50], "rsi_period": 14, "macd_fast": 12, "macd_slow": 26, "macd_signal": 9, "bollinger_period": 20, "bollinger_std": 2, "atr_period": 14, "stochastic_k": 14, "stochastic_d": 3}, "statistical_features": {"returns_periods": [1, 5, 10, 20], "volatility_periods": [10, 20, 50], "momentum_periods": [5, 10, 20], "zscore_periods": [20, 50], "percentile_periods": [20, 50]}, "lagged_features": {"price_lags": [1, 2, 3, 5, 10], "volume_lags": [1, 2, 3], "indicator_lags": [1, 2]}}, "models": {"linear_regression": {"enabled": true, "regularization": "ridge", "alpha": 1.0, "fit_intercept": true}, "random_forest": {"enabled": true, "n_estimators": 100, "max_depth": 10, "min_samples_split": 5, "min_samples_leaf": 2, "random_state": 42}, "xgboost": {"enabled": true, "n_estimators": 100, "max_depth": 6, "learning_rate": 0.1, "subsample": 0.8, "colsample_bytree": 0.8, "random_state": 42}, "lstm": {"enabled": true, "units": [50, 50], "dropout": 0.2, "epochs": 100, "batch_size": 32, "sequence_length": 60, "validation_split": 0.2, "early_stopping_patience": 10}, "transformer": {"enabled": false, "d_model": 64, "nhead": 8, "num_layers": 6, "epochs": 100, "batch_size": 32, "sequence_length": 60}, "ensemble": {"enabled": true, "voting": "soft", "weights": "auto"}}, "training": {"data_split": {"train_ratio": 0.7, "validation_ratio": 0.15, "test_ratio": 0.15}, "target": {"column": "close", "prediction_horizon": 1, "task_type": "regression"}, "cross_validation": {"enabled": true, "folds": 5, "time_series_split": true}, "hyperparameter_tuning": {"enabled": false, "method": "grid_search", "cv_folds": 3}}, "risk_management": {"position_sizing": {"method": "fixed_percentage", "risk_per_trade": 0.01, "max_position_size": 0.05, "min_position_size": 0.001, "volatility_adjustment": true}, "stop_loss": {"method": "atr_based", "atr_multiplier": 2.0, "fixed_percentage": 0.02, "max_stop_loss": 0.03, "trailing_stop": false}, "take_profit": {"method": "risk_reward_ratio", "risk_reward_ratio": 2.0, "fixed_percentage": 0.04, "trailing_profit": false}, "portfolio_limits": {"max_daily_loss": 0.02, "max_drawdown": 0.1, "max_positions": 5, "max_exposure_per_symbol": 0.15, "max_correlation": 0.7}, "volatility": {"lookback_period": 20, "max_volatility": 0.05, "volatility_adjustment": true}}, "backtesting": {"initial_capital": 10000.0, "commission": 0.001, "slippage": 0.0001, "start_date": "2020-01-01", "end_date": "2023-12-31", "benchmark": "buy_and_hold", "rebalance_frequency": "daily"}, "trading": {"mode": "live", "auto_trading": false, "max_trades_per_day": 10, "trading_hours": {"start": "00:00", "end": "23:59", "timezone": "UTC"}, "signal_generation": {"confidence_threshold": 0.6, "min_signal_strength": 0.5, "signal_aggregation": "ensemble"}, "execution": {"order_type": "market", "execution_delay": 1, "retry_attempts": 3, "timeout": 30}}, "monitoring": {"dashboard": {"enabled": true, "refresh_interval": 60, "port": 8080}, "alerts": {"enabled": true, "thresholds": {"max_drawdown": 0.08, "daily_loss": 0.03, "win_rate_min": 0.45, "system_cpu": 80, "system_memory": 85, "connection_timeout": 30}, "notification_methods": {"email": {"enabled": false, "smtp_server": "smtp.gmail.com", "smtp_port": 587, "username": "<EMAIL>", "password": "your_app_password", "recipients": ["<EMAIL>"]}, "webhook": {"enabled": false, "url": "https://hooks.slack.com/your-webhook-url"}}}, "logging": {"level": "INFO", "file_path": "logs/trading_bot.log", "max_file_size": "10MB", "backup_count": 5, "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"}, "performance_tracking": {"enabled": true, "metrics_interval": 300, "save_to_database": true, "export_reports": true}}, "api": {"enabled": true, "host": "0.0.0.0", "port": 8000, "workers": 1, "cors": {"enabled": true, "origins": ["*"]}, "authentication": {"enabled": false, "api_key": "your_api_key_here"}, "rate_limiting": {"enabled": true, "requests_per_minute": 60}}, "database": {"type": "sqlite", "path": "data/trading_bot.db", "backup": {"enabled": true, "interval": "daily", "retention_days": 30}}, "experiment_tracking": {"enabled": true, "database_path": "data/experiments.db", "auto_log": true, "save_artifacts": true, "artifacts_path": "artifacts"}, "security": {"encrypt_credentials": true, "secure_api": true, "audit_logging": true, "backup_encryption": false}, "performance": {"multiprocessing": false, "max_workers": 4, "cache_enabled": true, "cache_size": 1000, "optimization_level": "standard"}}