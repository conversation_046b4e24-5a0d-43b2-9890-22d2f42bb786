{"curl_examples": {"health_check": "curl -X GET http://localhost:8000/health", "connect_mt5": "curl -X POST http://localhost:8000/mt5/connect", "get_price": "curl -X GET http://localhost:8000/mt5/prices/EURUSD", "place_order": "curl -X POST http://localhost:8000/trading/order -H \"Content-Type: application/json\" -d '{\"symbol\": \"EURUSD\", \"order_type\": \"buy\", \"volume\": 0.01}'", "get_positions": "curl -X GET http://localhost:8000/trading/positions", "start_trading": "curl -X POST http://localhost:8000/trading/start"}, "python_examples": {"health_check": "requests.get('http://localhost:8000/health')", "place_order": "requests.post('http://localhost:8000/trading/order', json={'symbol': 'EURUSD', 'order_type': 'buy', 'volume': 0.01})", "get_prediction": "requests.post('http://localhost:8000/models/predict', json={'symbol': 'EURUSD', 'horizon': 1})"}}