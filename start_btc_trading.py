#!/usr/bin/env python3
"""
AI Trading Bot - BTC Auto-Trading Startup (Simplified)
"""

import sys
import os
import json
import asyncio
import signal
import logging
from pathlib import Path
from datetime import datetime
from typing import Optional

# Add project root to path
project_root = Path(__file__).parent
sys.path.append(str(project_root / "src"))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/btc_trading.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


class BTCTradingBot:
    """
    Simplified BTC trading bot without API dependencies.
    """
    
    def __init__(self):
        """Initialize the BTC trading bot."""
        self.running = False
        self.mt5_connector = None
        self.config = self.get_btc_config()
        
    def get_btc_config(self) -> dict:
        """Get BTC-specific trading configuration."""
        return {
            "trading": {
                "auto_trading_enabled": True,
                "symbols": ["BTCUSDm"],
                "timeframe": "M15",
                "signal_interval": 300,  # 5 minutes
                "min_signal_strength": 0.6,
                "max_trades_per_day": 20
            },
            "risk_management": {
                "position_sizing": {"risk_per_trade": 0.30},  # 30% risk!
                "portfolio_limits": {
                    "max_daily_loss": 0.50,
                    "max_drawdown": 0.70,
                    "max_positions": 3
                },
                "take_profit": {
                    "trailing_percentage": 0.05,
                    "trailing_activation": 0.02
                }
            }
        }
    
    def setup_signal_handlers(self):
        """Set up signal handlers for graceful shutdown."""
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, shutting down...")
            self.shutdown()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def connect_mt5(self) -> bool:
        """Connect to MetaTrader 5."""
        try:
            # Import MT5 connector
            from trading.mt5_connector import MT5Connector
            
            logger.info("📡 Connecting to MetaTrader 5...")
            self.mt5_connector = MT5Connector()
            
            if not self.mt5_connector.connect():
                logger.error("❌ Failed to connect to MT5")
                return False
            
            logger.info("✅ Connected to MT5 successfully!")
            
            # Enable auto-trading
            if not self.mt5_connector.enable_auto_trading():
                logger.error("❌ Failed to enable auto-trading")
                return False
            
            logger.info("✅ Auto-trading enabled!")
            
            # Get account info
            account_info = self.mt5_connector.get_account_info()
            if account_info:
                logger.info(f"💰 Account: {account_info.get('login', 'Unknown')}")
                logger.info(f"💰 Balance: ${account_info.get('balance', 0):,.2f}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Error connecting to MT5: {e}")
            return False
    
    def check_btc_symbol(self) -> bool:
        """Check if BTCUSDm symbol is available."""
        try:
            prices = self.mt5_connector.get_current_prices(['BTCUSDm'])
            if 'BTCUSDm' in prices:
                current_price = prices['BTCUSDm']['bid']
                logger.info(f"📈 BTCUSDm current price: ${current_price:,.2f}")
                return True
            else:
                logger.error("❌ BTCUSDm symbol not available")
                logger.info("💡 Make sure BTCUSDm is available in your MT5 account")
                return False
        except Exception as e:
            logger.error(f"❌ Error checking BTCUSDm: {e}")
            return False
    
    async def simple_trading_loop(self):
        """Simple trading loop for BTC."""
        logger.info("🔄 Starting BTC trading loop...")
        
        trade_count = 0
        max_trades = self.config['trading']['max_trades_per_day']
        
        while self.running and trade_count < max_trades:
            try:
                # Get current BTC price
                prices = self.mt5_connector.get_current_prices(['BTCUSDm'])
                if 'BTCUSDm' not in prices:
                    logger.warning("⚠️ Cannot get BTCUSDm price")
                    await asyncio.sleep(60)
                    continue
                
                current_price = prices['BTCUSDm']['bid']
                logger.info(f"📊 BTCUSDm: ${current_price:,.2f}")
                
                # Simple trading logic (placeholder)
                # In a real implementation, this would use AI models
                
                # Get account info
                account_info = self.mt5_connector.get_account_info()
                if not account_info:
                    logger.warning("⚠️ Cannot get account info")
                    await asyncio.sleep(60)
                    continue
                
                # Check if we have open positions
                positions = self.mt5_connector.get_positions()
                btc_positions = [pos for pos in positions if pos['symbol'] == 'BTCUSDm']
                
                logger.info(f"📊 Account Balance: ${account_info['balance']:,.2f}")
                logger.info(f"📊 Open BTC Positions: {len(btc_positions)}")
                
                # Simple demo trade logic (very basic)
                if len(btc_positions) == 0 and trade_count < max_trades:
                    # This is where AI would make the decision
                    # For now, just log that we're monitoring
                    logger.info("🤖 AI monitoring market conditions...")
                    logger.info("💡 In live mode, AI would analyze and potentially place trades")
                
                # Update trailing stops for existing positions
                if btc_positions:
                    logger.info("📈 Updating trailing stops...")
                    # This would update trailing stops in real implementation
                
                trade_count += 1
                
                # Wait for next check
                await asyncio.sleep(self.config['trading']['signal_interval'])
                
            except Exception as e:
                logger.error(f"❌ Error in trading loop: {e}")
                await asyncio.sleep(60)
        
        logger.info(f"🛑 Trading loop completed. Total checks: {trade_count}")
    
    async def start(self):
        """Start the BTC trading bot."""
        logger.info("="*80)
        logger.info("🚀 AI TRADING BOT - BTC AUTO-TRADING MODE")
        logger.info("="*80)
        logger.info("🎯 TRADING SYMBOL: BTCUSDm ONLY")
        logger.info("⚠️ HIGH RISK CONFIGURATION:")
        logger.info("   • 30% risk per trade")
        logger.info("   • 50% max daily loss")
        logger.info("   • 70% max drawdown")
        logger.info("   • Automatic trading enabled")
        logger.info("="*80)
        
        try:
            # Set up signal handlers
            self.setup_signal_handlers()
            
            # Connect to MT5
            if not self.connect_mt5():
                logger.error("❌ Cannot start trading without MT5 connection")
                return
            
            # Check BTC symbol
            if not self.check_btc_symbol():
                logger.error("❌ Cannot trade BTCUSDm - symbol not available")
                return
            
            # Set running flag
            self.running = True
            
            logger.info("✅ BTC Auto-Trading Started!")
            logger.info("🎯 Monitoring BTCUSDm for trading opportunities...")
            logger.info("🛑 Press Ctrl+C to stop trading")
            
            # Start trading loop
            await self.simple_trading_loop()
                
        except KeyboardInterrupt:
            logger.info("🛑 Shutdown requested by user")
            self.shutdown()
        except Exception as e:
            logger.error(f"❌ Error starting BTC trading: {e}")
            self.shutdown()
            raise
    
    def shutdown(self):
        """Gracefully shutdown the trading bot."""
        logger.info("🛑 Shutting down BTC Trading Bot...")
        
        self.running = False
        
        try:
            # Disconnect from MT5
            if self.mt5_connector and self.mt5_connector.connected:
                self.mt5_connector.disconnect()
                logger.info("✅ Disconnected from MT5")
            
            logger.info("✅ BTC Trading Bot shutdown complete")
            
        except Exception as e:
            logger.error(f"❌ Error during shutdown: {e}")


def main():
    """Main entry point."""
    try:
        # Show startup message
        print("🚀 AI Trading Bot - BTC Auto-Trading")
        print("="*60)
        print("🎯 Trading Symbol: BTCUSDm ONLY")
        print("⚠️ WARNING: 30% RISK PER TRADE!")
        print("="*60)
        
        # Create and start BTC trading bot
        btc_bot = BTCTradingBot()
        
        # Run the bot
        asyncio.run(btc_bot.start())
        
    except KeyboardInterrupt:
        logger.info("🛑 Shutdown requested")
    except Exception as e:
        logger.error(f"❌ Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
