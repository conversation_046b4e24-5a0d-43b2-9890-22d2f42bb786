# The Ultimate AI-Driven Trading Bot for MetaTrader 5

An advanced AI-powered trading bot that leverages machine learning, deep learning, and reinforcement learning to execute automated trading strategies on MetaTrader 5.

## Features

- **Multi-Model AI Framework**: Supports supervised learning, deep learning (LSTM, Transformers), and reinforcement learning (DQN)
- **Comprehensive Technical Analysis**: 50+ technical indicators and custom feature engineering
- **Advanced Backtesting**: Walk-forward validation with multiple performance metrics
- **Risk Management**: Position sizing, stop-loss/take-profit, and portfolio diversification
- **Real-time Trading**: Live integration with MetaTrader 5 API
- **Experiment Tracking**: MLflow integration for model versioning and performance tracking
- **Multi-Asset Support**: Forex, commodities, and cryptocurrency trading

## Project Structure

```
├── data/                   # Data storage and processing
│   ├── raw/               # Raw CSV files
│   ├── processed/         # Cleaned and feature-engineered data
│   └── external/          # External data sources
├── src/                   # Source code
│   ├── data/              # Data processing modules
│   ├── features/          # Feature engineering
│   ├── models/            # ML/DL model implementations
│   ├── backtesting/       # Backtesting framework
│   ├── trading/           # Live trading logic
│   ├── risk/              # Risk management
│   └── api/               # FastAPI endpoints
├── notebooks/             # Jupyter notebooks for analysis
├── tests/                 # Unit and integration tests
├── configs/               # Configuration files
├── scripts/               # Utility scripts
└── docs/                  # Documentation
```

## Quick Start

1. **Environment Setup**:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements.txt
   ```

2. **Data Preparation**:
   ```bash
   python scripts/prepare_data.py --input data/raw/ --output data/processed/
   ```

3. **Model Training**:
   ```bash
   python scripts/train_models.py --config configs/model_config.yaml
   ```

4. **Backtesting**:
   ```bash
   python scripts/backtest.py --strategy lstm_strategy --data data/processed/EURUSD.csv
   ```

5. **Live Trading** (Demo Mode):
   ```bash
   python scripts/live_trading.py --demo --config configs/trading_config.yaml
   ```

## Configuration

All configurations are stored in the `configs/` directory:
- `model_config.yaml`: Model hyperparameters and training settings
- `trading_config.yaml`: Trading parameters and risk management
- `data_config.yaml`: Data sources and preprocessing settings

## Safety and Risk Management

⚠️ **Important**: This bot is for educational and research purposes. Always:
- Test thoroughly in demo mode before live trading
- Start with small position sizes
- Monitor performance continuously
- Understand the risks involved in automated trading

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Disclaimer

Trading involves substantial risk of loss and is not suitable for all investors. Past performance is not indicative of future results.
