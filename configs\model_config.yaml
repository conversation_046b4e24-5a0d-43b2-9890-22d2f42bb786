# Model Configuration for AI Trading Bot

# Data Configuration
data:
  train_split: 0.7
  validation_split: 0.15
  test_split: 0.15
  sequence_length: 60  # For LSTM models
  target_column: "close"
  feature_columns:
    - "open"
    - "high" 
    - "low"
    - "close"
    - "volume"
  
# Feature Engineering
features:
  technical_indicators:
    sma_periods: [10, 20, 50, 200]
    ema_periods: [12, 26, 50]
    rsi_period: 14
    macd_fast: 12
    macd_slow: 26
    macd_signal: 9
    bollinger_period: 20
    bollinger_std: 2
    atr_period: 14
    stochastic_k: 14
    stochastic_d: 3
  
  rolling_features:
    volatility_window: 20
    returns_window: [1, 5, 10]
    volume_sma_window: 20
  
  lagged_features:
    price_lags: [1, 2, 3, 5, 10]
    volume_lags: [1, 2, 3]

# Model Configurations
models:
  linear_regression:
    enabled: true
    regularization: "ridge"
    alpha: 1.0
    
  random_forest:
    enabled: true
    n_estimators: 100
    max_depth: 10
    min_samples_split: 5
    min_samples_leaf: 2
    random_state: 42
    
  xgboost:
    enabled: true
    n_estimators: 100
    max_depth: 6
    learning_rate: 0.1
    subsample: 0.8
    colsample_bytree: 0.8
    random_state: 42
    
  lstm:
    enabled: true
    units: [50, 50]
    dropout: 0.2
    recurrent_dropout: 0.2
    batch_size: 32
    epochs: 100
    patience: 10
    learning_rate: 0.001
    
  transformer:
    enabled: false  # Enable when ready
    d_model: 64
    nhead: 8
    num_layers: 6
    dropout: 0.1
    
  dqn:
    enabled: false  # Enable for RL training
    state_size: 50
    action_size: 3  # buy, sell, hold
    memory_size: 10000
    batch_size: 32
    learning_rate: 0.001
    gamma: 0.95
    epsilon_start: 1.0
    epsilon_end: 0.01
    epsilon_decay: 0.995

# Training Configuration
training:
  cross_validation:
    enabled: true
    n_splits: 5
    shuffle: false  # Time series data
    
  hyperparameter_tuning:
    enabled: false
    method: "grid_search"  # or "random_search", "bayesian"
    n_trials: 50
    
  early_stopping:
    enabled: true
    patience: 10
    min_delta: 0.001
    
  model_selection:
    metric: "sharpe_ratio"  # or "returns", "max_drawdown"
    
# MLflow Configuration
mlflow:
  experiment_name: "trading_bot_experiments"
  tracking_uri: "sqlite:///mlflow.db"
  artifact_location: "./mlruns"
