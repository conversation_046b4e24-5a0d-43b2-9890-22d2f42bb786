# Trading Configuration for AI Trading Bot

# MetaTrader 5 Configuration
mt5:
  server: "MetaQuotes-Demo"  # Demo server
  login: 0  # Replace with actual login
  password: ""  # Replace with actual password
  path: ""  # Path to MT5 terminal (auto-detect if empty)
  timeout: 60000
  
# Trading Parameters
trading:
  mode: "demo"  # "demo" or "live"
  symbols:
    - "EURUSD"
    - "GBPUSD" 
    - "USDJPY"
    - "AUDUSD"
    - "USDCHF"
    - "USDCAD"
    - "NZDUSD"
  
  timeframes:
    primary: "M15"  # 15-minute charts
    secondary: "H1"  # 1-hour for confirmation
    
  trading_hours:
    start: "00:00"
    end: "23:59"
    timezone: "UTC"
    
  max_positions: 5  # Maximum concurrent positions
  max_daily_trades: 20
  
# Risk Management
risk_management:
  account_risk_per_trade: 0.02  # 2% of account per trade
  max_account_risk: 0.10  # 10% total account risk
  
  position_sizing:
    method: "kelly_criterion"  # "fixed_percent", "kelly_criterion", "volatility_adjusted"
    min_position_size: 0.01
    max_position_size: 1.0
    
  stop_loss:
    method: "atr"  # "fixed_pips", "atr", "percentage"
    atr_multiplier: 2.0
    fixed_pips: 50
    percentage: 0.02
    
  take_profit:
    method: "risk_reward"  # "fixed_pips", "atr", "risk_reward"
    risk_reward_ratio: 2.0
    atr_multiplier: 3.0
    fixed_pips: 100
    
  trailing_stop:
    enabled: true
    method: "atr"
    atr_multiplier: 1.5
    fixed_pips: 30
    
# Signal Generation
signals:
  confidence_threshold: 0.7  # Minimum confidence for trade execution
  
  filters:
    trend_filter: true
    volatility_filter: true
    news_filter: false  # Disable during news events
    
  confirmation:
    require_multiple_models: true
    min_models_agreement: 2
    
# Portfolio Management
portfolio:
  diversification:
    max_correlation: 0.7
    max_positions_per_currency: 2
    
  rebalancing:
    frequency: "daily"
    method: "equal_weight"  # "equal_weight", "risk_parity", "momentum"
    
# Performance Monitoring
monitoring:
  performance_metrics:
    - "total_return"
    - "sharpe_ratio"
    - "max_drawdown"
    - "win_rate"
    - "profit_factor"
    - "calmar_ratio"
    
  alerts:
    max_drawdown_threshold: 0.15  # 15%
    daily_loss_threshold: 0.05   # 5%
    
  reporting:
    frequency: "daily"
    email_reports: false
    
# API Configuration
api:
  host: "localhost"
  port: 8000
  reload: true
  log_level: "info"
  
# Logging
logging:
  level: "INFO"
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
  rotation: "1 day"
  retention: "30 days"
  
# Database (Optional)
database:
  enabled: false
  url: "postgresql://user:password@localhost/trading_bot"
  
# External Data Sources
external_data:
  news_api:
    enabled: false
    api_key: ""
    
  economic_calendar:
    enabled: false
    api_key: ""
