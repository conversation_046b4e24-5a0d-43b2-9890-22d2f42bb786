"""
Feature engineering module that combines technical indicators and statistical features.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Union, Any
import yaml
from pathlib import Path

from .technical_indicators import TechnicalIndicators
from .statistical_features import StatisticalFeatures


class FeatureEngineer:
    """
    Main feature engineering class that creates comprehensive features for trading models.
    """
    
    def __init__(self, config_path: str = "configs/model_config.yaml"):
        """
        Initialize FeatureEngineer with configuration.
        
        Args:
            config_path: Path to model configuration file
        """
        self.config = self._load_config(config_path)
        self.feature_names = []
        
    def _load_config(self, config_path: str) -> Dict:
        """Load configuration from YAML file."""
        try:
            with open(config_path, 'r') as file:
                return yaml.safe_load(file)
        except FileNotFoundError:
            print(f"Config file {config_path} not found. Using default settings.")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict:
        """Return default configuration."""
        return {
            'features': {
                'technical_indicators': {
                    'sma_periods': [10, 20, 50, 200],
                    'ema_periods': [12, 26, 50],
                    'rsi_period': 14,
                    'macd_fast': 12,
                    'macd_slow': 26,
                    'macd_signal': 9,
                    'bollinger_period': 20,
                    'bollinger_std': 2,
                    'atr_period': 14,
                    'stochastic_k': 14,
                    'stochastic_d': 3
                },
                'rolling_features': {
                    'volatility_window': 20,
                    'returns_window': [1, 5, 10],
                    'volume_sma_window': 20
                },
                'lagged_features': {
                    'price_lags': [1, 2, 3, 5, 10],
                    'volume_lags': [1, 2, 3]
                }
            }
        }
    
    def create_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Create technical indicators.
        
        Args:
            df: Input DataFrame with OHLCV data
            
        Returns:
            DataFrame with technical indicators added
        """
        df_features = df.copy()
        
        # Get configuration
        tech_config = self.config.get('features', {}).get('technical_indicators', {})
        
        # Simple Moving Averages
        sma_periods = tech_config.get('sma_periods', [10, 20, 50, 200])
        for period in sma_periods:
            df_features[f'sma_{period}'] = TechnicalIndicators.sma(df['close'], period)
            self.feature_names.append(f'sma_{period}')
        
        # Exponential Moving Averages
        ema_periods = tech_config.get('ema_periods', [12, 26, 50])
        for period in ema_periods:
            df_features[f'ema_{period}'] = TechnicalIndicators.ema(df['close'], period)
            self.feature_names.append(f'ema_{period}')
        
        # RSI
        rsi_period = tech_config.get('rsi_period', 14)
        df_features['rsi'] = TechnicalIndicators.rsi(df['close'], rsi_period)
        self.feature_names.append('rsi')
        
        # MACD
        macd_fast = tech_config.get('macd_fast', 12)
        macd_slow = tech_config.get('macd_slow', 26)
        macd_signal = tech_config.get('macd_signal', 9)
        
        macd_data = TechnicalIndicators.macd(df['close'], macd_fast, macd_slow, macd_signal)
        df_features['macd'] = macd_data['macd']
        df_features['macd_signal'] = macd_data['signal']
        df_features['macd_histogram'] = macd_data['histogram']
        self.feature_names.extend(['macd', 'macd_signal', 'macd_histogram'])
        
        # Bollinger Bands
        bb_period = tech_config.get('bollinger_period', 20)
        bb_std = tech_config.get('bollinger_std', 2)
        
        bb_data = TechnicalIndicators.bollinger_bands(df['close'], bb_period, bb_std)
        df_features['bb_upper'] = bb_data['upper']
        df_features['bb_middle'] = bb_data['middle']
        df_features['bb_lower'] = bb_data['lower']
        df_features['bb_width'] = bb_data['width']
        df_features['bb_percent_b'] = bb_data['percent_b']
        self.feature_names.extend(['bb_upper', 'bb_middle', 'bb_lower', 'bb_width', 'bb_percent_b'])
        
        # ATR
        if all(col in df.columns for col in ['high', 'low', 'close']):
            atr_period = tech_config.get('atr_period', 14)
            df_features['atr'] = TechnicalIndicators.atr(df['high'], df['low'], df['close'], atr_period)
            self.feature_names.append('atr')
        
        # Stochastic
        if all(col in df.columns for col in ['high', 'low', 'close']):
            stoch_k = tech_config.get('stochastic_k', 14)
            stoch_d = tech_config.get('stochastic_d', 3)
            
            stoch_data = TechnicalIndicators.stochastic(df['high'], df['low'], df['close'], stoch_k, stoch_d)
            df_features['stoch_k'] = stoch_data['k']
            df_features['stoch_d'] = stoch_data['d']
            self.feature_names.extend(['stoch_k', 'stoch_d'])
        
        # Williams %R
        if all(col in df.columns for col in ['high', 'low', 'close']):
            df_features['williams_r'] = TechnicalIndicators.williams_r(df['high'], df['low'], df['close'])
            self.feature_names.append('williams_r')
        
        # Rate of Change
        df_features['roc'] = TechnicalIndicators.roc(df['close'])
        self.feature_names.append('roc')
        
        # ADX
        if all(col in df.columns for col in ['high', 'low', 'close']):
            adx_data = TechnicalIndicators.adx(df['high'], df['low'], df['close'])
            df_features['adx'] = adx_data['adx']
            df_features['di_plus'] = adx_data['di_plus']
            df_features['di_minus'] = adx_data['di_minus']
            self.feature_names.extend(['adx', 'di_plus', 'di_minus'])
        
        # Volume indicators (if volume available)
        if 'volume' in df.columns:
            # OBV
            df_features['obv'] = TechnicalIndicators.obv(df['close'], df['volume'])
            self.feature_names.append('obv')
            
            # VWAP
            if all(col in df.columns for col in ['high', 'low', 'close', 'volume']):
                df_features['vwap'] = TechnicalIndicators.vwap(df['high'], df['low'], df['close'], df['volume'])
                self.feature_names.append('vwap')
            
            # Volume SMA
            vol_sma_window = self.config.get('features', {}).get('rolling_features', {}).get('volume_sma_window', 20)
            df_features['volume_sma'] = TechnicalIndicators.sma(df['volume'], vol_sma_window)
            df_features['volume_ratio'] = df['volume'] / df_features['volume_sma']
            self.feature_names.extend(['volume_sma', 'volume_ratio'])
        
        return df_features
    
    def create_statistical_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Create statistical features.
        
        Args:
            df: Input DataFrame
            
        Returns:
            DataFrame with statistical features added
        """
        df_features = df.copy()
        
        # Get configuration
        rolling_config = self.config.get('features', {}).get('rolling_features', {})
        
        # Returns
        returns_windows = rolling_config.get('returns_window', [1, 5, 10])
        for window in returns_windows:
            df_features[f'returns_{window}'] = StatisticalFeatures.returns(df['close'], window)
            df_features[f'log_returns_{window}'] = StatisticalFeatures.log_returns(df['close'], window)
            self.feature_names.extend([f'returns_{window}', f'log_returns_{window}'])
        
        # Volatility
        vol_window = rolling_config.get('volatility_window', 20)
        df_features['volatility'] = StatisticalFeatures.volatility(df['close'], vol_window)
        self.feature_names.append('volatility')
        
        # Rolling statistics
        for window in [10, 20, 50]:
            df_features[f'rolling_mean_{window}'] = StatisticalFeatures.rolling_mean(df['close'], window)
            df_features[f'rolling_std_{window}'] = StatisticalFeatures.rolling_std(df['close'], window)
            df_features[f'z_score_{window}'] = StatisticalFeatures.z_score(df['close'], window)
            
            self.feature_names.extend([
                f'rolling_mean_{window}', f'rolling_std_{window}', f'z_score_{window}'
            ])
        
        # Momentum features
        for window in [5, 10, 20]:
            df_features[f'momentum_{window}'] = StatisticalFeatures.momentum(df['close'], window)
            self.feature_names.append(f'momentum_{window}')
        
        # Percentile rank
        df_features['percentile_rank_20'] = StatisticalFeatures.percentile_rank(df['close'], 20)
        self.feature_names.append('percentile_rank_20')
        
        return df_features
    
    def create_lagged_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Create lagged features.
        
        Args:
            df: Input DataFrame
            
        Returns:
            DataFrame with lagged features added
        """
        df_features = df.copy()
        
        # Get configuration
        lag_config = self.config.get('features', {}).get('lagged_features', {})
        
        # Price lags
        price_lags = lag_config.get('price_lags', [1, 2, 3, 5, 10])
        for lag in price_lags:
            df_features[f'close_lag_{lag}'] = df['close'].shift(lag)
            df_features[f'open_lag_{lag}'] = df['open'].shift(lag)
            self.feature_names.extend([f'close_lag_{lag}', f'open_lag_{lag}'])
        
        # Volume lags (if available)
        if 'volume' in df.columns:
            volume_lags = lag_config.get('volume_lags', [1, 2, 3])
            for lag in volume_lags:
                df_features[f'volume_lag_{lag}'] = df['volume'].shift(lag)
                self.feature_names.append(f'volume_lag_{lag}')
        
        return df_features
    
    def create_time_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Create time-based features.
        
        Args:
            df: Input DataFrame with timestamp column
            
        Returns:
            DataFrame with time features added
        """
        df_features = df.copy()
        
        if 'timestamp' in df.columns:
            # Extract time components
            df_features['hour'] = df['timestamp'].dt.hour
            df_features['day_of_week'] = df['timestamp'].dt.dayofweek
            df_features['month'] = df['timestamp'].dt.month
            df_features['quarter'] = df['timestamp'].dt.quarter
            
            # Cyclical encoding
            df_features['hour_sin'] = np.sin(2 * np.pi * df_features['hour'] / 24)
            df_features['hour_cos'] = np.cos(2 * np.pi * df_features['hour'] / 24)
            df_features['day_sin'] = np.sin(2 * np.pi * df_features['day_of_week'] / 7)
            df_features['day_cos'] = np.cos(2 * np.pi * df_features['day_of_week'] / 7)
            df_features['month_sin'] = np.sin(2 * np.pi * df_features['month'] / 12)
            df_features['month_cos'] = np.cos(2 * np.pi * df_features['month'] / 12)
            
            time_features = [
                'hour', 'day_of_week', 'month', 'quarter',
                'hour_sin', 'hour_cos', 'day_sin', 'day_cos', 'month_sin', 'month_cos'
            ]
            self.feature_names.extend(time_features)
        
        return df_features
    
    def create_all_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Create all features using the complete pipeline.
        
        Args:
            df: Input DataFrame with OHLCV data
            
        Returns:
            DataFrame with all features
        """
        print("Creating technical indicators...")
        df_features = self.create_technical_indicators(df)
        
        print("Creating statistical features...")
        df_features = self.create_statistical_features(df_features)
        
        print("Creating lagged features...")
        df_features = self.create_lagged_features(df_features)
        
        print("Creating time features...")
        df_features = self.create_time_features(df_features)
        
        print(f"Feature engineering completed. Created {len(self.feature_names)} features.")
        
        return df_features
    
    def get_feature_names(self) -> List[str]:
        """Get list of created feature names."""
        return self.feature_names.copy()
    
    def get_feature_importance_data(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Get data for feature importance analysis.
        
        Args:
            df: DataFrame with features
            
        Returns:
            Dictionary with feature statistics
        """
        feature_stats = {}
        
        for feature in self.feature_names:
            if feature in df.columns:
                feature_stats[feature] = {
                    'mean': float(df[feature].mean()),
                    'std': float(df[feature].std()),
                    'min': float(df[feature].min()),
                    'max': float(df[feature].max()),
                    'missing_ratio': float(df[feature].isnull().mean()),
                    'unique_values': int(df[feature].nunique())
                }
        
        return feature_stats
