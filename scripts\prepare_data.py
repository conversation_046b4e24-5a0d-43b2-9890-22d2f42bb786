#!/usr/bin/env python3
"""
Data preparation script for The Ultimate AI Trading Bot.
This script loads, cleans, validates, and processes trading data.
"""

import sys
import argparse
from pathlib import Path
import pandas as pd
from loguru import logger

# Add src to path
sys.path.append(str(Path(__file__).parent.parent))

from src.data.data_loader import DataLoader
from src.data.data_processor import DataProcessor
from src.data.data_validator import DataValidator


def setup_logging(log_level: str = "INFO"):
    """Setup logging configuration."""
    logger.remove()
    logger.add(
        sys.stdout,
        level=log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    )
    
    # Also log to file
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    logger.add(
        log_dir / "data_preparation.log",
        level=log_level,
        rotation="1 day",
        retention="30 days",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}"
    )


def prepare_single_symbol(symbol: str, data_loader: DataLoader, 
                         data_processor: DataProcessor, data_validator: DataValidator,
                         output_dir: Path) -> bool:
    """
    Prepare data for a single symbol.
    
    Args:
        symbol: Currency pair symbol
        data_loader: DataLoader instance
        data_processor: DataProcessor instance
        data_validator: DataValidator instance
        output_dir: Output directory for processed data
        
    Returns:
        True if successful, False otherwise
    """
    try:
        logger.info(f"Processing {symbol}")
        
        # Load data
        df = data_loader.load_currency_pair_data(symbol)
        if df is None:
            logger.warning(f"No data found for {symbol}")
            return False
        
        logger.info(f"Loaded {len(df)} rows for {symbol}")
        
        # Validate raw data
        validation_results = data_validator.validate_dataframe(df, symbol)
        logger.info(f"Validation quality score for {symbol}: {validation_results['quality_score']:.2f}")
        
        if validation_results['quality_score'] < 0.5:
            logger.warning(f"Low quality score for {symbol}. Proceeding with caution.")
        
        # Process data
        df_processed = data_processor.process_pipeline(df, fit_scaler=True)
        
        # Split data
        train_df, val_df, test_df = data_processor.split_data(df_processed)
        
        # Save processed data
        symbol_dir = output_dir / symbol
        symbol_dir.mkdir(exist_ok=True)
        
        # Save in parquet format for efficiency
        train_df.to_parquet(symbol_dir / "train.parquet", index=False)
        val_df.to_parquet(symbol_dir / "validation.parquet", index=False)
        test_df.to_parquet(symbol_dir / "test.parquet", index=False)
        
        # Also save full processed data
        df_processed.to_parquet(symbol_dir / "full_processed.parquet", index=False)
        
        # Save validation report
        with open(symbol_dir / "validation_report.txt", "w") as f:
            f.write(data_validator.generate_validation_report(symbol))
        
        logger.info(f"Successfully processed and saved {symbol}")
        return True
        
    except Exception as e:
        logger.error(f"Error processing {symbol}: {str(e)}")
        return False


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Prepare trading data for AI models")
    parser.add_argument("--input", "-i", type=str, default="data/raw",
                       help="Input directory containing CSV files")
    parser.add_argument("--output", "-o", type=str, default="data/processed",
                       help="Output directory for processed data")
    parser.add_argument("--symbols", "-s", nargs="+", 
                       help="Specific symbols to process (default: all available)")
    parser.add_argument("--log-level", type=str, default="INFO",
                       choices=["DEBUG", "INFO", "WARNING", "ERROR"],
                       help="Logging level")
    parser.add_argument("--config", "-c", type=str, default="configs/data_config.yaml",
                       help="Path to data configuration file")
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.log_level)
    
    logger.info("Starting data preparation process")
    
    # Create output directory
    output_dir = Path(args.output)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    try:
        # Initialize components
        data_loader = DataLoader(args.config)
        data_processor = DataProcessor(args.config)
        data_validator = DataValidator(args.config)
        
        # Get symbols to process
        if args.symbols:
            symbols = args.symbols
            logger.info(f"Processing specified symbols: {symbols}")
        else:
            symbols = data_loader.get_available_symbols()
            logger.info(f"Processing all available symbols: {symbols}")
        
        if not symbols:
            logger.error("No symbols found to process")
            return 1
        
        # Process each symbol
        successful_symbols = []
        failed_symbols = []
        
        for symbol in symbols:
            success = prepare_single_symbol(
                symbol, data_loader, data_processor, data_validator, output_dir
            )
            
            if success:
                successful_symbols.append(symbol)
            else:
                failed_symbols.append(symbol)
        
        # Generate summary report
        logger.info("Generating summary report")
        
        summary = {
            'total_symbols': len(symbols),
            'successful': len(successful_symbols),
            'failed': len(failed_symbols),
            'successful_symbols': successful_symbols,
            'failed_symbols': failed_symbols
        }
        
        # Save summary
        summary_df = pd.DataFrame([summary])
        summary_df.to_json(output_dir / "preparation_summary.json", orient="records", indent=2)
        
        # Generate validation summary
        validation_summary = data_validator.get_validation_summary()
        validation_df = pd.DataFrame([validation_summary])
        validation_df.to_json(output_dir / "validation_summary.json", orient="records", indent=2)
        
        # Generate overall validation report
        with open(output_dir / "overall_validation_report.txt", "w") as f:
            f.write(data_validator.generate_validation_report())
        
        # Print summary
        logger.info(f"Data preparation completed!")
        logger.info(f"Successfully processed: {len(successful_symbols)} symbols")
        logger.info(f"Failed to process: {len(failed_symbols)} symbols")
        
        if failed_symbols:
            logger.warning(f"Failed symbols: {failed_symbols}")
        
        logger.info(f"Processed data saved to: {output_dir}")
        
        return 0 if not failed_symbols else 1
        
    except Exception as e:
        logger.error(f"Error in data preparation: {str(e)}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
