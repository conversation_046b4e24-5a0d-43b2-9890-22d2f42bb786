"""
Risk management system for trading operations.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any, Union
from datetime import datetime, timedelta
import json
from pathlib import Path


class RiskManager:
    """
    Comprehensive risk management system for trading operations.
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """
        Initialize RiskManager.
        
        Args:
            config: Risk management configuration
        """
        self.config = config or self._get_default_config()
        self.portfolio_value = 0.0
        self.positions = {}
        self.daily_pnl = []
        self.risk_metrics = {}
        
    def _get_default_config(self) -> Dict:
        """Get default risk management configuration."""
        return {
            'position_sizing': {
                'method': 'fixed_percentage',  # 'fixed_percentage', 'volatility_based', 'kelly'
                'risk_per_trade': 0.30,  # 30% risk per trade (HIGH RISK!)
                'max_position_size': 0.50,  # 50% max position size
                'min_position_size': 0.001  # 0.1% min position size
            },
            'stop_loss': {
                'method': 'atr_based',  # 'fixed_percentage', 'atr_based', 'volatility_based'
                'atr_multiplier': 2.0,
                'fixed_percentage': 0.02,  # 2% stop loss
                'max_stop_loss': 0.05  # 5% maximum stop loss
            },
            'take_profit': {
                'method': 'trailing',  # 'fixed_percentage', 'risk_reward_ratio', 'trailing'
                'risk_reward_ratio': 2.0,  # 2:1 reward to risk
                'fixed_percentage': 0.04,  # 4% take profit
                'trailing_percentage': 0.05,  # 5% trailing stop
                'trailing_activation': 0.02  # Activate trailing after 2% profit
            },
            'portfolio_limits': {
                'max_daily_loss': 0.50,  # 50% max daily loss (EXTREME RISK!)
                'max_drawdown': 0.70,  # 70% max drawdown (EXTREME RISK!)
                'max_correlation': 0.9,  # Maximum correlation between positions
                'max_positions': 5,  # Maximum number of open positions
                'max_exposure_per_symbol': 0.5  # 50% max exposure per symbol
            },
            'volatility': {
                'lookback_period': 20,  # Days for volatility calculation
                'max_volatility': 0.05,  # 5% maximum daily volatility
                'volatility_adjustment': True  # Adjust position size based on volatility
            }
        }
    
    def calculate_position_size(self, symbol: str, entry_price: float, 
                              stop_loss: float, account_balance: float,
                              volatility: Optional[float] = None) -> float:
        """
        Calculate optimal position size based on risk management rules.
        
        Args:
            symbol: Trading symbol
            entry_price: Entry price
            stop_loss: Stop loss price
            account_balance: Current account balance
            volatility: Symbol volatility (optional)
            
        Returns:
            Position size
        """
        config = self.config['position_sizing']
        method = config['method']
        
        # Calculate risk per trade in dollar terms
        risk_amount = account_balance * config['risk_per_trade']
        
        # Calculate price risk (distance to stop loss)
        price_risk = abs(entry_price - stop_loss)
        
        if price_risk == 0:
            return 0
        
        if method == 'fixed_percentage':
            # Fixed percentage risk per trade
            position_size = risk_amount / price_risk
            
        elif method == 'volatility_based':
            # Adjust position size based on volatility
            if volatility is None:
                volatility = 0.02  # Default 2% volatility
            
            # Reduce position size for higher volatility
            volatility_adjustment = min(1.0, 0.02 / volatility)
            position_size = (risk_amount / price_risk) * volatility_adjustment
            
        elif method == 'kelly':
            # Kelly criterion (simplified)
            # Requires win rate and average win/loss ratio
            win_rate = 0.55  # Default 55% win rate
            avg_win_loss_ratio = 1.5  # Default 1.5:1 win/loss ratio
            
            kelly_fraction = win_rate - ((1 - win_rate) / avg_win_loss_ratio)
            kelly_fraction = max(0, min(kelly_fraction, 0.25))  # Cap at 25%
            
            position_size = (account_balance * kelly_fraction) / price_risk
            
        else:
            # Default to fixed percentage
            position_size = risk_amount / price_risk
        
        # Apply position size limits
        max_position_value = account_balance * config['max_position_size']
        min_position_value = account_balance * config['min_position_size']
        
        position_value = position_size * entry_price
        
        if position_value > max_position_value:
            position_size = max_position_value / entry_price
        elif position_value < min_position_value:
            position_size = min_position_value / entry_price
        
        return position_size
    
    def calculate_stop_loss(self, symbol: str, entry_price: float, 
                           direction: str, atr: Optional[float] = None,
                           volatility: Optional[float] = None) -> float:
        """
        Calculate stop loss price based on risk management rules.
        
        Args:
            symbol: Trading symbol
            entry_price: Entry price
            direction: 'buy' or 'sell'
            atr: Average True Range (optional)
            volatility: Symbol volatility (optional)
            
        Returns:
            Stop loss price
        """
        config = self.config['stop_loss']
        method = config['method']
        
        if method == 'fixed_percentage':
            stop_distance = entry_price * config['fixed_percentage']
            
        elif method == 'atr_based':
            if atr is None:
                atr = entry_price * 0.01  # Default 1% ATR
            
            stop_distance = atr * config['atr_multiplier']
            
        elif method == 'volatility_based':
            if volatility is None:
                volatility = 0.02  # Default 2% volatility
            
            stop_distance = entry_price * volatility * 2  # 2x volatility
            
        else:
            # Default to fixed percentage
            stop_distance = entry_price * config['fixed_percentage']
        
        # Apply maximum stop loss limit
        max_stop_distance = entry_price * config['max_stop_loss']
        stop_distance = min(stop_distance, max_stop_distance)
        
        # Calculate stop loss price based on direction
        if direction.lower() == 'buy':
            stop_loss = entry_price - stop_distance
        else:  # sell
            stop_loss = entry_price + stop_distance
        
        return stop_loss
    
    def calculate_take_profit(self, symbol: str, entry_price: float, 
                            stop_loss: float, direction: str) -> float:
        """
        Calculate take profit price based on risk management rules.
        
        Args:
            symbol: Trading symbol
            entry_price: Entry price
            stop_loss: Stop loss price
            direction: 'buy' or 'sell'
            
        Returns:
            Take profit price
        """
        config = self.config['take_profit']
        method = config['method']
        
        if method == 'fixed_percentage':
            profit_distance = entry_price * config['fixed_percentage']
            
        elif method == 'risk_reward_ratio':
            risk_distance = abs(entry_price - stop_loss)
            profit_distance = risk_distance * config['risk_reward_ratio']
            
        elif method == 'trailing':
            # For trailing stops, return initial take profit
            profit_distance = entry_price * config['trailing_percentage'] * 3
            
        else:
            # Default to risk-reward ratio
            risk_distance = abs(entry_price - stop_loss)
            profit_distance = risk_distance * config['risk_reward_ratio']
        
        # Calculate take profit price based on direction
        if direction.lower() == 'buy':
            take_profit = entry_price + profit_distance
        else:  # sell
            take_profit = entry_price - profit_distance
        
        return take_profit
    
    def check_portfolio_limits(self, new_position: Dict, 
                             current_positions: List[Dict],
                             account_balance: float) -> Dict[str, Any]:
        """
        Check if new position violates portfolio limits.
        
        Args:
            new_position: New position details
            current_positions: List of current positions
            account_balance: Current account balance
            
        Returns:
            Validation result with allowed status and reasons
        """
        config = self.config['portfolio_limits']
        violations = []
        
        # Check maximum number of positions
        if len(current_positions) >= config['max_positions']:
            violations.append(f"Maximum positions limit ({config['max_positions']}) reached")
        
        # Check maximum exposure per symbol
        symbol = new_position['symbol']
        current_exposure = sum(
            pos['value'] for pos in current_positions 
            if pos['symbol'] == symbol
        )
        new_exposure = current_exposure + new_position['value']
        max_exposure = account_balance * config['max_exposure_per_symbol']
        
        if new_exposure > max_exposure:
            violations.append(f"Maximum exposure for {symbol} exceeded")
        
        # Check correlation limits (simplified)
        if len(current_positions) > 0:
            # This would require actual correlation calculation
            # For now, just check if too many positions in same currency
            base_currency = symbol[:3]
            same_currency_count = sum(
                1 for pos in current_positions 
                if pos['symbol'].startswith(base_currency)
            )
            
            if same_currency_count >= 3:  # Max 3 positions per base currency
                violations.append(f"Too many positions in {base_currency}")
        
        return {
            'allowed': len(violations) == 0,
            'violations': violations,
            'max_positions': config['max_positions'],
            'current_positions': len(current_positions),
            'max_exposure_per_symbol': config['max_exposure_per_symbol'],
            'current_exposure_ratio': new_exposure / account_balance if account_balance > 0 else 0
        }
    
    def check_daily_loss_limit(self, current_pnl: float, 
                              account_balance: float) -> Dict[str, Any]:
        """
        Check if daily loss limit is exceeded.
        
        Args:
            current_pnl: Current day's P&L
            account_balance: Current account balance
            
        Returns:
            Loss limit check result
        """
        config = self.config['portfolio_limits']
        max_daily_loss = account_balance * config['max_daily_loss']
        
        loss_ratio = abs(current_pnl) / account_balance if account_balance > 0 else 0
        
        return {
            'limit_exceeded': current_pnl < -max_daily_loss,
            'current_loss': current_pnl,
            'max_daily_loss': max_daily_loss,
            'loss_ratio': loss_ratio,
            'remaining_loss_allowance': max_daily_loss + current_pnl
        }
    
    def calculate_drawdown(self, equity_curve: List[float]) -> Dict[str, Any]:
        """
        Calculate current drawdown metrics.
        
        Args:
            equity_curve: List of portfolio values over time
            
        Returns:
            Drawdown metrics
        """
        if len(equity_curve) < 2:
            return {'current_drawdown': 0, 'max_drawdown': 0}
        
        equity_series = pd.Series(equity_curve)
        running_max = equity_series.expanding().max()
        drawdown = (equity_series - running_max) / running_max
        
        current_drawdown = drawdown.iloc[-1]
        max_drawdown = drawdown.min()
        
        config = self.config['portfolio_limits']
        max_allowed_drawdown = config['max_drawdown']
        
        return {
            'current_drawdown': current_drawdown,
            'max_drawdown': max_drawdown,
            'max_allowed_drawdown': max_allowed_drawdown,
            'drawdown_limit_exceeded': current_drawdown < -max_allowed_drawdown,
            'drawdown_series': drawdown.tolist()
        }
    
    def adjust_position_for_volatility(self, base_position_size: float,
                                     current_volatility: float,
                                     target_volatility: float = 0.02) -> float:
        """
        Adjust position size based on current market volatility.
        
        Args:
            base_position_size: Base position size
            current_volatility: Current market volatility
            target_volatility: Target volatility level
            
        Returns:
            Adjusted position size
        """
        if not self.config['volatility']['volatility_adjustment']:
            return base_position_size
        
        max_volatility = self.config['volatility']['max_volatility']
        
        # If volatility is too high, reduce position size
        if current_volatility > max_volatility:
            return 0  # No position if volatility is too high
        
        # Adjust position size inversely to volatility
        volatility_ratio = target_volatility / current_volatility
        adjusted_size = base_position_size * min(volatility_ratio, 2.0)  # Cap at 2x
        
        return adjusted_size
    
    def validate_trade(self, trade_request: Dict, 
                      current_positions: List[Dict],
                      account_info: Dict,
                      market_data: Dict) -> Dict[str, Any]:
        """
        Comprehensive trade validation before execution.
        
        Args:
            trade_request: Trade request details
            current_positions: Current open positions
            account_info: Account information
            market_data: Current market data
            
        Returns:
            Validation result
        """
        validation_result = {
            'approved': True,
            'reasons': [],
            'warnings': [],
            'adjusted_trade': trade_request.copy()
        }
        
        symbol = trade_request['symbol']
        direction = trade_request['direction']
        entry_price = trade_request.get('price', market_data.get('price', 0))
        account_balance = account_info.get('balance', 0)
        
        # Calculate stop loss if not provided
        if 'stop_loss' not in trade_request:
            atr = market_data.get('atr')
            volatility = market_data.get('volatility')
            stop_loss = self.calculate_stop_loss(symbol, entry_price, direction, atr, volatility)
            validation_result['adjusted_trade']['stop_loss'] = stop_loss
        else:
            stop_loss = trade_request['stop_loss']
        
        # Calculate take profit if not provided
        if 'take_profit' not in trade_request:
            take_profit = self.calculate_take_profit(symbol, entry_price, stop_loss, direction)
            validation_result['adjusted_trade']['take_profit'] = take_profit
        
        # Calculate position size if not provided
        if 'position_size' not in trade_request:
            volatility = market_data.get('volatility')
            position_size = self.calculate_position_size(
                symbol, entry_price, stop_loss, account_balance, volatility
            )
            validation_result['adjusted_trade']['position_size'] = position_size
        else:
            position_size = trade_request['position_size']
        
        # Check portfolio limits
        new_position = {
            'symbol': symbol,
            'value': position_size * entry_price
        }
        
        portfolio_check = self.check_portfolio_limits(
            new_position, current_positions, account_balance
        )
        
        if not portfolio_check['allowed']:
            validation_result['approved'] = False
            validation_result['reasons'].extend(portfolio_check['violations'])
        
        # Check daily loss limit
        current_pnl = sum(pos.get('pnl', 0) for pos in current_positions)
        loss_check = self.check_daily_loss_limit(current_pnl, account_balance)
        
        if loss_check['limit_exceeded']:
            validation_result['approved'] = False
            validation_result['reasons'].append("Daily loss limit exceeded")
        
        # Check market conditions
        if market_data.get('volatility', 0) > self.config['volatility']['max_volatility']:
            validation_result['approved'] = False
            validation_result['reasons'].append("Market volatility too high")
        
        # Add risk metrics to result
        validation_result['risk_metrics'] = {
            'position_size': position_size,
            'risk_amount': abs(entry_price - stop_loss) * position_size,
            'risk_percentage': (abs(entry_price - stop_loss) * position_size) / account_balance if account_balance > 0 else 0,
            'reward_risk_ratio': abs(validation_result['adjusted_trade'].get('take_profit', entry_price) - entry_price) / abs(entry_price - stop_loss) if stop_loss != entry_price else 0
        }
        
        return validation_result
    
    def update_trailing_stops(self, positions: List[Dict],
                            current_prices: Dict[str, float]) -> List[Dict]:
        """
        Enhanced trailing stop system with activation threshold.

        Args:
            positions: List of open positions
            current_prices: Current market prices

        Returns:
            Updated positions with new stop losses
        """
        config = self.config['take_profit']
        trailing_percentage = config.get('trailing_percentage', 0.05)  # 5% trailing
        activation_threshold = config.get('trailing_activation', 0.02)  # Activate after 2% profit

        updated_positions = []

        for position in positions:
            symbol = position['symbol']
            direction = position['direction']
            entry_price = position['entry_price']
            current_stop = position.get('stop_loss', 0)
            current_price = current_prices.get(symbol, entry_price)

            # Calculate current profit/loss percentage
            if direction.lower() == 'buy':
                profit_pct = (current_price - entry_price) / entry_price
            else:
                profit_pct = (entry_price - current_price) / entry_price

            new_stop = current_stop
            trailing_active = position.get('trailing_active', False)

            # Activate trailing stop if profit threshold is reached
            if not trailing_active and profit_pct >= activation_threshold:
                trailing_active = True
                position['trailing_activated_at'] = datetime.now()
                position['trailing_activated_price'] = current_price
                print(f"🎯 Trailing stop ACTIVATED for {symbol} at {profit_pct:.2%} profit")

            # Update trailing stop if active
            if trailing_active:
                if direction.lower() == 'buy':
                    # For long positions, trail stop up
                    potential_stop = current_price * (1 - trailing_percentage)
                    if potential_stop > current_stop:
                        new_stop = potential_stop
                        print(f"📈 Trailing stop updated for {symbol}: {current_stop:.5f} → {new_stop:.5f}")
                else:
                    # For short positions, trail stop down
                    potential_stop = current_price * (1 + trailing_percentage)
                    if potential_stop < current_stop:
                        new_stop = potential_stop
                        print(f"📉 Trailing stop updated for {symbol}: {current_stop:.5f} → {new_stop:.5f}")

            updated_position = position.copy()
            updated_position['stop_loss'] = new_stop
            updated_position['trailing_active'] = trailing_active
            updated_position['current_profit_pct'] = profit_pct

            if new_stop != current_stop:
                updated_position['stop_updated'] = True
                updated_position['stop_update_time'] = datetime.now()
                updated_position['stop_update_reason'] = 'trailing_stop'

            updated_positions.append(updated_position)

        return updated_positions
    
    def get_risk_summary(self, positions: List[Dict], 
                        account_info: Dict) -> Dict[str, Any]:
        """
        Get comprehensive risk summary.
        
        Args:
            positions: Current positions
            account_info: Account information
            
        Returns:
            Risk summary
        """
        account_balance = account_info.get('balance', 0)
        equity = account_info.get('equity', account_balance)
        
        # Calculate total exposure
        total_exposure = sum(pos.get('value', 0) for pos in positions)
        exposure_ratio = total_exposure / account_balance if account_balance > 0 else 0
        
        # Calculate total risk
        total_risk = sum(pos.get('risk_amount', 0) for pos in positions)
        risk_ratio = total_risk / account_balance if account_balance > 0 else 0
        
        # Calculate current P&L
        current_pnl = sum(pos.get('pnl', 0) for pos in positions)
        pnl_ratio = current_pnl / account_balance if account_balance > 0 else 0
        
        # Count positions by symbol
        symbol_counts = {}
        for pos in positions:
            symbol = pos['symbol']
            symbol_counts[symbol] = symbol_counts.get(symbol, 0) + 1
        
        return {
            'account': {
                'balance': account_balance,
                'equity': equity,
                'margin_used': account_info.get('margin', 0),
                'free_margin': account_info.get('free_margin', 0)
            },
            'positions': {
                'total_count': len(positions),
                'total_exposure': total_exposure,
                'exposure_ratio': exposure_ratio,
                'total_risk': total_risk,
                'risk_ratio': risk_ratio,
                'current_pnl': current_pnl,
                'pnl_ratio': pnl_ratio
            },
            'limits': {
                'max_positions': self.config['portfolio_limits']['max_positions'],
                'max_daily_loss': self.config['portfolio_limits']['max_daily_loss'],
                'max_drawdown': self.config['portfolio_limits']['max_drawdown'],
                'max_exposure_per_symbol': self.config['portfolio_limits']['max_exposure_per_symbol']
            },
            'symbol_distribution': symbol_counts,
            'risk_level': self._assess_risk_level(risk_ratio, exposure_ratio, len(positions))
        }
    
    def _assess_risk_level(self, risk_ratio: float, exposure_ratio: float, 
                          position_count: int) -> str:
        """Assess overall risk level."""
        if risk_ratio > 0.1 or exposure_ratio > 0.8 or position_count > 8:
            return 'HIGH'
        elif risk_ratio > 0.05 or exposure_ratio > 0.5 or position_count > 5:
            return 'MEDIUM'
        else:
            return 'LOW'


class TrailingStopManager:
    """
    Advanced trailing stop management system.
    """

    def __init__(self, config: Optional[Dict] = None):
        """Initialize trailing stop manager."""
        self.config = config or {
            'trailing_percentage': 0.05,  # 5% trailing distance
            'activation_threshold': 0.02,  # Activate after 2% profit
            'step_size': 0.01,  # Move stop in 1% increments
            'max_trailing_distance': 0.15,  # Maximum 15% trailing distance
            'breakeven_buffer': 0.005  # 0.5% buffer above breakeven
        }
        self.position_trails = {}  # Track trailing data per position

    def initialize_trailing_stop(self, position_id: str, entry_price: float,
                                direction: str, initial_stop: float) -> Dict:
        """Initialize trailing stop for a new position."""
        trail_data = {
            'position_id': position_id,
            'entry_price': entry_price,
            'direction': direction.lower(),
            'initial_stop': initial_stop,
            'current_stop': initial_stop,
            'highest_price': entry_price if direction.lower() == 'buy' else entry_price,
            'lowest_price': entry_price if direction.lower() == 'sell' else entry_price,
            'trailing_active': False,
            'activation_price': None,
            'total_trail_distance': 0,
            'trail_updates': 0,
            'created_at': datetime.now()
        }

        self.position_trails[position_id] = trail_data
        return trail_data

    def update_trailing_stop(self, position_id: str, current_price: float) -> Dict:
        """Update trailing stop for a position."""
        if position_id not in self.position_trails:
            raise ValueError(f"Position {position_id} not found in trailing stops")

        trail_data = self.position_trails[position_id]
        direction = trail_data['direction']
        entry_price = trail_data['entry_price']
        current_stop = trail_data['current_stop']

        # Update price extremes
        if direction == 'buy':
            trail_data['highest_price'] = max(trail_data['highest_price'], current_price)
            profit_pct = (current_price - entry_price) / entry_price
        else:
            trail_data['lowest_price'] = min(trail_data['lowest_price'], current_price)
            profit_pct = (entry_price - current_price) / entry_price

        # Check if trailing should be activated
        if not trail_data['trailing_active'] and profit_pct >= self.config['activation_threshold']:
            trail_data['trailing_active'] = True
            trail_data['activation_price'] = current_price
            print(f"🎯 Trailing stop ACTIVATED for position {position_id} at {profit_pct:.2%} profit")

        # Calculate new trailing stop
        new_stop = current_stop
        if trail_data['trailing_active']:
            trailing_pct = self.config['trailing_percentage']

            if direction == 'buy':
                # Trail stop up for long positions
                potential_stop = trail_data['highest_price'] * (1 - trailing_pct)

                # Ensure stop doesn't go below breakeven (with buffer)
                breakeven_stop = entry_price * (1 + self.config['breakeven_buffer'])
                potential_stop = max(potential_stop, breakeven_stop)

                if potential_stop > current_stop:
                    new_stop = potential_stop
                    trail_data['trail_updates'] += 1
                    trail_data['total_trail_distance'] = new_stop - trail_data['initial_stop']

            else:
                # Trail stop down for short positions
                potential_stop = trail_data['lowest_price'] * (1 + trailing_pct)

                # Ensure stop doesn't go above breakeven (with buffer)
                breakeven_stop = entry_price * (1 - self.config['breakeven_buffer'])
                potential_stop = min(potential_stop, breakeven_stop)

                if potential_stop < current_stop:
                    new_stop = potential_stop
                    trail_data['trail_updates'] += 1
                    trail_data['total_trail_distance'] = trail_data['initial_stop'] - new_stop

        # Update trail data
        trail_data['current_stop'] = new_stop
        trail_data['last_update'] = datetime.now()
        trail_data['current_price'] = current_price
        trail_data['current_profit_pct'] = profit_pct

        return {
            'position_id': position_id,
            'old_stop': current_stop,
            'new_stop': new_stop,
            'stop_updated': new_stop != current_stop,
            'trailing_active': trail_data['trailing_active'],
            'profit_pct': profit_pct,
            'trail_distance': abs(current_price - new_stop) / current_price,
            'total_updates': trail_data['trail_updates']
        }

    def get_trailing_summary(self, position_id: str) -> Dict:
        """Get comprehensive trailing stop summary."""
        if position_id not in self.position_trails:
            return {'error': 'Position not found'}

        trail_data = self.position_trails[position_id]

        return {
            'position_id': position_id,
            'direction': trail_data['direction'],
            'entry_price': trail_data['entry_price'],
            'current_stop': trail_data['current_stop'],
            'initial_stop': trail_data['initial_stop'],
            'trailing_active': trail_data['trailing_active'],
            'activation_price': trail_data.get('activation_price'),
            'total_trail_distance': trail_data.get('total_trail_distance', 0),
            'trail_updates': trail_data.get('trail_updates', 0),
            'current_profit_pct': trail_data.get('current_profit_pct', 0),
            'highest_price': trail_data.get('highest_price'),
            'lowest_price': trail_data.get('lowest_price'),
            'created_at': trail_data['created_at'],
            'last_update': trail_data.get('last_update')
        }

    def remove_position(self, position_id: str):
        """Remove position from trailing stop tracking."""
        if position_id in self.position_trails:
            del self.position_trails[position_id]

    def get_all_trailing_positions(self) -> List[Dict]:
        """Get all positions with trailing stops."""
        return [self.get_trailing_summary(pos_id) for pos_id in self.position_trails.keys()]


class PositionSizer:
    """
    Advanced position sizing algorithms.
    """

    @staticmethod
    def fixed_percentage(account_balance: float, risk_percentage: float,
                        entry_price: float, stop_loss: float) -> float:
        """Fixed percentage position sizing."""
        risk_amount = account_balance * risk_percentage
        price_risk = abs(entry_price - stop_loss)

        if price_risk == 0:
            return 0

        return risk_amount / price_risk

    @staticmethod
    def kelly_criterion(win_rate: float, avg_win: float, avg_loss: float,
                       account_balance: float, entry_price: float,
                       stop_loss: float) -> float:
        """Kelly criterion position sizing."""
        if avg_loss == 0:
            return 0

        win_loss_ratio = avg_win / abs(avg_loss)
        kelly_fraction = win_rate - ((1 - win_rate) / win_loss_ratio)

        # Cap Kelly fraction to prevent over-leveraging
        kelly_fraction = max(0, min(kelly_fraction, 0.25))

        price_risk = abs(entry_price - stop_loss)
        if price_risk == 0:
            return 0

        return (account_balance * kelly_fraction) / price_risk

    @staticmethod
    def volatility_adjusted(base_position_size: float, current_volatility: float,
                           target_volatility: float = 0.02) -> float:
        """Volatility-adjusted position sizing."""
        if current_volatility == 0:
            return base_position_size

        volatility_ratio = target_volatility / current_volatility
        return base_position_size * min(volatility_ratio, 2.0)  # Cap at 2x

    @staticmethod
    def atr_based(account_balance: float, risk_percentage: float,
                 atr: float, atr_multiplier: float = 2.0) -> float:
        """ATR-based position sizing."""
        risk_amount = account_balance * risk_percentage
        risk_per_unit = atr * atr_multiplier

        if risk_per_unit == 0:
            return 0

        return risk_amount / risk_per_unit


class StopLossCalculator:
    """
    Advanced stop loss calculation methods.
    """

    @staticmethod
    def atr_stop(entry_price: float, atr: float, direction: str,
                multiplier: float = 2.0) -> float:
        """ATR-based stop loss."""
        stop_distance = atr * multiplier

        if direction.lower() == 'buy':
            return entry_price - stop_distance
        else:
            return entry_price + stop_distance

    @staticmethod
    def percentage_stop(entry_price: float, direction: str,
                       percentage: float = 0.02) -> float:
        """Percentage-based stop loss."""
        stop_distance = entry_price * percentage

        if direction.lower() == 'buy':
            return entry_price - stop_distance
        else:
            return entry_price + stop_distance

    @staticmethod
    def volatility_stop(entry_price: float, volatility: float, direction: str,
                       multiplier: float = 2.0) -> float:
        """Volatility-based stop loss."""
        stop_distance = entry_price * volatility * multiplier

        if direction.lower() == 'buy':
            return entry_price - stop_distance
        else:
            return entry_price + stop_distance

    @staticmethod
    def support_resistance_stop(entry_price: float, support_resistance_level: float,
                               direction: str, buffer: float = 0.001) -> float:
        """Support/resistance-based stop loss."""
        if direction.lower() == 'buy':
            return support_resistance_level - (entry_price * buffer)
        else:
            return support_resistance_level + (entry_price * buffer)


class PortfolioRiskAnalyzer:
    """
    Portfolio-level risk analysis and monitoring.
    """

    def __init__(self):
        self.correlation_matrix = {}
        self.var_models = {}

    def calculate_portfolio_var(self, positions: List[Dict],
                               confidence_level: float = 0.05,
                               time_horizon: int = 1) -> float:
        """
        Calculate portfolio Value at Risk.

        Args:
            positions: List of portfolio positions
            confidence_level: VaR confidence level
            time_horizon: Time horizon in days

        Returns:
            Portfolio VaR
        """
        if not positions:
            return 0

        # Simplified VaR calculation
        # In practice, this would use historical simulation or Monte Carlo

        total_value = sum(pos.get('value', 0) for pos in positions)
        avg_volatility = np.mean([pos.get('volatility', 0.02) for pos in positions])

        # Assume normal distribution for simplicity
        z_score = 1.645 if confidence_level == 0.05 else 2.33  # 95% or 99%

        portfolio_var = total_value * avg_volatility * z_score * np.sqrt(time_horizon)

        return portfolio_var

    def calculate_correlation_risk(self, positions: List[Dict]) -> Dict[str, float]:
        """
        Calculate correlation risk between positions.

        Args:
            positions: List of portfolio positions

        Returns:
            Correlation risk metrics
        """
        if len(positions) < 2:
            return {'avg_correlation': 0, 'max_correlation': 0, 'correlation_risk': 'LOW'}

        # Simplified correlation calculation
        # In practice, this would use historical price correlations

        symbols = [pos['symbol'] for pos in positions]
        correlations = []

        for i, symbol1 in enumerate(symbols):
            for j, symbol2 in enumerate(symbols[i+1:], i+1):
                # Simplified correlation based on currency pairs
                base1, quote1 = symbol1[:3], symbol1[3:]
                base2, quote2 = symbol2[:3], symbol2[3:]

                if base1 == base2 or quote1 == quote2:
                    correlation = 0.7  # High correlation
                elif base1 == quote2 or quote1 == base2:
                    correlation = -0.7  # Negative correlation
                else:
                    correlation = 0.1  # Low correlation

                correlations.append(abs(correlation))

        avg_correlation = np.mean(correlations) if correlations else 0
        max_correlation = max(correlations) if correlations else 0

        if max_correlation > 0.8:
            risk_level = 'HIGH'
        elif max_correlation > 0.5:
            risk_level = 'MEDIUM'
        else:
            risk_level = 'LOW'

        return {
            'avg_correlation': avg_correlation,
            'max_correlation': max_correlation,
            'correlation_risk': risk_level,
            'correlation_pairs': len(correlations)
        }

    def calculate_concentration_risk(self, positions: List[Dict]) -> Dict[str, Any]:
        """
        Calculate portfolio concentration risk.

        Args:
            positions: List of portfolio positions

        Returns:
            Concentration risk metrics
        """
        if not positions:
            return {'concentration_risk': 'LOW', 'herfindahl_index': 0}

        total_value = sum(pos.get('value', 0) for pos in positions)

        if total_value == 0:
            return {'concentration_risk': 'LOW', 'herfindahl_index': 0}

        # Calculate position weights
        weights = [pos.get('value', 0) / total_value for pos in positions]

        # Herfindahl-Hirschman Index
        hhi = sum(w**2 for w in weights)

        # Concentration by symbol
        symbol_weights = {}
        for pos in positions:
            symbol = pos['symbol']
            weight = pos.get('value', 0) / total_value
            symbol_weights[symbol] = symbol_weights.get(symbol, 0) + weight

        max_symbol_weight = max(symbol_weights.values()) if symbol_weights else 0

        # Risk assessment
        if hhi > 0.25 or max_symbol_weight > 0.4:
            risk_level = 'HIGH'
        elif hhi > 0.15 or max_symbol_weight > 0.25:
            risk_level = 'MEDIUM'
        else:
            risk_level = 'LOW'

        return {
            'concentration_risk': risk_level,
            'herfindahl_index': hhi,
            'max_symbol_weight': max_symbol_weight,
            'symbol_weights': symbol_weights,
            'effective_positions': 1 / hhi if hhi > 0 else len(positions)
        }
