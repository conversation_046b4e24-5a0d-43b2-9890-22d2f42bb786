#!/usr/bin/env python3
"""
Simple test for MT5 auto-trading setup.
"""

import sys
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

def test_mt5_connection():
    """Test basic MT5 connection."""
    print("🤖 AI Trading Bot - MT5 Auto-Trading Test")
    print("="*60)
    
    try:
        # Try to import MT5
        import MetaTrader5 as mt5
        print("✅ MetaTrader5 module available")
    except ImportError:
        print("❌ MetaTrader5 module not installed")
        print("💡 Install with: pip install MetaTrader5")
        return False
    
    # Test connection
    print("📡 Testing MT5 connection...")
    
    if not mt5.initialize():
        print("❌ Failed to initialize MT5")
        print("💡 Make sure MetaTrader 5 is running and logged in")
        return False
    
    print("✅ MT5 initialized successfully!")
    
    # Get terminal info
    terminal_info = mt5.terminal_info()
    if terminal_info is None:
        print("❌ Failed to get terminal info")
        mt5.shutdown()
        return False
    
    print(f"📊 Terminal Information:")
    print(f"   Company: {terminal_info.company}")
    print(f"   Name: {terminal_info.name}")
    print(f"   Build: {terminal_info.build}")
    print(f"   Trade allowed: {'✅' if terminal_info.trade_allowed else '❌'}")
    print(f"   Trade API enabled: {'✅' if not terminal_info.tradeapi_disabled else '❌'}")
    
    # Get account info
    account_info = mt5.account_info()
    if account_info is None:
        print("❌ Failed to get account info")
        mt5.shutdown()
        return False
    
    print(f"💰 Account Information:")
    print(f"   Login: {account_info.login}")
    print(f"   Server: {account_info.server}")
    print(f"   Balance: ${account_info.balance:,.2f}")
    print(f"   Equity: ${account_info.equity:,.2f}")
    print(f"   Trade allowed: {'✅' if account_info.trade_allowed else '❌'}")
    print(f"   Expert trading: {'✅' if account_info.trade_expert else '❌'}")
    
    # Check auto-trading status
    auto_trading_ready = (
        terminal_info.trade_allowed and
        not terminal_info.tradeapi_disabled and
        account_info.trade_allowed and
        account_info.trade_expert
    )
    
    print(f"\n🚀 AUTO-TRADING STATUS: {'✅ READY' if auto_trading_ready else '❌ NOT READY'}")
    
    if not auto_trading_ready:
        print("\n💡 To enable auto-trading:")
        print("   1. In MT5, go to Tools → Options → Expert Advisors")
        print("   2. Check 'Allow automated trading'")
        print("   3. Check 'Allow DLL imports'")
        print("   4. Restart MT5")
    
    # Test symbol access
    print(f"\n📈 Testing symbol access...")
    symbols = ['EURUSD', 'GBPUSD', 'USDJPY']
    
    for symbol in symbols:
        symbol_info = mt5.symbol_info(symbol)
        if symbol_info is not None:
            tick = mt5.symbol_info_tick(symbol)
            if tick is not None:
                print(f"   {symbol}: {tick.bid:.5f}/{tick.ask:.5f} ✅")
            else:
                print(f"   {symbol}: No price data ⚠️")
        else:
            print(f"   {symbol}: Not available ❌")
    
    mt5.shutdown()
    return auto_trading_ready


def show_startup_instructions():
    """Show instructions for starting auto-trading."""
    print("\n" + "="*80)
    print("🚀 AUTO-TRADING STARTUP INSTRUCTIONS")
    print("="*80)
    
    print("📋 PREREQUISITES:")
    print("   ✅ MetaTrader 5 installed and running")
    print("   ✅ Logged into your trading account")
    print("   ✅ Auto-trading enabled in MT5 settings")
    print("   ✅ Python MetaTrader5 package installed")
    
    print("\n⚙️ HIGH-RISK CONFIGURATION ACTIVE:")
    print("   🚨 30% risk per trade (EXTREME RISK!)")
    print("   🚨 50% maximum daily loss")
    print("   🚨 70% maximum drawdown")
    print("   🚨 Automatic trading enabled")
    
    print("\n🚀 TO START AUTO-TRADING:")
    print("   1. Run: python start_trading_bot.py")
    print("   2. Bot will connect to MT5 automatically")
    print("   3. AI will analyze markets and place trades")
    print("   4. Monitor dashboard: http://localhost:8000")
    print("   5. Check logs: logs/trading_bot.log")
    
    print("\n⚠️ SAFETY WARNINGS:")
    print("   • Start with DEMO account or very small real account")
    print("   • Monitor ALL trades closely")
    print("   • Be prepared for rapid account changes")
    print("   • 30% risk can wipe out account in 3-4 losing trades")
    
    print("\n🛑 EMERGENCY STOP:")
    print("   • Press Ctrl+C in the bot terminal")
    print("   • Or disable auto-trading in MT5")
    print("   • Bot will stop placing new trades")
    
    print("\n📊 MONITORING:")
    print("   • Real-time dashboard: http://localhost:8000")
    print("   • API documentation: http://localhost:8000/docs")
    print("   • Log files: logs/ directory")
    print("   • Performance tracking: data/processed/")


def main():
    """Main test function."""
    try:
        # Test MT5 connection and auto-trading setup
        mt5_ready = test_mt5_connection()
        
        # Show startup instructions
        show_startup_instructions()
        
        print("\n" + "="*80)
        print("🎯 AUTO-TRADING SETUP TEST COMPLETED")
        print("="*80)
        
        if mt5_ready:
            print("✅ YOUR BOT IS READY FOR AUTOMATIC TRADING!")
            print("🚀 Run: python start_trading_bot.py")
            print("⚠️ REMEMBER: 30% risk per trade is EXTREMELY DANGEROUS!")
        else:
            print("❌ Please fix MT5 setup issues before starting auto-trading")
            print("💡 Follow the instructions above to enable auto-trading in MT5")
        
        print("\n🎮 Happy automated trading! (Trade safely!)")
        
    except Exception as e:
        print(f"❌ Error during test: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
