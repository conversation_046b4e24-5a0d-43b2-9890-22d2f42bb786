"""
Performance metrics calculation for trading strategies.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any, Union
from datetime import datetime, timedelta


class PerformanceMetrics:
    """
    Comprehensive performance metrics calculator for trading strategies.
    """
    
    @staticmethod
    def calculate_returns(prices: pd.Series) -> pd.Series:
        """Calculate simple returns."""
        return prices.pct_change()
    
    @staticmethod
    def calculate_log_returns(prices: pd.Series) -> pd.Series:
        """Calculate logarithmic returns."""
        return np.log(prices / prices.shift(1))
    
    @staticmethod
    def calculate_cumulative_returns(returns: pd.Series) -> pd.Series:
        """Calculate cumulative returns."""
        return (1 + returns).cumprod() - 1
    
    @staticmethod
    def total_return(initial_value: float, final_value: float) -> float:
        """Calculate total return."""
        return (final_value - initial_value) / initial_value
    
    @staticmethod
    def annualized_return(total_return: float, days: int) -> float:
        """Calculate annualized return."""
        if days <= 0:
            return 0
        return (1 + total_return) ** (365 / days) - 1
    
    @staticmethod
    def volatility(returns: pd.Series, annualize: bool = True) -> float:
        """Calculate volatility (standard deviation of returns)."""
        vol = returns.std()
        if annualize:
            vol = vol * np.sqrt(252)  # Assuming daily returns
        return vol
    
    @staticmethod
    def sharpe_ratio(returns: pd.Series, risk_free_rate: float = 0.0, 
                    annualize: bool = True) -> float:
        """Calculate Sharpe ratio."""
        excess_returns = returns - risk_free_rate / 252  # Daily risk-free rate
        mean_excess = excess_returns.mean()
        std_excess = excess_returns.std()
        
        if std_excess == 0:
            return 0
        
        sharpe = mean_excess / std_excess
        
        if annualize:
            sharpe = sharpe * np.sqrt(252)
        
        return sharpe
    
    @staticmethod
    def sortino_ratio(returns: pd.Series, risk_free_rate: float = 0.0,
                     annualize: bool = True) -> float:
        """Calculate Sortino ratio (uses downside deviation)."""
        excess_returns = returns - risk_free_rate / 252
        mean_excess = excess_returns.mean()
        
        # Downside deviation
        downside_returns = excess_returns[excess_returns < 0]
        downside_std = downside_returns.std()
        
        if downside_std == 0:
            return float('inf') if mean_excess > 0 else 0
        
        sortino = mean_excess / downside_std
        
        if annualize:
            sortino = sortino * np.sqrt(252)
        
        return sortino
    
    @staticmethod
    def maximum_drawdown(prices: pd.Series) -> Dict[str, float]:
        """Calculate maximum drawdown and related metrics."""
        cumulative = prices / prices.iloc[0]  # Normalize to start at 1
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        
        max_dd = drawdown.min()
        max_dd_idx = drawdown.idxmin()
        
        # Find the peak before the maximum drawdown
        peak_idx = running_max.loc[:max_dd_idx].idxmax()
        
        # Find recovery point (if any)
        recovery_idx = None
        peak_value = running_max.loc[peak_idx]
        
        for idx in cumulative.loc[max_dd_idx:].index:
            if cumulative.loc[idx] >= peak_value:
                recovery_idx = idx
                break
        
        # Calculate drawdown duration
        if recovery_idx is not None:
            drawdown_duration = (recovery_idx - peak_idx).days
        else:
            drawdown_duration = (cumulative.index[-1] - peak_idx).days
        
        return {
            'max_drawdown': max_dd,
            'max_drawdown_date': max_dd_idx,
            'peak_date': peak_idx,
            'recovery_date': recovery_idx,
            'drawdown_duration_days': drawdown_duration
        }
    
    @staticmethod
    def calmar_ratio(annualized_return: float, max_drawdown: float) -> float:
        """Calculate Calmar ratio."""
        if max_drawdown == 0:
            return float('inf') if annualized_return > 0 else 0
        return annualized_return / abs(max_drawdown)
    
    @staticmethod
    def value_at_risk(returns: pd.Series, confidence_level: float = 0.05) -> float:
        """Calculate Value at Risk (VaR)."""
        return np.percentile(returns.dropna(), confidence_level * 100)
    
    @staticmethod
    def conditional_value_at_risk(returns: pd.Series, confidence_level: float = 0.05) -> float:
        """Calculate Conditional Value at Risk (CVaR)."""
        var = PerformanceMetrics.value_at_risk(returns, confidence_level)
        return returns[returns <= var].mean()
    
    @staticmethod
    def beta(returns: pd.Series, market_returns: pd.Series) -> float:
        """Calculate beta relative to market."""
        covariance = np.cov(returns.dropna(), market_returns.dropna())[0][1]
        market_variance = np.var(market_returns.dropna())
        
        if market_variance == 0:
            return 0
        
        return covariance / market_variance
    
    @staticmethod
    def alpha(returns: pd.Series, market_returns: pd.Series, 
             risk_free_rate: float = 0.0) -> float:
        """Calculate alpha (Jensen's alpha)."""
        beta = PerformanceMetrics.beta(returns, market_returns)
        
        portfolio_return = returns.mean() * 252  # Annualized
        market_return = market_returns.mean() * 252  # Annualized
        
        expected_return = risk_free_rate + beta * (market_return - risk_free_rate)
        
        return portfolio_return - expected_return
    
    @staticmethod
    def information_ratio(returns: pd.Series, benchmark_returns: pd.Series) -> float:
        """Calculate information ratio."""
        excess_returns = returns - benchmark_returns
        tracking_error = excess_returns.std()
        
        if tracking_error == 0:
            return 0
        
        return excess_returns.mean() / tracking_error
    
    @staticmethod
    def treynor_ratio(returns: pd.Series, market_returns: pd.Series,
                     risk_free_rate: float = 0.0) -> float:
        """Calculate Treynor ratio."""
        beta = PerformanceMetrics.beta(returns, market_returns)
        
        if beta == 0:
            return 0
        
        portfolio_return = returns.mean() * 252  # Annualized
        
        return (portfolio_return - risk_free_rate) / beta
    
    @staticmethod
    def calculate_trade_metrics(trades: List[Dict]) -> Dict[str, Any]:
        """Calculate trade-specific metrics."""
        if not trades:
            return {}
        
        # Extract trade P&L
        trade_pnl = []
        positions = {}
        
        for trade in trades:
            symbol = trade['symbol']
            action = trade['action']
            quantity = trade['quantity']
            price = trade['price']
            
            if symbol not in positions:
                positions[symbol] = {'quantity': 0, 'avg_price': 0}
            
            if action == 'buy':
                # Update average price for position
                current_qty = positions[symbol]['quantity']
                current_avg = positions[symbol]['avg_price']
                
                new_qty = current_qty + quantity
                new_avg = ((current_qty * current_avg) + (quantity * price)) / new_qty
                
                positions[symbol]['quantity'] = new_qty
                positions[symbol]['avg_price'] = new_avg
                
            elif action == 'sell':
                if positions[symbol]['quantity'] > 0:
                    # Calculate P&L for the sold quantity
                    avg_buy_price = positions[symbol]['avg_price']
                    pnl = (price - avg_buy_price) * quantity
                    trade_pnl.append(pnl)
                    
                    # Update position
                    positions[symbol]['quantity'] -= quantity
        
        if not trade_pnl:
            return {
                'total_trades': len(trades),
                'profitable_trades': 0,
                'losing_trades': 0,
                'win_rate': 0,
                'profit_factor': 0,
                'avg_win': 0,
                'avg_loss': 0,
                'largest_win': 0,
                'largest_loss': 0,
                'total_pnl': 0
            }
        
        # Calculate metrics
        profitable_trades = [pnl for pnl in trade_pnl if pnl > 0]
        losing_trades = [pnl for pnl in trade_pnl if pnl < 0]
        
        win_rate = len(profitable_trades) / len(trade_pnl)
        
        avg_win = np.mean(profitable_trades) if profitable_trades else 0
        avg_loss = np.mean(losing_trades) if losing_trades else 0
        
        profit_factor = abs(sum(profitable_trades) / sum(losing_trades)) if losing_trades else float('inf')
        
        largest_win = max(profitable_trades) if profitable_trades else 0
        largest_loss = min(losing_trades) if losing_trades else 0
        
        total_pnl = sum(trade_pnl)
        
        return {
            'total_trades': len(trade_pnl),
            'profitable_trades': len(profitable_trades),
            'losing_trades': len(losing_trades),
            'win_rate': win_rate,
            'profit_factor': profit_factor,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'largest_win': largest_win,
            'largest_loss': largest_loss,
            'total_pnl': total_pnl
        }
    
    @staticmethod
    def calculate_comprehensive_metrics(equity_curve: pd.Series, 
                                      trades: Optional[List[Dict]] = None,
                                      benchmark: Optional[pd.Series] = None,
                                      risk_free_rate: float = 0.0) -> Dict[str, Any]:
        """Calculate comprehensive performance metrics."""
        if len(equity_curve) < 2:
            return {}
        
        # Basic calculations
        returns = PerformanceMetrics.calculate_returns(equity_curve)
        returns = returns.dropna()
        
        initial_value = equity_curve.iloc[0]
        final_value = equity_curve.iloc[-1]
        
        # Time period
        start_date = equity_curve.index[0]
        end_date = equity_curve.index[-1]
        days = (end_date - start_date).days
        
        # Calculate metrics
        total_ret = PerformanceMetrics.total_return(initial_value, final_value)
        ann_ret = PerformanceMetrics.annualized_return(total_ret, days)
        vol = PerformanceMetrics.volatility(returns)
        sharpe = PerformanceMetrics.sharpe_ratio(returns, risk_free_rate)
        sortino = PerformanceMetrics.sortino_ratio(returns, risk_free_rate)
        
        # Drawdown metrics
        dd_metrics = PerformanceMetrics.maximum_drawdown(equity_curve)
        calmar = PerformanceMetrics.calmar_ratio(ann_ret, dd_metrics['max_drawdown'])
        
        # Risk metrics
        var_5 = PerformanceMetrics.value_at_risk(returns, 0.05)
        cvar_5 = PerformanceMetrics.conditional_value_at_risk(returns, 0.05)
        
        metrics = {
            'period': {
                'start_date': start_date,
                'end_date': end_date,
                'days': days
            },
            'returns': {
                'total_return': total_ret,
                'annualized_return': ann_ret,
                'volatility': vol,
                'sharpe_ratio': sharpe,
                'sortino_ratio': sortino
            },
            'risk': {
                'max_drawdown': dd_metrics['max_drawdown'],
                'calmar_ratio': calmar,
                'var_5': var_5,
                'cvar_5': cvar_5
            },
            'drawdown_details': dd_metrics
        }
        
        # Benchmark comparison
        if benchmark is not None:
            benchmark_returns = PerformanceMetrics.calculate_returns(benchmark).dropna()
            
            # Align returns
            common_index = returns.index.intersection(benchmark_returns.index)
            if len(common_index) > 1:
                aligned_returns = returns.loc[common_index]
                aligned_benchmark = benchmark_returns.loc[common_index]
                
                beta = PerformanceMetrics.beta(aligned_returns, aligned_benchmark)
                alpha = PerformanceMetrics.alpha(aligned_returns, aligned_benchmark, risk_free_rate)
                info_ratio = PerformanceMetrics.information_ratio(aligned_returns, aligned_benchmark)
                treynor = PerformanceMetrics.treynor_ratio(aligned_returns, aligned_benchmark, risk_free_rate)
                
                metrics['benchmark_comparison'] = {
                    'beta': beta,
                    'alpha': alpha,
                    'information_ratio': info_ratio,
                    'treynor_ratio': treynor
                }
        
        # Trade metrics
        if trades:
            trade_metrics = PerformanceMetrics.calculate_trade_metrics(trades)
            metrics['trades'] = trade_metrics
        
        return metrics
    
    @staticmethod
    def generate_performance_report(metrics: Dict[str, Any]) -> str:
        """Generate a formatted performance report."""
        report = "Performance Report\n"
        report += "=" * 50 + "\n\n"
        
        # Period information
        if 'period' in metrics:
            period = metrics['period']
            report += f"Period: {period['start_date'].strftime('%Y-%m-%d')} to {period['end_date'].strftime('%Y-%m-%d')}\n"
            report += f"Duration: {period['days']} days\n\n"
        
        # Return metrics
        if 'returns' in metrics:
            returns = metrics['returns']
            report += "Return Metrics:\n"
            report += f"  Total Return: {returns['total_return']:.2%}\n"
            report += f"  Annualized Return: {returns['annualized_return']:.2%}\n"
            report += f"  Volatility: {returns['volatility']:.2%}\n"
            report += f"  Sharpe Ratio: {returns['sharpe_ratio']:.3f}\n"
            report += f"  Sortino Ratio: {returns['sortino_ratio']:.3f}\n\n"
        
        # Risk metrics
        if 'risk' in metrics:
            risk = metrics['risk']
            report += "Risk Metrics:\n"
            report += f"  Maximum Drawdown: {risk['max_drawdown']:.2%}\n"
            report += f"  Calmar Ratio: {risk['calmar_ratio']:.3f}\n"
            report += f"  VaR (5%): {risk['var_5']:.2%}\n"
            report += f"  CVaR (5%): {risk['cvar_5']:.2%}\n\n"
        
        # Trade metrics
        if 'trades' in metrics:
            trades = metrics['trades']
            report += "Trade Metrics:\n"
            report += f"  Total Trades: {trades['total_trades']}\n"
            report += f"  Win Rate: {trades['win_rate']:.2%}\n"
            report += f"  Profit Factor: {trades['profit_factor']:.3f}\n"
            report += f"  Average Win: ${trades['avg_win']:.2f}\n"
            report += f"  Average Loss: ${trades['avg_loss']:.2f}\n"
            report += f"  Largest Win: ${trades['largest_win']:.2f}\n"
            report += f"  Largest Loss: ${trades['largest_loss']:.2f}\n\n"
        
        # Benchmark comparison
        if 'benchmark_comparison' in metrics:
            bench = metrics['benchmark_comparison']
            report += "Benchmark Comparison:\n"
            report += f"  Beta: {bench['beta']:.3f}\n"
            report += f"  Alpha: {bench['alpha']:.2%}\n"
            report += f"  Information Ratio: {bench['information_ratio']:.3f}\n"
            report += f"  Treynor Ratio: {bench['treynor_ratio']:.3f}\n\n"
        
        return report
