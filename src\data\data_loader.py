"""
Data loading module for handling various data sources and formats.
"""

import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional, Union, Tuple
import yaml
from loguru import logger
import glob
import os


class DataLoader:
    """
    Handles loading data from various sources including CSV files, 
    MetaTrader 5, and external APIs.
    """
    
    def __init__(self, config_path: str = "configs/data_config.yaml"):
        """
        Initialize DataLoader with configuration.
        
        Args:
            config_path: Path to data configuration file
        """
        self.config = self._load_config(config_path)
        self.data_dir = Path(self.config['data_sources']['local_files']['directory'])
        
    def _load_config(self, config_path: str) -> Dict:
        """Load configuration from YAML file."""
        try:
            with open(config_path, 'r') as file:
                return yaml.safe_load(file)
        except FileNotFoundError:
            logger.warning(f"Config file {config_path} not found. Using default settings.")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict:
        """Return default configuration."""
        return {
            'data_sources': {
                'local_files': {
                    'enabled': True,
                    'directory': 'data/raw',
                    'file_patterns': ['*.csv']
                }
            },
            'preprocessing': {
                'cleaning': {
                    'remove_duplicates': True,
                    'handle_missing_values': 'forward_fill',
                    'outlier_detection': {
                        'method': 'z_score',
                        'threshold': 3.0
                    }
                }
            }
        }
    
    def load_csv_file(self, file_path: Union[str, Path]) -> pd.DataFrame:
        """
        Load a single CSV file and standardize the format.
        
        Args:
            file_path: Path to the CSV file
            
        Returns:
            Standardized DataFrame with columns: timestamp, open, high, low, close, volume
        """
        try:
            df = pd.read_csv(file_path)
            logger.info(f"Loaded {len(df)} rows from {file_path}")
            
            # Standardize column names and format
            df = self._standardize_dataframe(df)
            
            return df
            
        except Exception as e:
            logger.error(f"Error loading {file_path}: {str(e)}")
            raise
    
    def _standardize_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Standardize DataFrame format to common schema.
        
        Expected output columns: timestamp, open, high, low, close, volume
        """
        df_copy = df.copy()
        
        # Handle different timestamp column names
        timestamp_cols = ['DATE_TIME', 'date', 'timestamp', 'time', 'datetime']
        timestamp_col = None
        
        for col in timestamp_cols:
            if col in df_copy.columns:
                timestamp_col = col
                break
        
        if timestamp_col:
            # Handle different timestamp formats
            if 'clock' in df_copy.columns:
                # Multi-column timestamp (date + clock)
                df_copy['timestamp'] = pd.to_datetime(
                    df_copy[timestamp_col].astype(str) + ' ' + df_copy['clock'].astype(str)
                )
            else:
                df_copy['timestamp'] = pd.to_datetime(df_copy[timestamp_col])
        else:
            logger.warning("No timestamp column found. Creating index-based timestamps.")
            df_copy['timestamp'] = pd.date_range(
                start='2020-01-01', periods=len(df_copy), freq='15T'
            )
        
        # Standardize price columns
        price_mapping = {
            'OPEN': 'open', 'open_eurusd': 'open', 'Open': 'open',
            'HIGH': 'high', 'high_eurusd': 'high', 'High': 'high',
            'LOW': 'low', 'low_eurusd': 'low', 'Low': 'low',
            'CLOSE': 'close', 'close_eurusd': 'close', 'Close': 'close'
        }
        
        # Rename columns to standard format
        for old_col, new_col in price_mapping.items():
            if old_col in df_copy.columns:
                df_copy[new_col] = df_copy[old_col]
        
        # Handle volume column
        volume_cols = ['volume', 'tikvol_eurusd', 'Volume', 'vol']
        volume_col = None
        
        for col in volume_cols:
            if col in df_copy.columns:
                volume_col = col
                break
        
        if volume_col:
            df_copy['volume'] = df_copy[volume_col]
        else:
            # Create synthetic volume if not available
            df_copy['volume'] = np.random.randint(100, 1000, len(df_copy))
            logger.warning("No volume column found. Created synthetic volume data.")
        
        # Select only required columns
        required_cols = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
        available_cols = [col for col in required_cols if col in df_copy.columns]
        
        if len(available_cols) < 5:  # At least timestamp + OHLC
            raise ValueError(f"Insufficient data columns. Found: {available_cols}")
        
        df_standardized = df_copy[available_cols].copy()
        
        # Sort by timestamp
        df_standardized = df_standardized.sort_values('timestamp').reset_index(drop=True)
        
        return df_standardized
    
    def load_multiple_files(self, pattern: str = "*.csv") -> Dict[str, pd.DataFrame]:
        """
        Load multiple CSV files from the data directory.
        
        Args:
            pattern: File pattern to match
            
        Returns:
            Dictionary with filename as key and DataFrame as value
        """
        file_pattern = self.data_dir / pattern
        files = glob.glob(str(file_pattern))
        
        if not files:
            logger.warning(f"No files found matching pattern: {file_pattern}")
            return {}
        
        data_dict = {}
        
        for file_path in files:
            try:
                filename = Path(file_path).stem
                df = self.load_csv_file(file_path)
                data_dict[filename] = df
                logger.info(f"Successfully loaded {filename}")
                
            except Exception as e:
                logger.error(f"Failed to load {file_path}: {str(e)}")
                continue
        
        return data_dict
    
    def load_currency_pair_data(self, symbol: str) -> Optional[pd.DataFrame]:
        """
        Load data for a specific currency pair.
        
        Args:
            symbol: Currency pair symbol (e.g., 'EURUSD')
            
        Returns:
            DataFrame for the specified symbol or None if not found
        """
        # Try different file naming conventions
        possible_names = [
            f"{symbol}*.csv",
            f"*{symbol}*.csv",
            f"{symbol.lower()}*.csv",
            f"*{symbol.lower()}*.csv"
        ]
        
        for pattern in possible_names:
            files = glob.glob(str(self.data_dir / pattern))
            if files:
                return self.load_csv_file(files[0])
        
        logger.warning(f"No data found for symbol: {symbol}")
        return None
    
    def get_available_symbols(self) -> List[str]:
        """
        Get list of available currency pair symbols from data files.
        
        Returns:
            List of available symbols
        """
        files = glob.glob(str(self.data_dir / "*.csv"))
        symbols = []
        
        for file_path in files:
            filename = Path(file_path).stem
            
            # Extract symbol from filename
            if 'EURUSD' in filename.upper():
                symbols.append('EURUSD')
            elif 'GBPUSD' in filename.upper():
                symbols.append('GBPUSD')
            elif 'USDJPY' in filename.upper():
                symbols.append('USDJPY')
            elif 'AUDUSD' in filename.upper():
                symbols.append('AUDUSD')
            elif 'USDCHF' in filename.upper():
                symbols.append('USDCHF')
            elif 'USDCAD' in filename.upper():
                symbols.append('USDCAD')
            elif 'NZDUSD' in filename.upper():
                symbols.append('NZDUSD')
            elif 'BTC' in filename.upper():
                symbols.append('BTCUSD')
            elif 'XAU' in filename.upper():
                symbols.append('XAUUSD')
        
        return list(set(symbols))
    
    def validate_data_quality(self, df: pd.DataFrame) -> Dict[str, any]:
        """
        Validate data quality and return metrics.
        
        Args:
            df: DataFrame to validate
            
        Returns:
            Dictionary with quality metrics
        """
        metrics = {
            'total_rows': len(df),
            'missing_values': df.isnull().sum().to_dict(),
            'duplicate_rows': df.duplicated().sum(),
            'date_range': {
                'start': df['timestamp'].min(),
                'end': df['timestamp'].max()
            },
            'price_consistency': self._check_price_consistency(df)
        }
        
        return metrics
    
    def _check_price_consistency(self, df: pd.DataFrame) -> Dict[str, int]:
        """Check for price consistency issues."""
        issues = {
            'high_less_than_low': 0,
            'high_less_than_open': 0,
            'high_less_than_close': 0,
            'low_greater_than_open': 0,
            'low_greater_than_close': 0,
            'negative_prices': 0
        }
        
        if all(col in df.columns for col in ['open', 'high', 'low', 'close']):
            issues['high_less_than_low'] = (df['high'] < df['low']).sum()
            issues['high_less_than_open'] = (df['high'] < df['open']).sum()
            issues['high_less_than_close'] = (df['high'] < df['close']).sum()
            issues['low_greater_than_open'] = (df['low'] > df['open']).sum()
            issues['low_greater_than_close'] = (df['low'] > df['close']).sum()
            issues['negative_prices'] = (
                (df['open'] <= 0) | (df['high'] <= 0) | 
                (df['low'] <= 0) | (df['close'] <= 0)
            ).sum()
        
        return issues
