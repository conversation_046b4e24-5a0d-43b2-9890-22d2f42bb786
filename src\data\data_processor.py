"""
Data processing module for cleaning, normalizing, and preprocessing trading data.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Union
from sklearn.preprocessing import MinMaxScaler, StandardScaler, RobustScaler
from scipy import stats
import yaml
from loguru import logger
from pathlib import Path


class DataProcessor:
    """
    Handles data cleaning, normalization, and preprocessing for trading data.
    """
    
    def __init__(self, config_path: str = "configs/data_config.yaml"):
        """
        Initialize DataProcessor with configuration.
        
        Args:
            config_path: Path to data configuration file
        """
        self.config = self._load_config(config_path)
        self.scalers = {}
        
    def _load_config(self, config_path: str) -> Dict:
        """Load configuration from YAML file."""
        try:
            with open(config_path, 'r') as file:
                return yaml.safe_load(file)
        except FileNotFoundError:
            logger.warning(f"Config file {config_path} not found. Using default settings.")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict:
        """Return default configuration."""
        return {
            'preprocessing': {
                'cleaning': {
                    'remove_duplicates': True,
                    'handle_missing_values': 'forward_fill',
                    'outlier_detection': {
                        'method': 'z_score',
                        'threshold': 3.0
                    }
                },
                'normalization': {
                    'method': 'min_max',
                    'feature_range': [0, 1]
                }
            }
        }
    
    def clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Clean the data by handling missing values, duplicates, and outliers.
        
        Args:
            df: Input DataFrame
            
        Returns:
            Cleaned DataFrame
        """
        df_clean = df.copy()
        
        logger.info(f"Starting data cleaning. Initial shape: {df_clean.shape}")
        
        # Remove duplicates
        if self.config['preprocessing']['cleaning']['remove_duplicates']:
            initial_rows = len(df_clean)
            df_clean = df_clean.drop_duplicates()
            removed_duplicates = initial_rows - len(df_clean)
            if removed_duplicates > 0:
                logger.info(f"Removed {removed_duplicates} duplicate rows")
        
        # Handle missing values
        df_clean = self._handle_missing_values(df_clean)
        
        # Detect and handle outliers
        df_clean = self._handle_outliers(df_clean)
        
        # Validate price consistency
        df_clean = self._fix_price_inconsistencies(df_clean)
        
        logger.info(f"Data cleaning completed. Final shape: {df_clean.shape}")
        
        return df_clean
    
    def _handle_missing_values(self, df: pd.DataFrame) -> pd.DataFrame:
        """Handle missing values based on configuration."""
        method = self.config['preprocessing']['cleaning']['handle_missing_values']
        
        missing_count = df.isnull().sum().sum()
        if missing_count == 0:
            return df
        
        logger.info(f"Handling {missing_count} missing values using method: {method}")
        
        if method == 'forward_fill':
            df = df.fillna(method='ffill')
        elif method == 'backward_fill':
            df = df.fillna(method='bfill')
        elif method == 'interpolate':
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            df[numeric_cols] = df[numeric_cols].interpolate(method='linear')
        elif method == 'drop':
            df = df.dropna()
        else:
            logger.warning(f"Unknown missing value method: {method}. Using forward fill.")
            df = df.fillna(method='ffill')
        
        # Fill any remaining missing values with backward fill
        df = df.fillna(method='bfill')
        
        return df
    
    def _handle_outliers(self, df: pd.DataFrame) -> pd.DataFrame:
        """Detect and handle outliers in price data."""
        outlier_config = self.config['preprocessing']['cleaning']['outlier_detection']
        method = outlier_config['method']
        threshold = outlier_config['threshold']
        
        price_cols = ['open', 'high', 'low', 'close']
        available_price_cols = [col for col in price_cols if col in df.columns]
        
        if not available_price_cols:
            return df
        
        df_clean = df.copy()
        total_outliers = 0
        
        for col in available_price_cols:
            if method == 'z_score':
                z_scores = np.abs(stats.zscore(df_clean[col]))
                outliers = z_scores > threshold
            elif method == 'iqr':
                Q1 = df_clean[col].quantile(0.25)
                Q3 = df_clean[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - threshold * IQR
                upper_bound = Q3 + threshold * IQR
                outliers = (df_clean[col] < lower_bound) | (df_clean[col] > upper_bound)
            else:
                continue
            
            outlier_count = outliers.sum()
            if outlier_count > 0:
                # Replace outliers with median value
                median_value = df_clean[col].median()
                df_clean.loc[outliers, col] = median_value
                total_outliers += outlier_count
        
        if total_outliers > 0:
            logger.info(f"Handled {total_outliers} outliers using {method} method")
        
        return df_clean
    
    def _fix_price_inconsistencies(self, df: pd.DataFrame) -> pd.DataFrame:
        """Fix price consistency issues (e.g., high < low)."""
        if not all(col in df.columns for col in ['open', 'high', 'low', 'close']):
            return df
        
        df_fixed = df.copy()
        
        # Fix high < low
        inconsistent_hl = df_fixed['high'] < df_fixed['low']
        if inconsistent_hl.any():
            # Swap high and low values
            df_fixed.loc[inconsistent_hl, ['high', 'low']] = df_fixed.loc[inconsistent_hl, ['low', 'high']].values
            logger.info(f"Fixed {inconsistent_hl.sum()} high < low inconsistencies")
        
        # Ensure high is the maximum of all prices
        df_fixed['high'] = df_fixed[['open', 'high', 'low', 'close']].max(axis=1)
        
        # Ensure low is the minimum of all prices
        df_fixed['low'] = df_fixed[['open', 'high', 'low', 'close']].min(axis=1)
        
        return df_fixed

    def normalize_data(self, df: pd.DataFrame, fit_scaler: bool = True) -> pd.DataFrame:
        """
        Normalize the data using the specified method.

        Args:
            df: Input DataFrame
            fit_scaler: Whether to fit the scaler (True for training data)

        Returns:
            Normalized DataFrame
        """
        norm_config = self.config['preprocessing']['normalization']
        method = norm_config['method']

        df_norm = df.copy()

        # Columns to normalize (exclude timestamp)
        numeric_cols = df_norm.select_dtypes(include=[np.number]).columns.tolist()
        if 'timestamp' in numeric_cols:
            numeric_cols.remove('timestamp')

        if not numeric_cols:
            logger.warning("No numeric columns found for normalization")
            return df_norm

        # Initialize scaler if not exists
        if method not in self.scalers or fit_scaler:
            if method == 'min_max':
                feature_range = norm_config.get('feature_range', [0, 1])
                self.scalers[method] = MinMaxScaler(feature_range=feature_range)
            elif method == 'standard':
                self.scalers[method] = StandardScaler()
            elif method == 'robust':
                self.scalers[method] = RobustScaler()
            else:
                logger.warning(f"Unknown normalization method: {method}. Using min_max.")
                self.scalers[method] = MinMaxScaler()

        scaler = self.scalers[method]

        if fit_scaler:
            df_norm[numeric_cols] = scaler.fit_transform(df_norm[numeric_cols])
            logger.info(f"Fitted and transformed data using {method} normalization")
        else:
            df_norm[numeric_cols] = scaler.transform(df_norm[numeric_cols])
            logger.info(f"Transformed data using existing {method} scaler")

        return df_norm

    def denormalize_data(self, df: pd.DataFrame, method: str = 'min_max') -> pd.DataFrame:
        """
        Denormalize the data back to original scale.

        Args:
            df: Normalized DataFrame
            method: Normalization method used

        Returns:
            Denormalized DataFrame
        """
        if method not in self.scalers:
            logger.error(f"Scaler for method {method} not found. Cannot denormalize.")
            return df

        df_denorm = df.copy()
        scaler = self.scalers[method]

        # Get numeric columns (exclude timestamp)
        numeric_cols = df_denorm.select_dtypes(include=[np.number]).columns.tolist()
        if 'timestamp' in numeric_cols:
            numeric_cols.remove('timestamp')

        if numeric_cols:
            df_denorm[numeric_cols] = scaler.inverse_transform(df_denorm[numeric_cols])
            logger.info(f"Denormalized data using {method} scaler")

        return df_denorm

    def create_sequences(self, df: pd.DataFrame, sequence_length: int = 60,
                        target_column: str = 'close') -> Tuple[np.ndarray, np.ndarray]:
        """
        Create sequences for time series modeling.

        Args:
            df: Input DataFrame
            sequence_length: Length of input sequences
            target_column: Column to predict

        Returns:
            Tuple of (X, y) arrays for training
        """
        if target_column not in df.columns:
            raise ValueError(f"Target column '{target_column}' not found in DataFrame")

        # Get feature columns (exclude timestamp and target)
        feature_cols = [col for col in df.columns
                       if col not in ['timestamp', target_column]]

        if not feature_cols:
            logger.warning("No feature columns found. Using all numeric columns.")
            feature_cols = df.select_dtypes(include=[np.number]).columns.tolist()
            if target_column in feature_cols:
                feature_cols.remove(target_column)

        # Prepare data
        features = df[feature_cols].values
        targets = df[target_column].values

        X, y = [], []

        for i in range(sequence_length, len(features)):
            X.append(features[i-sequence_length:i])
            y.append(targets[i])

        X = np.array(X)
        y = np.array(y)

        logger.info(f"Created {len(X)} sequences of length {sequence_length}")
        logger.info(f"Feature shape: {X.shape}, Target shape: {y.shape}")

        return X, y

    def split_data(self, df: pd.DataFrame,
                   train_ratio: float = 0.7,
                   val_ratio: float = 0.15,
                   test_ratio: float = 0.15) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """
        Split data into train, validation, and test sets.

        Args:
            df: Input DataFrame
            train_ratio: Ratio for training data
            val_ratio: Ratio for validation data
            test_ratio: Ratio for test data

        Returns:
            Tuple of (train_df, val_df, test_df)
        """
        if abs(train_ratio + val_ratio + test_ratio - 1.0) > 1e-6:
            raise ValueError("Ratios must sum to 1.0")

        n = len(df)
        train_end = int(n * train_ratio)
        val_end = int(n * (train_ratio + val_ratio))

        train_df = df.iloc[:train_end].copy()
        val_df = df.iloc[train_end:val_end].copy()
        test_df = df.iloc[val_end:].copy()

        logger.info(f"Data split - Train: {len(train_df)}, Val: {len(val_df)}, Test: {len(test_df)}")

        return train_df, val_df, test_df

    def add_returns(self, df: pd.DataFrame, price_column: str = 'close') -> pd.DataFrame:
        """
        Add return columns to the DataFrame.

        Args:
            df: Input DataFrame
            price_column: Column to calculate returns from

        Returns:
            DataFrame with added return columns
        """
        df_returns = df.copy()

        if price_column not in df_returns.columns:
            logger.warning(f"Price column '{price_column}' not found")
            return df_returns

        # Simple returns
        df_returns['returns'] = df_returns[price_column].pct_change()

        # Log returns
        df_returns['log_returns'] = np.log(df_returns[price_column] / df_returns[price_column].shift(1))

        # Cumulative returns
        df_returns['cumulative_returns'] = (1 + df_returns['returns']).cumprod() - 1

        logger.info("Added return columns: returns, log_returns, cumulative_returns")

        return df_returns

    def process_pipeline(self, df: pd.DataFrame, fit_scaler: bool = True) -> pd.DataFrame:
        """
        Run the complete data processing pipeline.

        Args:
            df: Input DataFrame
            fit_scaler: Whether to fit the scaler

        Returns:
            Processed DataFrame
        """
        logger.info("Starting data processing pipeline")

        # Step 1: Clean data
        df_processed = self.clean_data(df)

        # Step 2: Add returns
        df_processed = self.add_returns(df_processed)

        # Step 3: Normalize data
        df_processed = self.normalize_data(df_processed, fit_scaler=fit_scaler)

        logger.info("Data processing pipeline completed")

        return df_processed
