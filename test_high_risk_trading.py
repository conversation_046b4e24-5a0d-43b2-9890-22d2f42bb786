#!/usr/bin/env python3
"""
Test script for high-risk trading configuration with trailing stops.
"""

import pandas as pd
import numpy as np
from pathlib import Path
import sys
import json
from datetime import datetime

# Add src to path
sys.path.append(str(Path(__file__).parent / "src" / "risk_management"))

# Import risk management modules
from risk_manager import RiskManager, TrailingStopManager


def test_high_risk_configuration():
    """Test the new high-risk configuration."""
    print("\n" + "="*60)
    print("🚨 HIGH-RISK TRADING CONFIGURATION TEST")
    print("="*60)
    print("⚠️  WARNING: 30% RISK PER TRADE IS EXTREMELY DANGEROUS!")
    print("⚠️  THIS COULD WIPE OUT YOUR ACCOUNT IN 3-4 LOSING TRADES!")
    print("="*60)
    
    # Initialize risk manager with high-risk config
    high_risk_config = {
        'position_sizing': {
            'method': 'fixed_percentage',
            'risk_per_trade': 0.30,  # 30% risk per trade!
            'max_position_size': 0.50,
            'min_position_size': 0.001
        },
        'stop_loss': {
            'method': 'atr_based',
            'atr_multiplier': 2.0,
            'max_stop_loss': 0.05
        },
        'take_profit': {
            'method': 'trailing',
            'trailing_percentage': 0.05,  # 5% trailing distance
            'trailing_activation': 0.02,  # Activate after 2% profit
            'breakeven_buffer': 0.005
        },
        'portfolio_limits': {
            'max_daily_loss': 0.50,  # 50% max daily loss!
            'max_drawdown': 0.70,    # 70% max drawdown!
            'max_positions': 5,
            'max_exposure_per_symbol': 0.50  # 50% max exposure per symbol
        }
    }
    
    risk_manager = RiskManager(high_risk_config)
    
    print(f"✅ High-risk configuration loaded:")
    print(f"   Risk per trade: {high_risk_config['position_sizing']['risk_per_trade']:.0%}")
    print(f"   Max daily loss: {high_risk_config['portfolio_limits']['max_daily_loss']:.0%}")
    print(f"   Max drawdown: {high_risk_config['portfolio_limits']['max_drawdown']:.0%}")
    
    return risk_manager


def test_extreme_position_sizing():
    """Test position sizing with 30% risk."""
    print("\n" + "="*50)
    print("💰 TESTING EXTREME POSITION SIZING (30% RISK)")
    print("="*50)
    
    risk_manager = test_high_risk_configuration()
    
    # Test scenarios
    account_balance = 10000.0
    entry_price = 1.1000
    stop_loss = 1.0950  # 50 pips stop
    symbol = 'EURUSD'
    
    position_size = risk_manager.calculate_position_size(
        symbol, entry_price, stop_loss, account_balance
    )
    
    position_value = position_size * entry_price
    risk_amount = abs(entry_price - stop_loss) * position_size
    
    print(f"📊 Position Sizing Results:")
    print(f"   Account balance: ${account_balance:,.2f}")
    print(f"   Entry price: {entry_price:.4f}")
    print(f"   Stop loss: {stop_loss:.4f}")
    print(f"   Stop distance: {abs(entry_price - stop_loss):.4f} ({abs(entry_price - stop_loss)/entry_price:.1%})")
    print(f"   Position size: {position_size:,.2f} units")
    print(f"   Position value: ${position_value:,.2f} ({position_value/account_balance:.1%} of account)")
    print(f"   Risk amount: ${risk_amount:,.2f} ({risk_amount/account_balance:.1%} of account)")
    
    print(f"\n🚨 RISK ANALYSIS:")
    if risk_amount/account_balance > 0.25:
        print(f"   ❌ EXTREME RISK: This trade risks {risk_amount/account_balance:.1%} of your account!")
    if position_value/account_balance > 0.30:
        print(f"   ❌ HIGH LEVERAGE: Position is {position_value/account_balance:.1%} of account!")
    
    return position_size, risk_amount


def test_trailing_stop_system():
    """Test the advanced trailing stop system."""
    print("\n" + "="*50)
    print("📈 TESTING ADVANCED TRAILING STOP SYSTEM")
    print("="*50)
    
    # Initialize trailing stop manager
    trailing_config = {
        'trailing_percentage': 0.05,  # 5% trailing distance
        'activation_threshold': 0.02,  # Activate after 2% profit
        'breakeven_buffer': 0.005     # 0.5% buffer above breakeven
    }
    
    trail_manager = TrailingStopManager(trailing_config)
    
    # Simulate a winning trade
    position_id = "EURUSD_001"
    entry_price = 1.1000
    direction = "buy"
    initial_stop = 1.0950
    
    # Initialize trailing stop
    trail_data = trail_manager.initialize_trailing_stop(
        position_id, entry_price, direction, initial_stop
    )
    
    print(f"🎯 Trailing Stop Initialized:")
    print(f"   Position: {position_id}")
    print(f"   Entry price: {entry_price:.4f}")
    print(f"   Initial stop: {initial_stop:.4f}")
    print(f"   Direction: {direction}")
    print(f"   Activation threshold: {trailing_config['activation_threshold']:.1%}")
    print(f"   Trailing distance: {trailing_config['trailing_percentage']:.1%}")
    
    # Simulate price movement
    price_scenarios = [
        1.1010,  # +0.09% - not enough to activate
        1.1050,  # +0.45% - not enough to activate
        1.1220,  # +2.00% - should activate trailing
        1.1250,  # +2.27% - should update trailing stop
        1.1280,  # +2.55% - should update trailing stop again
        1.1260,  # +2.36% - price pullback, stop should not move
        1.1300,  # +2.73% - new high, should update stop
        1.1180,  # +1.64% - significant pullback, might hit trailing stop
    ]
    
    print(f"\n📊 Price Movement Simulation:")
    print(f"{'Price':<8} {'Profit%':<8} {'Stop':<8} {'Trailing':<10} {'Updated':<8}")
    print("-" * 50)
    
    for current_price in price_scenarios:
        update_result = trail_manager.update_trailing_stop(position_id, current_price)
        
        profit_pct = update_result['profit_pct']
        new_stop = update_result['new_stop']
        trailing_active = update_result['trailing_active']
        stop_updated = update_result['stop_updated']
        
        status = "🟢 ACTIVE" if trailing_active else "⚪ INACTIVE"
        updated = "✅ YES" if stop_updated else "❌ NO"
        
        print(f"{current_price:<8.4f} {profit_pct:<7.1%} {new_stop:<8.4f} {status:<10} {updated:<8}")
        
        # Check if stop would be hit
        if direction == "buy" and current_price <= new_stop:
            print(f"🛑 STOP LOSS HIT! Position would be closed at {new_stop:.4f}")
            break
    
    # Get final summary
    summary = trail_manager.get_trailing_summary(position_id)
    
    print(f"\n📋 Final Trailing Stop Summary:")
    print(f"   Total trail updates: {summary['trail_updates']}")
    print(f"   Total trail distance: {summary['total_trail_distance']:.4f}")
    print(f"   Final profit: {summary['current_profit_pct']:.2%}")
    print(f"   Highest price reached: {summary['highest_price']:.4f}")
    
    return trail_manager


def test_portfolio_risk_limits():
    """Test portfolio risk limits with high-risk settings."""
    print("\n" + "="*50)
    print("🏦 TESTING PORTFOLIO RISK LIMITS")
    print("="*50)
    
    risk_manager = test_high_risk_configuration()
    
    # Simulate current portfolio
    current_positions = [
        {'symbol': 'EURUSD', 'value': 4000.0, 'pnl': -1500.0},  # Big loss
        {'symbol': 'GBPUSD', 'value': 3000.0, 'pnl': -800.0},   # Another loss
    ]
    
    account_info = {
        'balance': 10000.0,
        'equity': 6700.0,  # After losses
    }
    
    # Test daily loss limit
    current_pnl = sum(pos['pnl'] for pos in current_positions)
    loss_check = risk_manager.check_daily_loss_limit(current_pnl, account_info['balance'])
    
    print(f"📊 Portfolio Status:")
    print(f"   Account balance: ${account_info['balance']:,.2f}")
    print(f"   Current equity: ${account_info['equity']:,.2f}")
    print(f"   Current P&L: ${current_pnl:,.2f}")
    print(f"   Loss percentage: {abs(current_pnl)/account_info['balance']:.1%}")
    
    print(f"\n🚨 Risk Limit Check:")
    print(f"   Max daily loss limit: {risk_manager.config['portfolio_limits']['max_daily_loss']:.0%}")
    print(f"   Current loss: {abs(current_pnl)/account_info['balance']:.1%}")
    
    if loss_check['limit_exceeded']:
        print(f"   ❌ DAILY LOSS LIMIT EXCEEDED!")
        print(f"   🛑 TRADING SHOULD BE STOPPED!")
    else:
        print(f"   ✅ Within daily loss limits")
        print(f"   Remaining loss allowance: ${loss_check['remaining_loss_allowance']:,.2f}")
    
    # Test new position
    new_position = {'symbol': 'USDJPY', 'value': 5000.0}
    portfolio_check = risk_manager.check_portfolio_limits(
        new_position, current_positions, account_info['balance']
    )
    
    print(f"\n🔍 New Position Check:")
    print(f"   New position value: ${new_position['value']:,.2f}")
    print(f"   Max exposure per symbol: {risk_manager.config['portfolio_limits']['max_exposure_per_symbol']:.0%}")
    
    if portfolio_check['allowed']:
        print(f"   ✅ New position APPROVED")
    else:
        print(f"   ❌ New position REJECTED")
        for violation in portfolio_check['violations']:
            print(f"      - {violation}")


def demonstrate_risk_scenarios():
    """Demonstrate various risk scenarios."""
    print("\n" + "="*60)
    print("⚠️  RISK SCENARIO DEMONSTRATIONS")
    print("="*60)
    
    scenarios = [
        {
            'name': 'Conservative Trader',
            'risk_per_trade': 0.01,
            'account': 10000,
            'description': 'Normal 1% risk per trade'
        },
        {
            'name': 'Aggressive Trader', 
            'risk_per_trade': 0.05,
            'account': 10000,
            'description': 'High 5% risk per trade'
        },
        {
            'name': 'YOUR SETTINGS',
            'risk_per_trade': 0.30,
            'account': 10000,
            'description': 'EXTREME 30% risk per trade'
        }
    ]
    
    print(f"💸 Account Destruction Scenarios:")
    print(f"{'Trader Type':<20} {'Risk%':<8} {'Trades to Ruin':<15} {'Probability':<12}")
    print("-" * 60)
    
    for scenario in scenarios:
        risk_pct = scenario['risk_per_trade']
        
        # Calculate trades to ruin (simplified)
        trades_to_ruin = int(1 / risk_pct) if risk_pct > 0 else float('inf')
        
        # Probability of ruin with 50% win rate
        if trades_to_ruin < 10:
            prob_ruin = "VERY HIGH"
        elif trades_to_ruin < 20:
            prob_ruin = "HIGH"
        elif trades_to_ruin < 50:
            prob_ruin = "MODERATE"
        else:
            prob_ruin = "LOW"
        
        marker = "🚨" if risk_pct >= 0.20 else "⚠️" if risk_pct >= 0.05 else "✅"
        
        print(f"{marker} {scenario['name']:<18} {risk_pct:<7.0%} {trades_to_ruin:<15} {prob_ruin:<12}")
    
    print(f"\n🎯 Key Insights:")
    print(f"   • With 30% risk per trade, just 4 consecutive losses = account gone")
    print(f"   • Even with 60% win rate, high risk leads to eventual ruin")
    print(f"   • Professional traders typically risk 1-2% per trade")
    print(f"   • Your settings are suitable only for very small test accounts")


def main():
    """Main test function."""
    print("🤖 AI Trading Bot - High-Risk Configuration Test")
    print("="*60)
    
    try:
        # Test high-risk configuration
        test_high_risk_configuration()
        
        # Test extreme position sizing
        test_extreme_position_sizing()
        
        # Test trailing stop system
        test_trailing_stop_system()
        
        # Test portfolio risk limits
        test_portfolio_risk_limits()
        
        # Demonstrate risk scenarios
        demonstrate_risk_scenarios()
        
        print("\n" + "="*60)
        print("🎯 HIGH-RISK CONFIGURATION TEST COMPLETED")
        print("="*60)
        
        print(f"\n🚨 FINAL WARNING:")
        print(f"   Your bot is now configured for EXTREME RISK trading!")
        print(f"   • 30% risk per trade")
        print(f"   • 50% max daily loss")
        print(f"   • 70% max drawdown")
        print(f"   • Advanced trailing stops enabled")
        
        print(f"\n💡 RECOMMENDATIONS:")
        print(f"   1. Start with a VERY SMALL test account")
        print(f"   2. Monitor every single trade closely")
        print(f"   3. Be prepared for rapid account swings")
        print(f"   4. Consider reducing risk after testing")
        
        print(f"\n🎮 Your bot is ready for high-risk trading!")
        print(f"   Use: python start_trading_bot.py")
        
    except Exception as e:
        print(f"❌ Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
