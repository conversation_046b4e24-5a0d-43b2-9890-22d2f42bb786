"""
Supervised learning models for trading prediction.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any, Union
import pickle
from pathlib import Path


class SupervisedModels:
    """
    Implements various supervised learning models for trading predictions.
    """
    
    def __init__(self):
        """Initialize SupervisedModels."""
        self.models = {}
        self.scalers = {}
        self.feature_names = []
        self.model_performance = {}
        
    def prepare_data(self, df: pd.DataFrame, target_column: str = 'close', 
                    prediction_horizon: int = 1, task_type: str = 'regression') -> Tuple[np.ndarray, np.ndarray]:
        """
        Prepare data for supervised learning.
        
        Args:
            df: Input DataFrame with features
            target_column: Column to predict
            prediction_horizon: Number of periods ahead to predict
            task_type: 'regression' or 'classification'
            
        Returns:
            Tuple of (X, y) arrays
        """
        # Remove non-feature columns
        feature_cols = [col for col in df.columns 
                       if col not in ['timestamp', target_column]]
        
        X = df[feature_cols].values
        
        if task_type == 'regression':
            # Predict future price
            y = df[target_column].shift(-prediction_horizon).values
        else:
            # Predict price direction (up/down)
            future_price = df[target_column].shift(-prediction_horizon)
            current_price = df[target_column]
            y = (future_price > current_price).astype(int).values
        
        # Remove rows with NaN values
        valid_indices = ~(np.isnan(X).any(axis=1) | np.isnan(y))
        X = X[valid_indices]
        y = y[valid_indices]
        
        self.feature_names = feature_cols
        
        return X, y
    
    def split_data(self, X: np.ndarray, y: np.ndarray, 
                   train_ratio: float = 0.7, val_ratio: float = 0.15) -> Tuple:
        """
        Split data into train, validation, and test sets.
        
        Args:
            X: Feature array
            y: Target array
            train_ratio: Ratio for training data
            val_ratio: Ratio for validation data
            
        Returns:
            Tuple of (X_train, X_val, X_test, y_train, y_val, y_test)
        """
        n = len(X)
        train_end = int(n * train_ratio)
        val_end = int(n * (train_ratio + val_ratio))
        
        X_train = X[:train_end]
        X_val = X[train_end:val_end]
        X_test = X[val_end:]
        
        y_train = y[:train_end]
        y_val = y[train_end:val_end]
        y_test = y[val_end:]
        
        return X_train, X_val, X_test, y_train, y_val, y_test
    
    def train_linear_regression(self, X_train: np.ndarray, y_train: np.ndarray,
                               regularization: str = 'ridge', alpha: float = 1.0) -> Dict:
        """
        Train linear regression model.
        
        Args:
            X_train: Training features
            y_train: Training targets
            regularization: Type of regularization ('ridge', 'lasso', 'elastic')
            alpha: Regularization strength
            
        Returns:
            Model performance metrics
        """
        try:
            from sklearn.linear_model import LinearRegression, Ridge, Lasso, ElasticNet
            from sklearn.metrics import mean_squared_error, r2_score
            
            if regularization == 'ridge':
                model = Ridge(alpha=alpha)
            elif regularization == 'lasso':
                model = Lasso(alpha=alpha)
            elif regularization == 'elastic':
                model = ElasticNet(alpha=alpha)
            else:
                model = LinearRegression()
            
            model.fit(X_train, y_train)
            
            # Make predictions
            y_pred = model.predict(X_train)
            
            # Calculate metrics
            mse = mean_squared_error(y_train, y_pred)
            r2 = r2_score(y_train, y_pred)
            
            self.models['linear_regression'] = model
            
            performance = {
                'model_type': 'linear_regression',
                'mse': mse,
                'rmse': np.sqrt(mse),
                'r2': r2,
                'regularization': regularization,
                'alpha': alpha
            }
            
            self.model_performance['linear_regression'] = performance
            
            return performance
            
        except ImportError:
            print("scikit-learn not available. Using simple linear regression.")
            return self._simple_linear_regression(X_train, y_train)
    
    def _simple_linear_regression(self, X_train: np.ndarray, y_train: np.ndarray) -> Dict:
        """Simple linear regression implementation."""
        # Add bias term
        X_with_bias = np.column_stack([np.ones(X_train.shape[0]), X_train])
        
        # Normal equation: theta = (X^T X)^-1 X^T y
        try:
            theta = np.linalg.solve(X_with_bias.T @ X_with_bias, X_with_bias.T @ y_train)
        except np.linalg.LinAlgError:
            # Use pseudo-inverse if matrix is singular
            theta = np.linalg.pinv(X_with_bias.T @ X_with_bias) @ X_with_bias.T @ y_train
        
        # Store model parameters
        self.models['linear_regression'] = {
            'type': 'simple_linear',
            'theta': theta,
            'feature_names': self.feature_names
        }
        
        # Calculate predictions and metrics
        y_pred = X_with_bias @ theta
        mse = np.mean((y_train - y_pred) ** 2)
        ss_res = np.sum((y_train - y_pred) ** 2)
        ss_tot = np.sum((y_train - np.mean(y_train)) ** 2)
        r2 = 1 - (ss_res / ss_tot)
        
        performance = {
            'model_type': 'simple_linear_regression',
            'mse': mse,
            'rmse': np.sqrt(mse),
            'r2': r2
        }
        
        self.model_performance['linear_regression'] = performance
        
        return performance
    
    def train_random_forest(self, X_train: np.ndarray, y_train: np.ndarray,
                           n_estimators: int = 100, max_depth: int = 10,
                           min_samples_split: int = 5, task_type: str = 'regression') -> Dict:
        """
        Train random forest model.
        
        Args:
            X_train: Training features
            y_train: Training targets
            n_estimators: Number of trees
            max_depth: Maximum depth of trees
            min_samples_split: Minimum samples to split
            task_type: 'regression' or 'classification'
            
        Returns:
            Model performance metrics
        """
        try:
            from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
            from sklearn.metrics import mean_squared_error, r2_score, accuracy_score, classification_report
            
            if task_type == 'regression':
                model = RandomForestRegressor(
                    n_estimators=n_estimators,
                    max_depth=max_depth,
                    min_samples_split=min_samples_split,
                    random_state=42
                )
            else:
                model = RandomForestClassifier(
                    n_estimators=n_estimators,
                    max_depth=max_depth,
                    min_samples_split=min_samples_split,
                    random_state=42
                )
            
            model.fit(X_train, y_train)
            y_pred = model.predict(X_train)
            
            self.models['random_forest'] = model
            
            if task_type == 'regression':
                mse = mean_squared_error(y_train, y_pred)
                r2 = r2_score(y_train, y_pred)
                
                performance = {
                    'model_type': 'random_forest_regression',
                    'mse': mse,
                    'rmse': np.sqrt(mse),
                    'r2': r2,
                    'feature_importance': dict(zip(self.feature_names, model.feature_importances_))
                }
            else:
                accuracy = accuracy_score(y_train, y_pred)
                
                performance = {
                    'model_type': 'random_forest_classification',
                    'accuracy': accuracy,
                    'feature_importance': dict(zip(self.feature_names, model.feature_importances_))
                }
            
            self.model_performance['random_forest'] = performance
            
            return performance
            
        except ImportError:
            print("scikit-learn not available. Random Forest not implemented.")
            return {'error': 'scikit-learn required for Random Forest'}
    
    def train_gradient_boosting(self, X_train: np.ndarray, y_train: np.ndarray,
                               n_estimators: int = 100, learning_rate: float = 0.1,
                               max_depth: int = 6, task_type: str = 'regression') -> Dict:
        """
        Train gradient boosting model (XGBoost if available, otherwise sklearn).
        
        Args:
            X_train: Training features
            y_train: Training targets
            n_estimators: Number of boosting rounds
            learning_rate: Learning rate
            max_depth: Maximum depth of trees
            task_type: 'regression' or 'classification'
            
        Returns:
            Model performance metrics
        """
        # Try XGBoost first
        try:
            import xgboost as xgb
            from sklearn.metrics import mean_squared_error, r2_score, accuracy_score
            
            if task_type == 'regression':
                model = xgb.XGBRegressor(
                    n_estimators=n_estimators,
                    learning_rate=learning_rate,
                    max_depth=max_depth,
                    random_state=42
                )
            else:
                model = xgb.XGBClassifier(
                    n_estimators=n_estimators,
                    learning_rate=learning_rate,
                    max_depth=max_depth,
                    random_state=42
                )
            
            model.fit(X_train, y_train)
            y_pred = model.predict(X_train)
            
            self.models['xgboost'] = model
            
            if task_type == 'regression':
                mse = mean_squared_error(y_train, y_pred)
                r2 = r2_score(y_train, y_pred)
                
                performance = {
                    'model_type': 'xgboost_regression',
                    'mse': mse,
                    'rmse': np.sqrt(mse),
                    'r2': r2,
                    'feature_importance': dict(zip(self.feature_names, model.feature_importances_))
                }
            else:
                accuracy = accuracy_score(y_train, y_pred)
                
                performance = {
                    'model_type': 'xgboost_classification',
                    'accuracy': accuracy,
                    'feature_importance': dict(zip(self.feature_names, model.feature_importances_))
                }
            
            self.model_performance['xgboost'] = performance
            
            return performance
            
        except ImportError:
            # Fall back to sklearn GradientBoosting
            try:
                from sklearn.ensemble import GradientBoostingRegressor, GradientBoostingClassifier
                from sklearn.metrics import mean_squared_error, r2_score, accuracy_score
                
                if task_type == 'regression':
                    model = GradientBoostingRegressor(
                        n_estimators=n_estimators,
                        learning_rate=learning_rate,
                        max_depth=max_depth,
                        random_state=42
                    )
                else:
                    model = GradientBoostingClassifier(
                        n_estimators=n_estimators,
                        learning_rate=learning_rate,
                        max_depth=max_depth,
                        random_state=42
                    )
                
                model.fit(X_train, y_train)
                y_pred = model.predict(X_train)
                
                self.models['gradient_boosting'] = model
                
                if task_type == 'regression':
                    mse = mean_squared_error(y_train, y_pred)
                    r2 = r2_score(y_train, y_pred)
                    
                    performance = {
                        'model_type': 'gradient_boosting_regression',
                        'mse': mse,
                        'rmse': np.sqrt(mse),
                        'r2': r2,
                        'feature_importance': dict(zip(self.feature_names, model.feature_importances_))
                    }
                else:
                    accuracy = accuracy_score(y_train, y_pred)
                    
                    performance = {
                        'model_type': 'gradient_boosting_classification',
                        'accuracy': accuracy,
                        'feature_importance': dict(zip(self.feature_names, model.feature_importances_))
                    }
                
                self.model_performance['gradient_boosting'] = performance
                
                return performance
                
            except ImportError:
                print("Neither XGBoost nor scikit-learn available for gradient boosting.")
                return {'error': 'Gradient boosting libraries not available'}
    
    def predict(self, model_name: str, X: np.ndarray) -> np.ndarray:
        """
        Make predictions using a trained model.
        
        Args:
            model_name: Name of the model to use
            X: Features to predict on
            
        Returns:
            Predictions array
        """
        if model_name not in self.models:
            raise ValueError(f"Model {model_name} not found. Available models: {list(self.models.keys())}")
        
        model = self.models[model_name]
        
        if isinstance(model, dict) and model.get('type') == 'simple_linear':
            # Handle simple linear regression
            X_with_bias = np.column_stack([np.ones(X.shape[0]), X])
            return X_with_bias @ model['theta']
        else:
            # Handle sklearn/xgboost models
            return model.predict(X)
    
    def evaluate_model(self, model_name: str, X_test: np.ndarray, y_test: np.ndarray,
                      task_type: str = 'regression') -> Dict:
        """
        Evaluate a trained model on test data.
        
        Args:
            model_name: Name of the model to evaluate
            X_test: Test features
            y_test: Test targets
            task_type: 'regression' or 'classification'
            
        Returns:
            Evaluation metrics
        """
        y_pred = self.predict(model_name, X_test)
        
        if task_type == 'regression':
            mse = np.mean((y_test - y_pred) ** 2)
            rmse = np.sqrt(mse)
            mae = np.mean(np.abs(y_test - y_pred))
            
            # R-squared
            ss_res = np.sum((y_test - y_pred) ** 2)
            ss_tot = np.sum((y_test - np.mean(y_test)) ** 2)
            r2 = 1 - (ss_res / ss_tot)
            
            return {
                'mse': mse,
                'rmse': rmse,
                'mae': mae,
                'r2': r2
            }
        else:
            accuracy = np.mean(y_test == y_pred)
            
            # Precision, Recall, F1 for binary classification
            if len(np.unique(y_test)) == 2:
                tp = np.sum((y_test == 1) & (y_pred == 1))
                fp = np.sum((y_test == 0) & (y_pred == 1))
                fn = np.sum((y_test == 1) & (y_pred == 0))
                
                precision = tp / (tp + fp) if (tp + fp) > 0 else 0
                recall = tp / (tp + fn) if (tp + fn) > 0 else 0
                f1 = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0
                
                return {
                    'accuracy': accuracy,
                    'precision': precision,
                    'recall': recall,
                    'f1': f1
                }
            else:
                return {'accuracy': accuracy}
    
    def save_models(self, save_dir: str = "models"):
        """Save trained models to disk."""
        save_path = Path(save_dir)
        save_path.mkdir(exist_ok=True)
        
        for model_name, model in self.models.items():
            model_file = save_path / f"{model_name}.pkl"
            with open(model_file, 'wb') as f:
                pickle.dump(model, f)
        
        # Save performance metrics
        performance_file = save_path / "model_performance.pkl"
        with open(performance_file, 'wb') as f:
            pickle.dump(self.model_performance, f)
        
        print(f"Saved {len(self.models)} models to {save_path}")
    
    def load_models(self, save_dir: str = "models"):
        """Load trained models from disk."""
        save_path = Path(save_dir)
        
        if not save_path.exists():
            print(f"Model directory {save_path} does not exist")
            return
        
        # Load models
        for model_file in save_path.glob("*.pkl"):
            if model_file.name != "model_performance.pkl":
                model_name = model_file.stem
                with open(model_file, 'rb') as f:
                    self.models[model_name] = pickle.load(f)
        
        # Load performance metrics
        performance_file = save_path / "model_performance.pkl"
        if performance_file.exists():
            with open(performance_file, 'rb') as f:
                self.model_performance = pickle.load(f)
        
        print(f"Loaded {len(self.models)} models from {save_path}")
    
    def get_model_summary(self) -> Dict:
        """Get summary of all trained models."""
        summary = {
            'total_models': len(self.models),
            'model_names': list(self.models.keys()),
            'performance': self.model_performance
        }
        
        return summary
