#!/usr/bin/env python3
"""
Deployment script for AI Trading Bot.
"""

import os
import sys
import json
import shutil
import subprocess
from pathlib import Path
from datetime import datetime


class TradingBotDeployer:
    """
    Automated deployment system for the AI Trading Bot.
    """
    
    def __init__(self, config_path: str = "config/config_template.json"):
        """Initialize deployer with configuration."""
        self.config_path = Path(config_path)
        self.project_root = Path(__file__).parent.parent
        self.deployment_log = []
        
    def log(self, message: str, level: str = "INFO"):
        """Log deployment messages."""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}"
        self.deployment_log.append(log_entry)
        print(log_entry)
    
    def check_prerequisites(self) -> bool:
        """Check if all prerequisites are met."""
        self.log("Checking deployment prerequisites...")
        
        checks = {
            "Python version": sys.version_info >= (3, 8),
            "Config file exists": self.config_path.exists(),
            "Data directory": (self.project_root / "data").exists(),
            "Source code": (self.project_root / "src").exists(),
        }
        
        all_passed = True
        for check_name, passed in checks.items():
            status = "[PASS]" if passed else "[FAIL]"
            self.log(f"{check_name}: {status}")
            if not passed:
                all_passed = False
        
        return all_passed
    
    def install_dependencies(self) -> bool:
        """Install required dependencies."""
        self.log("Installing dependencies...")
        
        try:
            # Check if requirements.txt exists
            requirements_file = self.project_root / "requirements.txt"
            if not requirements_file.exists():
                self.log("requirements.txt not found", "WARNING")
                return True
            
            # Install dependencies
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                self.log("Dependencies installed successfully")
                return True
            else:
                self.log(f"Failed to install dependencies: {result.stderr}", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"Error installing dependencies: {str(e)}", "ERROR")
            return False
    
    def setup_directories(self) -> bool:
        """Set up required directories."""
        self.log("Setting up directories...")
        
        directories = [
            "data/raw",
            "data/processed", 
            "data/backup",
            "logs",
            "models/production",
            "artifacts",
            "config"
        ]
        
        try:
            for directory in directories:
                dir_path = self.project_root / directory
                dir_path.mkdir(parents=True, exist_ok=True)
                self.log(f"Created directory: {directory}")
            
            return True
            
        except Exception as e:
            self.log(f"Error setting up directories: {str(e)}", "ERROR")
            return False
    
    def validate_configuration(self) -> bool:
        """Validate configuration file."""
        self.log("Validating configuration...")
        
        try:
            with open(self.config_path, 'r') as f:
                config = json.load(f)
            
            # Check required sections
            required_sections = [
                "mt5", "risk_management", "models", "trading"
            ]
            
            for section in required_sections:
                if section not in config:
                    self.log(f"Missing configuration section: {section}", "ERROR")
                    return False
            
            # Validate MT5 configuration
            mt5_config = config.get("mt5", {})
            if not mt5_config.get("login") or not mt5_config.get("server"):
                self.log("MT5 configuration incomplete", "WARNING")
            
            self.log("Configuration validation passed")
            return True
            
        except Exception as e:
            self.log(f"Error validating configuration: {str(e)}", "ERROR")
            return False
    
    def test_system_components(self) -> bool:
        """Test all system components."""
        self.log("Testing system components...")
        
        try:
            # Run comprehensive system test
            test_script = self.project_root / "test_all_systems.py"
            if test_script.exists():
                result = subprocess.run([
                    sys.executable, str(test_script)
                ], capture_output=True, text=True, cwd=self.project_root)
                
                if "READY FOR DEPLOYMENT" in result.stdout:
                    self.log("System tests passed")
                    return True
                else:
                    self.log("System tests failed - check test output", "WARNING")
                    return False
            else:
                self.log("Test script not found - skipping tests", "WARNING")
                return True
                
        except Exception as e:
            self.log(f"Error running system tests: {str(e)}", "ERROR")
            return False
    
    def setup_monitoring(self) -> bool:
        """Set up monitoring and logging."""
        self.log("Setting up monitoring...")
        
        try:
            # Create log configuration
            log_config = {
                "version": 1,
                "disable_existing_loggers": False,
                "formatters": {
                    "standard": {
                        "format": "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
                    }
                },
                "handlers": {
                    "default": {
                        "level": "INFO",
                        "formatter": "standard",
                        "class": "logging.StreamHandler"
                    },
                    "file": {
                        "level": "INFO",
                        "formatter": "standard",
                        "class": "logging.FileHandler",
                        "filename": "logs/trading_bot.log"
                    }
                },
                "loggers": {
                    "": {
                        "handlers": ["default", "file"],
                        "level": "INFO",
                        "propagate": False
                    }
                }
            }
            
            # Save log configuration
            log_config_path = self.project_root / "config" / "logging.json"
            with open(log_config_path, 'w', encoding='utf-8') as f:
                json.dump(log_config, f, indent=2)
            
            self.log("Monitoring setup completed")
            return True
            
        except Exception as e:
            self.log(f"Error setting up monitoring: {str(e)}", "ERROR")
            return False
    
    def create_startup_script(self) -> bool:
        """Create startup script for the trading bot."""
        self.log("Creating startup script...")
        
        try:
            startup_script = f"""#!/usr/bin/env python3
\"\"\"
AI Trading Bot Startup Script
Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
\"\"\"

import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.append(str(project_root / "src"))

# Import main components
from api.main import app
from trading.mt5_connector import MT5Connector
from monitoring.dashboard import TradingDashboard
from risk_management.risk_manager import RiskManager

def main():
    print("Starting AI Trading Bot...")
    
    # Initialize components
    mt5_connector = MT5Connector()
    dashboard = TradingDashboard()
    risk_manager = RiskManager()
    
    # Connect to MT5
    if mt5_connector.connect():
        print("[SUCCESS] Connected to MetaTrader 5")
    else:
        print("[WARNING] MT5 connection failed - running in simulation mode")

    # Start API server
    import uvicorn
    print("[INFO] Starting API server on http://localhost:8000")
    uvicorn.run(app, host="0.0.0.0", port=8000)

if __name__ == "__main__":
    main()
"""
            
            startup_path = self.project_root / "start_trading_bot.py"
            with open(startup_path, 'w', encoding='utf-8') as f:
                f.write(startup_script)
            
            # Make executable on Unix systems
            if os.name != 'nt':
                os.chmod(startup_path, 0o755)
            
            self.log("Startup script created")
            return True
            
        except Exception as e:
            self.log(f"Error creating startup script: {str(e)}", "ERROR")
            return False
    
    def create_service_files(self) -> bool:
        """Create service files for system integration."""
        self.log("Creating service files...")
        
        try:
            # Windows batch file
            batch_content = f"""@echo off
cd /d "{self.project_root}"
python start_trading_bot.py
pause
"""
            
            batch_path = self.project_root / "start_bot.bat"
            with open(batch_path, 'w', encoding='utf-8') as f:
                f.write(batch_content)
            
            # Linux systemd service file
            service_content = f"""[Unit]
Description=AI Trading Bot
After=network.target

[Service]
Type=simple
User=trader
WorkingDirectory={self.project_root}
ExecStart=/usr/bin/python3 {self.project_root}/start_trading_bot.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
"""
            
            service_path = self.project_root / "trading-bot.service"
            with open(service_path, 'w', encoding='utf-8') as f:
                f.write(service_content)
            
            self.log("Service files created")
            return True
            
        except Exception as e:
            self.log(f"Error creating service files: {str(e)}", "ERROR")
            return False
    
    def backup_existing_data(self) -> bool:
        """Backup existing data before deployment."""
        self.log("Backing up existing data...")
        
        try:
            backup_dir = self.project_root / "backup" / datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_dir.mkdir(parents=True, exist_ok=True)
            
            # Backup important directories
            backup_items = ["data", "models", "logs", "config"]
            
            for item in backup_items:
                source = self.project_root / item
                if source.exists():
                    if source.is_dir():
                        shutil.copytree(source, backup_dir / item, dirs_exist_ok=True)
                    else:
                        shutil.copy2(source, backup_dir / item)
                    self.log(f"Backed up: {item}")
            
            self.log(f"Backup completed: {backup_dir}")
            return True
            
        except Exception as e:
            self.log(f"Error during backup: {str(e)}", "ERROR")
            return False
    
    def deploy(self, skip_tests: bool = False, backup: bool = True) -> bool:
        """Execute full deployment process."""
        self.log("Starting AI Trading Bot deployment...")
        self.log("="*50)
        
        deployment_steps = [
            ("Prerequisites Check", self.check_prerequisites),
            ("Backup Data", self.backup_existing_data if backup else lambda: True),
            ("Setup Directories", self.setup_directories),
            ("Install Dependencies", self.install_dependencies),
            ("Validate Configuration", self.validate_configuration),
            ("Test Components", self.test_system_components if not skip_tests else lambda: True),
            ("Setup Monitoring", self.setup_monitoring),
            ("Create Startup Script", self.create_startup_script),
            ("Create Service Files", self.create_service_files),
        ]
        
        failed_steps = []
        
        for step_name, step_function in deployment_steps:
            self.log(f"Executing: {step_name}")
            
            try:
                if step_function():
                    self.log(f"[SUCCESS] {step_name} completed successfully")
                else:
                    self.log(f"[FAILED] {step_name} failed", "ERROR")
                    failed_steps.append(step_name)
            except Exception as e:
                self.log(f"[FAILED] {step_name} failed with exception: {str(e)}", "ERROR")
                failed_steps.append(step_name)
        
        # Save deployment log
        log_path = self.project_root / "logs" / f"deployment_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        with open(log_path, 'w', encoding='utf-8') as f:
            f.write("\n".join(self.deployment_log))
        
        # Deployment summary
        self.log("="*50)
        if not failed_steps:
            self.log("DEPLOYMENT SUCCESSFUL!")
            self.log("AI Trading Bot is ready for production use.")
            self.log("To start the bot, run: python start_trading_bot.py")
            return True
        else:
            self.log("DEPLOYMENT COMPLETED WITH ISSUES")
            self.log(f"Failed steps: {', '.join(failed_steps)}")
            self.log("Please review the issues and retry deployment.")
            return False


def main():
    """Main deployment function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Deploy AI Trading Bot")
    parser.add_argument("--config", default="config/config_template.json", 
                       help="Configuration file path")
    parser.add_argument("--skip-tests", action="store_true", 
                       help="Skip system tests during deployment")
    parser.add_argument("--no-backup", action="store_true", 
                       help="Skip data backup")
    
    args = parser.parse_args()
    
    deployer = TradingBotDeployer(args.config)
    success = deployer.deploy(
        skip_tests=args.skip_tests,
        backup=not args.no_backup
    )
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
