#!/usr/bin/env python3
"""
Test script for automatic trading functionality.
"""

import pandas as pd
import numpy as np
from pathlib import Path
import sys
import json
import asyncio
from datetime import datetime

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

# Import auto-trading components
from trading.mt5_connector import MT5Connector
from trading.auto_trader import AutoTrader


def test_mt5_auto_trading_setup():
    """Test MT5 auto-trading setup and permissions."""
    print("\n" + "="*60)
    print("🤖 TESTING MT5 AUTOMATIC TRADING SETUP")
    print("="*60)
    
    # Initialize MT5 connector
    mt5_connector = MT5Connector()
    
    print("📡 Connecting to MetaTrader 5...")
    connected = mt5_connector.connect()
    
    if not connected:
        print("❌ MT5 connection failed")
        print("💡 Make sure MetaTrader 5 is running and logged in")
        return False
    
    print("✅ Connected to MT5 successfully!")
    
    # Check auto-trading permissions
    print("\n🔍 Checking auto-trading permissions...")
    permissions = mt5_connector.check_trading_permissions()
    
    print(f"📊 Permission Status:")
    print(f"   Terminal trade allowed: {'✅' if permissions['terminal_trade_allowed'] else '❌'}")
    print(f"   Trade API enabled: {'✅' if permissions['tradeapi_enabled'] else '❌'}")
    print(f"   Account trade allowed: {'✅' if permissions['account_trade_allowed'] else '❌'}")
    print(f"   Expert Advisor trading: {'✅' if permissions['account_trade_expert'] else '❌'}")
    print(f"   AUTO-TRADING READY: {'✅' if permissions['auto_trading_allowed'] else '❌'}")
    
    if not permissions['auto_trading_allowed']:
        print("\n🚨 AUTO-TRADING NOT ENABLED!")
        print("💡 To enable auto-trading in MT5:")
        print("   1. Go to Tools → Options → Expert Advisors")
        print("   2. Check 'Allow automated trading'")
        print("   3. Check 'Allow DLL imports'")
        print("   4. Restart MT5 and try again")
        return False
    
    # Show account info
    account_info = permissions['account_info']
    print(f"\n💰 Account Information:")
    print(f"   Login: {account_info['login']}")
    print(f"   Server: {account_info['server']}")
    print(f"   Balance: ${account_info['balance']:,.2f}")
    print(f"   Equity: ${account_info['equity']:,.2f}")
    print(f"   Trade Mode: {account_info['trade_mode']}")
    
    # Test enabling auto-trading
    print(f"\n🚀 Enabling auto-trading...")
    auto_enabled = mt5_connector.enable_auto_trading()
    
    if auto_enabled:
        print("✅ Auto-trading enabled successfully!")
    else:
        print("❌ Failed to enable auto-trading")
        return False
    
    # Test market order placement (demo)
    print(f"\n🧪 Testing order placement capabilities...")
    
    # Get current price for EURUSD
    prices = mt5_connector.get_current_prices(['EURUSD'])
    if 'EURUSD' in prices:
        current_price = prices['EURUSD']['ask']
        print(f"   EURUSD current price: {current_price:.5f}")
        
        # Test order validation (don't actually place)
        test_order = {
            'symbol': 'EURUSD',
            'order_type': 'buy',
            'volume': 0.01,  # Minimum volume
            'comment': 'Auto-trading test'
        }
        
        print(f"   Test order prepared: {test_order}")
        print(f"   ✅ Order placement system ready")
    
    mt5_connector.disconnect()
    return True


def test_auto_trader_initialization():
    """Test auto-trader initialization."""
    print("\n" + "="*60)
    print("🤖 TESTING AUTO-TRADER INITIALIZATION")
    print("="*60)
    
    # High-risk configuration
    config = {
        'trading': {
            'enabled': True,
            'symbols': ['EURUSD', 'GBPUSD'],
            'timeframe': 'M15',
            'signal_interval': 60,  # 1 minute for testing
            'min_signal_strength': 0.6,
            'max_trades_per_day': 10
        },
        'risk_management': {
            'position_sizing': {
                'risk_per_trade': 0.30  # 30% risk as requested
            },
            'portfolio_limits': {
                'max_daily_loss': 0.50,
                'max_drawdown': 0.70,
                'max_positions': 5
            }
        }
    }
    
    print("⚙️ Initializing AutoTrader with high-risk configuration...")
    auto_trader = AutoTrader(config)
    
    print("✅ AutoTrader initialized successfully!")
    print(f"   Risk per trade: {config['risk_management']['position_sizing']['risk_per_trade']:.0%}")
    print(f"   Max daily loss: {config['risk_management']['portfolio_limits']['max_daily_loss']:.0%}")
    print(f"   Symbols to trade: {', '.join(config['trading']['symbols'])}")
    print(f"   Signal interval: {config['trading']['signal_interval']} seconds")
    
    return auto_trader


async def test_auto_trading_simulation():
    """Test auto-trading in simulation mode."""
    print("\n" + "="*60)
    print("🎮 TESTING AUTO-TRADING SIMULATION")
    print("="*60)
    
    # Initialize auto-trader
    auto_trader = test_auto_trader_initialization()
    
    print("🚀 Starting auto-trading simulation...")
    print("⏱️ Running for 30 seconds...")
    
    try:
        # Start auto-trading task
        trading_task = asyncio.create_task(auto_trader.start_auto_trading())
        
        # Let it run for 30 seconds
        await asyncio.sleep(30)
        
        # Stop auto-trading
        auto_trader.stop_auto_trading()
        
        print("✅ Auto-trading simulation completed!")
        
    except Exception as e:
        print(f"❌ Error in auto-trading simulation: {str(e)}")
        auto_trader.stop_auto_trading()


def test_configuration_validation():
    """Test configuration validation for auto-trading."""
    print("\n" + "="*60)
    print("⚙️ TESTING CONFIGURATION VALIDATION")
    print("="*60)
    
    # Test high-risk configuration
    high_risk_config = {
        'trading': {
            'auto_trading_enabled': True,
            'symbols': ['EURUSD', 'GBPUSD', 'USDJPY'],
            'risk_per_trade': 0.30,  # 30% risk!
            'max_daily_loss': 0.50,  # 50% max daily loss!
            'max_drawdown': 0.70     # 70% max drawdown!
        }
    }
    
    print("🚨 HIGH-RISK CONFIGURATION DETECTED:")
    print(f"   Risk per trade: {high_risk_config['trading']['risk_per_trade']:.0%}")
    print(f"   Max daily loss: {high_risk_config['trading']['max_daily_loss']:.0%}")
    print(f"   Max drawdown: {high_risk_config['trading']['max_drawdown']:.0%}")
    
    # Validate configuration
    warnings = []
    
    if high_risk_config['trading']['risk_per_trade'] > 0.10:
        warnings.append(f"⚠️ Risk per trade ({high_risk_config['trading']['risk_per_trade']:.0%}) is extremely high!")
    
    if high_risk_config['trading']['max_daily_loss'] > 0.20:
        warnings.append(f"⚠️ Max daily loss ({high_risk_config['trading']['max_daily_loss']:.0%}) is very high!")
    
    if high_risk_config['trading']['max_drawdown'] > 0.30:
        warnings.append(f"⚠️ Max drawdown ({high_risk_config['trading']['max_drawdown']:.0%}) is extremely high!")
    
    if warnings:
        print("\n🚨 CONFIGURATION WARNINGS:")
        for warning in warnings:
            print(f"   {warning}")
        
        print("\n💡 RECOMMENDATIONS:")
        print("   • Start with a very small test account")
        print("   • Monitor every trade closely")
        print("   • Be prepared for rapid account swings")
        print("   • Consider reducing risk after testing")
    
    print("\n✅ Configuration validation completed")
    return len(warnings) == 0


def show_auto_trading_instructions():
    """Show instructions for enabling auto-trading."""
    print("\n" + "="*80)
    print("📋 AUTO-TRADING SETUP INSTRUCTIONS")
    print("="*80)
    
    print("🔧 METATRADER 5 SETUP:")
    print("   1. Open MetaTrader 5")
    print("   2. Login to your trading account")
    print("   3. Go to Tools → Options → Expert Advisors")
    print("   4. Enable these settings:")
    print("      ✅ Allow automated trading")
    print("      ✅ Allow DLL imports")
    print("      ✅ Allow WebRequest for listed URL")
    print("   5. Click OK and restart MT5")
    
    print("\n🤖 BOT CONFIGURATION:")
    print("   • Risk per trade: 30% (EXTREME RISK!)")
    print("   • Max daily loss: 50%")
    print("   • Max drawdown: 70%")
    print("   • Trailing stops: Enabled")
    print("   • Auto-trading: Enabled")
    
    print("\n🚀 STARTING AUTO-TRADING:")
    print("   1. Run: python start_trading_bot.py")
    print("   2. Bot will connect to MT5 automatically")
    print("   3. Monitor dashboard at: http://localhost:8000")
    print("   4. Check logs in: logs/trading_bot.log")
    
    print("\n⚠️ SAFETY REMINDERS:")
    print("   • Start with demo account or very small real account")
    print("   • Monitor all trades closely")
    print("   • Be prepared for rapid account changes")
    print("   • Stop trading if losses exceed comfort level")
    
    print("\n🛑 EMERGENCY STOP:")
    print("   • Press Ctrl+C to stop the bot")
    print("   • Or disable auto-trading in MT5")
    print("   • Bot will close all positions safely")


async def main():
    """Main test function."""
    print("🤖 AI Trading Bot - Automatic Trading Test")
    print("="*60)
    
    try:
        # Test MT5 setup
        mt5_ready = test_mt5_auto_trading_setup()
        
        # Test configuration
        config_valid = test_configuration_validation()
        
        # Test auto-trader initialization
        if mt5_ready:
            auto_trader = test_auto_trader_initialization()
            
            # Ask user if they want to run simulation
            print(f"\n❓ Do you want to run auto-trading simulation? (y/n): ", end="")
            try:
                response = input().lower().strip()
                if response in ['y', 'yes']:
                    await test_auto_trading_simulation()
                else:
                    print("⏭️ Skipping simulation")
            except:
                print("⏭️ Skipping simulation")
        
        # Show setup instructions
        show_auto_trading_instructions()
        
        print("\n" + "="*60)
        print("🎯 AUTO-TRADING TEST COMPLETED")
        print("="*60)
        
        if mt5_ready and config_valid:
            print("✅ Your bot is ready for automatic trading!")
            print("🚀 Run: python start_trading_bot.py")
        else:
            print("⚠️ Please fix the issues above before starting auto-trading")
        
    except Exception as e:
        print(f"❌ Error during auto-trading test: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
