"""
MetaTrader 5 connector for live trading integration.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any, Union
from datetime import datetime, timedelta
import time
import json
from pathlib import Path


class MT5Connector:
    """
    Connector for MetaTrader 5 platform integration.
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """
        Initialize MT5 connector.
        
        Args:
            config: MT5 configuration dictionary
        """
        self.config = config or self._get_default_config()
        self.connected = False
        self.account_info = {}
        self.symbols_info = {}
        self.auto_trading_enabled = False
        self.last_error = None
        self.trade_requests = []
        
        # Try to import MT5 library
        try:
            import MetaTrader5 as mt5
            self.mt5 = mt5
            self.mt5_available = True
        except ImportError:
            print("MetaTrader5 library not available. Using simulation mode.")
            self.mt5 = None
            self.mt5_available = False
    
    def _get_default_config(self) -> Dict:
        """Get default MT5 configuration."""
        return {
            'server': 'MetaQuotes-Demo',
            'login': 0,
            'password': '',
            'path': '',
            'timeout': 60000,
            'symbols': ['EURUSD', 'GBPUSD', 'USDJPY'],
            'timeframes': {
                'M1': 1,
                'M5': 5,
                'M15': 15,
                'M30': 30,
                'H1': 60,
                'H4': 240,
                'D1': 1440
            }
        }
    
    def connect(self) -> bool:
        """
        Connect to MetaTrader 5.
        
        Returns:
            True if connection successful, False otherwise
        """
        if not self.mt5_available:
            print("MT5 library not available. Using simulation mode.")
            self.connected = True
            return True
        
        try:
            # Initialize MT5 connection
            if not self.mt5.initialize(
                path=self.config.get('path', ''),
                login=self.config.get('login', 0),
                password=self.config.get('password', ''),
                server=self.config.get('server', ''),
                timeout=self.config.get('timeout', 60000)
            ):
                print(f"MT5 initialization failed: {self.mt5.last_error()}")
                return False
            
            # Get account info
            account_info = self.mt5.account_info()
            if account_info is None:
                print(f"Failed to get account info: {self.mt5.last_error()}")
                return False
            
            self.account_info = account_info._asdict()
            self.connected = True
            
            print(f"Connected to MT5 successfully!")
            print(f"Account: {self.account_info.get('login', 'Unknown')}")
            print(f"Server: {self.account_info.get('server', 'Unknown')}")
            print(f"Balance: ${self.account_info.get('balance', 0):,.2f}")
            
            # Initialize symbols
            self._initialize_symbols()
            
            return True
            
        except Exception as e:
            print(f"Error connecting to MT5: {str(e)}")
            return False
    
    def disconnect(self):
        """Disconnect from MetaTrader 5."""
        if self.mt5_available and self.connected:
            self.mt5.shutdown()
        
        self.connected = False
        print("Disconnected from MT5")
    
    def _initialize_symbols(self):
        """Initialize trading symbols."""
        if not self.mt5_available:
            return
        
        symbols = self.config.get('symbols', ['EURUSD'])
        
        for symbol in symbols:
            # Select symbol in Market Watch
            if not self.mt5.symbol_select(symbol, True):
                print(f"Failed to select symbol {symbol}")
                continue
            
            # Get symbol info
            symbol_info = self.mt5.symbol_info(symbol)
            if symbol_info is not None:
                self.symbols_info[symbol] = symbol_info._asdict()
                print(f"Initialized symbol: {symbol}")
    
    def get_account_info(self) -> Dict:
        """Get current account information."""
        if not self.connected:
            return {}
        
        if not self.mt5_available:
            # Return simulated account info
            return {
                'login': 12345,
                'server': 'Demo-Server',
                'balance': 10000.0,
                'equity': 10000.0,
                'margin': 0.0,
                'free_margin': 10000.0,
                'currency': 'USD'
            }
        
        try:
            account_info = self.mt5.account_info()
            if account_info is not None:
                self.account_info = account_info._asdict()
                return self.account_info
            else:
                print(f"Failed to get account info: {self.mt5.last_error()}")
                return {}
        except Exception as e:
            print(f"Error getting account info: {str(e)}")
            return {}
    
    def get_symbol_info(self, symbol: str) -> Dict:
        """Get symbol information."""
        if not self.connected:
            return {}
        
        if not self.mt5_available:
            # Return simulated symbol info
            return {
                'symbol': symbol,
                'bid': 1.1000,
                'ask': 1.1002,
                'spread': 2,
                'digits': 5,
                'point': 0.00001,
                'trade_contract_size': 100000,
                'volume_min': 0.01,
                'volume_max': 100.0,
                'volume_step': 0.01
            }
        
        try:
            symbol_info = self.mt5.symbol_info(symbol)
            if symbol_info is not None:
                return symbol_info._asdict()
            else:
                print(f"Failed to get symbol info for {symbol}: {self.mt5.last_error()}")
                return {}
        except Exception as e:
            print(f"Error getting symbol info for {symbol}: {str(e)}")
            return {}
    
    def get_current_prices(self, symbols: Optional[List[str]] = None) -> Dict[str, Dict]:
        """
        Get current prices for symbols.
        
        Args:
            symbols: List of symbols (if None, use configured symbols)
            
        Returns:
            Dictionary with symbol prices
        """
        if not self.connected:
            return {}
        
        if symbols is None:
            symbols = self.config.get('symbols', ['EURUSD'])
        
        prices = {}
        
        if not self.mt5_available:
            # Return simulated prices
            for symbol in symbols:
                base_price = 1.1000 if 'USD' in symbol else 100.0
                spread = 0.0002
                prices[symbol] = {
                    'bid': base_price,
                    'ask': base_price + spread,
                    'time': datetime.now(),
                    'spread': spread * 100000  # in points
                }
            return prices
        
        try:
            for symbol in symbols:
                tick = self.mt5.symbol_info_tick(symbol)
                if tick is not None:
                    prices[symbol] = {
                        'bid': tick.bid,
                        'ask': tick.ask,
                        'time': datetime.fromtimestamp(tick.time),
                        'spread': tick.ask - tick.bid
                    }
                else:
                    print(f"Failed to get tick for {symbol}: {self.mt5.last_error()}")
            
            return prices
            
        except Exception as e:
            print(f"Error getting current prices: {str(e)}")
            return {}
    
    def get_historical_data(self, symbol: str, timeframe: str = 'M15', 
                           count: int = 1000) -> pd.DataFrame:
        """
        Get historical price data.
        
        Args:
            symbol: Trading symbol
            timeframe: Timeframe (M1, M5, M15, M30, H1, H4, D1)
            count: Number of bars to retrieve
            
        Returns:
            DataFrame with OHLCV data
        """
        if not self.connected:
            return pd.DataFrame()
        
        if not self.mt5_available:
            # Return simulated historical data
            dates = pd.date_range(end=datetime.now(), periods=count, freq='15T')
            
            # Generate realistic price data
            np.random.seed(42)
            price = 1.1000
            prices = []
            
            for _ in range(count):
                change = np.random.normal(0, 0.001)
                price = price * (1 + change)
                prices.append(price)
            
            df = pd.DataFrame({
                'time': dates,
                'open': prices,
                'high': [p * (1 + abs(np.random.normal(0, 0.0005))) for p in prices],
                'low': [p * (1 - abs(np.random.normal(0, 0.0005))) for p in prices],
                'close': prices,
                'tick_volume': np.random.randint(100, 1000, count),
                'spread': np.random.randint(1, 5, count),
                'real_volume': np.random.randint(1000, 10000, count)
            })
            
            return df
        
        try:
            # Map timeframe string to MT5 constant
            timeframe_map = {
                'M1': self.mt5.TIMEFRAME_M1,
                'M5': self.mt5.TIMEFRAME_M5,
                'M15': self.mt5.TIMEFRAME_M15,
                'M30': self.mt5.TIMEFRAME_M30,
                'H1': self.mt5.TIMEFRAME_H1,
                'H4': self.mt5.TIMEFRAME_H4,
                'D1': self.mt5.TIMEFRAME_D1
            }
            
            mt5_timeframe = timeframe_map.get(timeframe, self.mt5.TIMEFRAME_M15)
            
            # Get historical data
            rates = self.mt5.copy_rates_from_pos(symbol, mt5_timeframe, 0, count)
            
            if rates is not None and len(rates) > 0:
                # Convert to DataFrame
                df = pd.DataFrame(rates)
                df['time'] = pd.to_datetime(df['time'], unit='s')
                return df
            else:
                print(f"Failed to get historical data for {symbol}: {self.mt5.last_error()}")
                return pd.DataFrame()
                
        except Exception as e:
            print(f"Error getting historical data: {str(e)}")
            return pd.DataFrame()
    
    def place_order(self, symbol: str, order_type: str, volume: float,
                   price: Optional[float] = None, sl: Optional[float] = None,
                   tp: Optional[float] = None, comment: str = "AI Trading Bot") -> Dict:
        """
        Place a trading order.
        
        Args:
            symbol: Trading symbol
            order_type: 'buy' or 'sell'
            volume: Order volume
            price: Order price (for pending orders)
            sl: Stop loss price
            tp: Take profit price
            comment: Order comment
            
        Returns:
            Order result dictionary
        """
        if not self.connected:
            return {'status': 'error', 'message': 'Not connected to MT5'}
        
        if not self.mt5_available:
            # Simulate order placement
            return {
                'status': 'success',
                'order': 12345,
                'volume': volume,
                'price': price or 1.1000,
                'symbol': symbol,
                'type': order_type,
                'comment': comment,
                'time': datetime.now()
            }
        
        try:
            # Get symbol info
            symbol_info = self.get_symbol_info(symbol)
            if not symbol_info:
                return {'status': 'error', 'message': f'Symbol {symbol} not found'}
            
            # Get current price
            tick = self.mt5.symbol_info_tick(symbol)
            if tick is None:
                return {'status': 'error', 'message': f'Failed to get price for {symbol}'}
            
            # Determine order type and price
            if order_type.lower() == 'buy':
                action = self.mt5.ORDER_TYPE_BUY
                order_price = tick.ask if price is None else price
            else:
                action = self.mt5.ORDER_TYPE_SELL
                order_price = tick.bid if price is None else price
            
            # Prepare order request
            request = {
                "action": self.mt5.TRADE_ACTION_DEAL,
                "symbol": symbol,
                "volume": volume,
                "type": action,
                "price": order_price,
                "deviation": 20,
                "magic": 234000,
                "comment": comment,
                "type_time": self.mt5.ORDER_TIME_GTC,
                "type_filling": self.mt5.ORDER_FILLING_IOC,
            }
            
            # Add stop loss and take profit if provided
            if sl is not None:
                request["sl"] = sl
            if tp is not None:
                request["tp"] = tp
            
            # Send order
            result = self.mt5.order_send(request)
            
            if result.retcode != self.mt5.TRADE_RETCODE_DONE:
                return {
                    'status': 'error',
                    'message': f'Order failed: {result.comment}',
                    'retcode': result.retcode
                }
            
            return {
                'status': 'success',
                'order': result.order,
                'deal': result.deal,
                'volume': result.volume,
                'price': result.price,
                'symbol': symbol,
                'type': order_type,
                'comment': comment,
                'time': datetime.now()
            }
            
        except Exception as e:
            return {'status': 'error', 'message': f'Error placing order: {str(e)}'}
    
    def get_positions(self) -> List[Dict]:
        """Get current open positions."""
        if not self.connected:
            return []
        
        if not self.mt5_available:
            # Return simulated positions
            return [
                {
                    'ticket': 12345,
                    'symbol': 'EURUSD',
                    'type': 0,  # Buy
                    'volume': 0.1,
                    'price_open': 1.1000,
                    'price_current': 1.1010,
                    'profit': 10.0,
                    'time': datetime.now(),
                    'comment': 'AI Trading Bot'
                }
            ]
        
        try:
            positions = self.mt5.positions_get()
            if positions is not None:
                return [pos._asdict() for pos in positions]
            else:
                return []
        except Exception as e:
            print(f"Error getting positions: {str(e)}")
            return []
    
    def close_position(self, ticket: int) -> Dict:
        """Close a position by ticket."""
        if not self.connected:
            return {'status': 'error', 'message': 'Not connected to MT5'}
        
        if not self.mt5_available:
            # Simulate position closing
            return {
                'status': 'success',
                'ticket': ticket,
                'message': 'Position closed (simulated)'
            }
        
        try:
            # Get position info
            position = self.mt5.positions_get(ticket=ticket)
            if not position:
                return {'status': 'error', 'message': f'Position {ticket} not found'}
            
            position = position[0]
            
            # Prepare close request
            if position.type == 0:  # Buy position
                order_type = self.mt5.ORDER_TYPE_SELL
                price = self.mt5.symbol_info_tick(position.symbol).bid
            else:  # Sell position
                order_type = self.mt5.ORDER_TYPE_BUY
                price = self.mt5.symbol_info_tick(position.symbol).ask
            
            request = {
                "action": self.mt5.TRADE_ACTION_DEAL,
                "symbol": position.symbol,
                "volume": position.volume,
                "type": order_type,
                "position": ticket,
                "price": price,
                "deviation": 20,
                "magic": 234000,
                "comment": "Close by AI Trading Bot",
                "type_time": self.mt5.ORDER_TIME_GTC,
                "type_filling": self.mt5.ORDER_FILLING_IOC,
            }
            
            result = self.mt5.order_send(request)
            
            if result.retcode != self.mt5.TRADE_RETCODE_DONE:
                return {
                    'status': 'error',
                    'message': f'Failed to close position: {result.comment}',
                    'retcode': result.retcode
                }
            
            return {
                'status': 'success',
                'ticket': ticket,
                'deal': result.deal,
                'message': 'Position closed successfully'
            }
            
        except Exception as e:
            return {'status': 'error', 'message': f'Error closing position: {str(e)}'}
    
    def get_orders(self) -> List[Dict]:
        """Get current pending orders."""
        if not self.connected:
            return []
        
        if not self.mt5_available:
            return []  # No simulated pending orders
        
        try:
            orders = self.mt5.orders_get()
            if orders is not None:
                return [order._asdict() for order in orders]
            else:
                return []
        except Exception as e:
            print(f"Error getting orders: {str(e)}")
            return []
    
    def is_market_open(self, symbol: str) -> bool:
        """Check if market is open for trading."""
        if not self.connected:
            return False
        
        if not self.mt5_available:
            # Simulate market hours (Monday to Friday, 24/5)
            now = datetime.now()
            return now.weekday() < 5  # Monday = 0, Sunday = 6
        
        try:
            symbol_info = self.get_symbol_info(symbol)
            if symbol_info:
                # Check if symbol is available for trading
                return symbol_info.get('trade_mode', 0) != 0
            return False
        except Exception as e:
            print(f"Error checking market status: {str(e)}")
            return False

    def enable_auto_trading(self) -> bool:
        """
        Enable automatic trading in MT5.

        Returns:
            True if auto-trading is enabled successfully
        """
        if not self.mt5_available or not self.connected:
            print("❌ MT5 not available or not connected")
            return False

        try:
            # Check if auto-trading is allowed
            terminal_info = self.mt5.terminal_info()
            if terminal_info is None:
                print("❌ Failed to get terminal info")
                return False

            # Check if algorithmic trading is enabled
            if not terminal_info.trade_allowed:
                print("❌ Trading is not allowed in terminal")
                return False

            if not terminal_info.tradeapi_disabled:
                print("✅ Auto-trading is enabled in MT5 terminal")
                self.auto_trading_enabled = True
                return True
            else:
                print("❌ Trade API is disabled in MT5 terminal")
                print("💡 Enable 'Allow automated trading' in MT5 Tools -> Options -> Expert Advisors")
                return False

        except Exception as e:
            print(f"❌ Error checking auto-trading status: {e}")
            self.last_error = str(e)
            return False

    def check_trading_permissions(self) -> Dict[str, Any]:
        """
        Check all trading permissions and requirements.

        Returns:
            Dictionary with permission status
        """
        if not self.mt5_available or not self.connected:
            return {
                'auto_trading_allowed': False,
                'error': 'MT5 not available or not connected'
            }

        try:
            terminal_info = self.mt5.terminal_info()
            account_info = self.mt5.account_info()

            if terminal_info is None or account_info is None:
                return {
                    'auto_trading_allowed': False,
                    'error': 'Failed to get terminal or account info'
                }

            permissions = {
                'terminal_trade_allowed': terminal_info.trade_allowed,
                'tradeapi_enabled': not terminal_info.tradeapi_disabled,
                'account_trade_allowed': account_info.trade_allowed,
                'account_trade_expert': account_info.trade_expert,
                'auto_trading_allowed': False,
                'terminal_info': {
                    'company': terminal_info.company,
                    'name': terminal_info.name,
                    'path': terminal_info.path,
                    'build': terminal_info.build
                },
                'account_info': {
                    'login': account_info.login,
                    'server': account_info.server,
                    'balance': account_info.balance,
                    'equity': account_info.equity,
                    'margin_mode': account_info.margin_mode,
                    'trade_mode': account_info.trade_mode
                }
            }

            # Auto-trading is allowed if all conditions are met
            permissions['auto_trading_allowed'] = (
                permissions['terminal_trade_allowed'] and
                permissions['tradeapi_enabled'] and
                permissions['account_trade_allowed'] and
                permissions['account_trade_expert']
            )

            return permissions

        except Exception as e:
            return {
                'auto_trading_allowed': False,
                'error': f'Error checking permissions: {str(e)}'
            }

    def place_market_order(self, symbol: str, order_type: str, volume: float,
                          stop_loss: Optional[float] = None,
                          take_profit: Optional[float] = None,
                          comment: str = "AI Trading Bot") -> Dict[str, Any]:
        """
        Place a market order for automatic trading.

        Args:
            symbol: Trading symbol
            order_type: 'buy' or 'sell'
            volume: Order volume
            stop_loss: Stop loss price (optional)
            take_profit: Take profit price (optional)
            comment: Order comment

        Returns:
            Order result dictionary
        """
        if not self.auto_trading_enabled:
            return {
                'status': 'error',
                'message': 'Auto-trading not enabled',
                'retcode': -1
            }

        if not self.mt5_available or not self.connected:
            return {
                'status': 'error',
                'message': 'MT5 not available or not connected',
                'retcode': -1
            }

        try:
            # Get symbol info
            symbol_info = self.mt5.symbol_info(symbol)
            if symbol_info is None:
                return {
                    'status': 'error',
                    'message': f'Symbol {symbol} not found',
                    'retcode': -1
                }

            # Check if symbol is available for trading
            if not symbol_info.visible:
                # Try to enable symbol
                if not self.mt5.symbol_select(symbol, True):
                    return {
                        'status': 'error',
                        'message': f'Failed to enable symbol {symbol}',
                        'retcode': -1
                    }

            # Get current price
            tick = self.mt5.symbol_info_tick(symbol)
            if tick is None:
                return {
                    'status': 'error',
                    'message': f'Failed to get price for {symbol}',
                    'retcode': -1
                }

            # Determine order type and price
            if order_type.lower() == 'buy':
                trade_type = self.mt5.ORDER_TYPE_BUY
                price = tick.ask
            elif order_type.lower() == 'sell':
                trade_type = self.mt5.ORDER_TYPE_SELL
                price = tick.bid
            else:
                return {
                    'status': 'error',
                    'message': f'Invalid order type: {order_type}',
                    'retcode': -1
                }

            # Prepare trade request
            request = {
                'action': self.mt5.TRADE_ACTION_DEAL,
                'symbol': symbol,
                'volume': volume,
                'type': trade_type,
                'price': price,
                'deviation': 20,  # Price deviation in points
                'magic': 234000,  # Magic number for EA identification
                'comment': comment,
                'type_time': self.mt5.ORDER_TIME_GTC,
                'type_filling': self.mt5.ORDER_FILLING_IOC,
            }

            # Add stop loss and take profit if provided
            if stop_loss is not None:
                request['sl'] = stop_loss
            if take_profit is not None:
                request['tp'] = take_profit

            # Store request for tracking
            self.trade_requests.append({
                'timestamp': datetime.now(),
                'request': request.copy()
            })

            # Send trade request
            print(f"🚀 Placing {order_type.upper()} order for {volume} {symbol} at {price}")
            result = self.mt5.order_send(request)

            if result is None:
                return {
                    'status': 'error',
                    'message': 'Failed to send order',
                    'retcode': -1
                }

            # Process result
            if result.retcode == self.mt5.TRADE_RETCODE_DONE:
                print(f"✅ Order executed successfully!")
                print(f"   Order ticket: {result.order}")
                print(f"   Deal ticket: {result.deal}")
                print(f"   Volume: {result.volume}")
                print(f"   Price: {result.price}")

                return {
                    'status': 'success',
                    'retcode': result.retcode,
                    'order': result.order,
                    'deal': result.deal,
                    'volume': result.volume,
                    'price': result.price,
                    'comment': result.comment,
                    'request_id': result.request_id
                }
            else:
                error_msg = f"Order failed with retcode: {result.retcode}"
                print(f"❌ {error_msg}")

                return {
                    'status': 'error',
                    'retcode': result.retcode,
                    'message': error_msg,
                    'result': result._asdict() if hasattr(result, '_asdict') else str(result)
                }

        except Exception as e:
            error_msg = f"Exception placing order: {str(e)}"
            print(f"❌ {error_msg}")
            self.last_error = error_msg

            return {
                'status': 'error',
                'message': error_msg,
                'retcode': -1
            }
