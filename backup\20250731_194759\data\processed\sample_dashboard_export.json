{"timestamp": "2025-07-31T19:36:03.640494", "summary": {"total_trades": 49, "win_rate": 0.****************, "total_pnl": 91.**************, "current_drawdown": -0.15, "account_balance": 0, "active_positions": 0, "portfolio_value": 8500.0, "daily_return": 0, "monthly_return": 0}, "system_status": {"timestamp": "2025-07-31T19:36:03.437436", "cpu_usage": 85.0, "memory_usage": 90.0, "mt5_connected": false}, "active_alerts": [{"id": 1, "timestamp": "2025-07-31T19:36:03.423875", "severity": "MEDIUM", "title": "Low Win Rate", "message": "Win rate: 32.4%", "acknowledged": false}, {"id": 2, "timestamp": "2025-07-31T19:36:03.437202", "severity": "HIGH", "title": "Maximum Drawdown Exceeded", "message": "Current drawdown: -15.00%", "acknowledged": false}, {"id": 3, "timestamp": "2025-07-31T19:36:03.437288", "severity": "HIGH", "title": "Daily Loss Limit Exceeded", "message": "Daily P&L: $-500.00", "acknowledged": false}, {"id": 4, "timestamp": "2025-07-31T19:36:03.437362", "severity": "MEDIUM", "title": "Low Win Rate", "message": "Win rate: 30.0%", "acknowledged": false}, {"id": 5, "timestamp": "2025-07-31T19:36:03.437445", "severity": "MEDIUM", "title": "High CPU Usage", "message": "CPU usage: 85.0%", "acknowledged": false}, {"id": 6, "timestamp": "2025-07-31T19:36:03.437568", "severity": "MEDIUM", "title": "High Memory Usage", "message": "Memory usage: 90.0%", "acknowledged": false}, {"id": 7, "timestamp": "2025-07-31T19:36:03.437658", "severity": "HIGH", "title": "MT5 Connection Lost", "message": "MetaTrader 5 connection is down", "acknowledged": false}], "recent_alerts": [{"id": 1, "timestamp": "2025-07-31T19:36:03.423875", "severity": "MEDIUM", "title": "Low Win Rate", "message": "Win rate: 32.4%", "acknowledged": false}, {"id": 2, "timestamp": "2025-07-31T19:36:03.437202", "severity": "HIGH", "title": "Maximum Drawdown Exceeded", "message": "Current drawdown: -15.00%", "acknowledged": false}, {"id": 3, "timestamp": "2025-07-31T19:36:03.437288", "severity": "HIGH", "title": "Daily Loss Limit Exceeded", "message": "Daily P&L: $-500.00", "acknowledged": false}, {"id": 4, "timestamp": "2025-07-31T19:36:03.437362", "severity": "MEDIUM", "title": "Low Win Rate", "message": "Win rate: 30.0%", "acknowledged": false}, {"id": 5, "timestamp": "2025-07-31T19:36:03.437445", "severity": "MEDIUM", "title": "High CPU Usage", "message": "CPU usage: 85.0%", "acknowledged": false}, {"id": 6, "timestamp": "2025-07-31T19:36:03.437568", "severity": "MEDIUM", "title": "High Memory Usage", "message": "Memory usage: 90.0%", "acknowledged": false}, {"id": 7, "timestamp": "2025-07-31T19:36:03.437658", "severity": "HIGH", "title": "MT5 Connection Lost", "message": "MetaTrader 5 connection is down", "acknowledged": false}], "charts": {"equity_curve": [{"timestamp": "2025-07-31T19:36:03.422998", "value": 9915.1828766192}, {"timestamp": "2025-07-31T19:36:03.423068", "value": 9919.96153535865}, {"timestamp": "2025-07-31T19:36:03.423122", "value": 9796.750229114023}, {"timestamp": "2025-07-31T19:36:03.423170", "value": 9791.003728728345}, {"timestamp": "2025-07-31T19:36:03.423215", "value": 9994.863367685015}, {"timestamp": "2025-07-31T19:36:03.423256", "value": 10133.552618081607}, {"timestamp": "2025-07-31T19:36:03.423298", "value": 10151.632154056475}, {"timestamp": "2025-07-31T19:36:03.423339", "value": 10010.599295330929}, {"timestamp": "2025-07-31T19:36:03.423379", "value": 10097.869504764221}, {"timestamp": "2025-07-31T19:36:03.423420", "value": 10187.645345790726}, {"timestamp": "2025-07-31T19:36:03.423462", "value": 10235.99387133611}, {"timestamp": "2025-07-31T19:36:03.423506", "value": 10253.920501141894}, {"timestamp": "2025-07-31T19:36:03.423548", "value": 10266.679299400665}, {"timestamp": "2025-07-31T19:36:03.423589", "value": 10448.331494271117}, {"timestamp": "2025-07-31T19:36:03.423631", "value": 10514.748548678699}, {"timestamp": "2025-07-31T19:36:03.423673", "value": 10273.307921761656}, {"timestamp": "2025-07-31T19:36:03.423711", "value": 10378.609494518169}, {"timestamp": "2025-07-31T19:36:03.423750", "value": 10465.738528755783}, {"timestamp": "2025-07-31T19:36:03.423787", "value": 10277.596176067718}, {"timestamp": "2025-07-31T19:36:03.423823", "value": 10422.16416913891}, {"timestamp": "2025-07-31T19:36:03.423861", "value": 10627.10988775448}, {"timestamp": "2025-07-31T19:36:03.424031", "value": 10588.798004237955}, {"timestamp": "2025-07-31T19:36:03.424055", "value": 10334.2321955512}, {"timestamp": "2025-07-31T19:36:03.424075", "value": 10548.303225866272}, {"timestamp": "2025-07-31T19:36:03.437186", "value": 8500.0}], "daily_pnl": [{"timestamp": "2025-07-31T19:36:03.422998", "value": -84.09772893894193}, {"timestamp": "2025-07-31T19:36:03.423068", "value": 4.780961831550001}, {"timestamp": "2025-07-31T19:36:03.423122", "value": -121.6809549491721}, {"timestamp": "2025-07-31T19:36:03.423170", "value": -5.743129648859999}, {"timestamp": "2025-07-31T19:36:03.423215", "value": 208.10422445035223}, {"timestamp": "2025-07-31T19:36:03.423256", "value": 140.61370973815275}, {"timestamp": "2025-07-31T19:36:03.423298", "value": 18.111792147346254}, {"timestamp": "2025-07-31T19:36:03.423339", "value": -139.07354154989957}, {"timestamp": "2025-07-31T19:36:03.423379", "value": 88.03101198165443}, {"timestamp": "2025-07-31T19:36:03.423420", "value": 90.5739996507775}, {"timestamp": "2025-07-31T19:36:03.423462", "value": 48.577977969674244}, {"timestamp": "2025-07-31T19:36:03.423506", "value": 17.958025297051297}, {"timestamp": "2025-07-31T19:36:03.423548", "value": 12.77467383855308}, {"timestamp": "2025-07-31T19:36:03.423589", "value": 184.86623506192709}, {"timestamp": "2025-07-31T19:36:03.423631", "value": 66.83924862285981}, {"timestamp": "2025-07-31T19:36:03.423673", "value": -235.89664495149975}, {"timestamp": "2025-07-31T19:36:03.423711", "value": 106.38091558449317}, {"timestamp": "2025-07-31T19:36:03.423750", "value": 87.86048758029769}, {"timestamp": "2025-07-31T19:36:03.423787", "value": -184.76012172770314}, {"timestamp": "2025-07-31T19:36:03.423823", "value": 146.60153323589458}, {"timestamp": "2025-07-31T19:36:03.423861", "value": 208.97585543717364}, {"timestamp": "2025-07-31T19:36:03.424031", "value": -38.173765022023936}, {"timestamp": "2025-07-31T19:36:03.424055", "value": -248.44577967813728}, {"timestamp": "2025-07-31T19:36:03.424075", "value": 218.50545806480775}, {"timestamp": "2025-07-31T19:36:03.437186", "value": -500.0}], "drawdown": [{"timestamp": "2025-07-31T19:36:03.422998", "value": -0.033871816011480975}, {"timestamp": "2025-07-31T19:36:03.423068", "value": -0.013449233423827029}, {"timestamp": "2025-07-31T19:36:03.423122", "value": -0.019154558867423885}, {"timestamp": "2025-07-31T19:36:03.423170", "value": 0.005339550318267803}, {"timestamp": "2025-07-31T19:36:03.423215", "value": 0.01177784728007948}, {"timestamp": "2025-07-31T19:36:03.423256", "value": -0.03217091147348368}, {"timestamp": "2025-07-31T19:36:03.423298", "value": -0.05}, {"timestamp": "2025-07-31T19:36:03.423339", "value": -0.0043062742668442235}, {"timestamp": "2025-07-31T19:36:03.423379", "value": -0.04197519625338427}, {"timestamp": "2025-07-31T19:36:03.423420", "value": -0.005071294283398836}, {"timestamp": "2025-07-31T19:36:03.423462", "value": -0.0074387937326106665}, {"timestamp": "2025-07-31T19:36:03.423506", "value": -0.04744185345794583}, {"timestamp": "2025-07-31T19:36:03.423548", "value": -0.0001432831103677628}, {"timestamp": "2025-07-31T19:36:03.423589", "value": 0.01768231460667672}, {"timestamp": "2025-07-31T19:36:03.423631", "value": -0.008445070332171319}, {"timestamp": "2025-07-31T19:36:03.423673", "value": -0.002604784663789132}, {"timestamp": "2025-07-31T19:36:03.423711", "value": -0.01955139665352036}, {"timestamp": "2025-07-31T19:36:03.423750", "value": -0.0033664138730040236}, {"timestamp": "2025-07-31T19:36:03.423787", "value": -0.02288664730095117}, {"timestamp": "2025-07-31T19:36:03.423823", "value": 0.019351025438832846}, {"timestamp": "2025-07-31T19:36:03.423861", "value": 0.027783253881865373}, {"timestamp": "2025-07-31T19:36:03.424031", "value": -0.0030073167458515085}, {"timestamp": "2025-07-31T19:36:03.424055", "value": 0.004840194769925614}, {"timestamp": "2025-07-31T19:36:03.424075", "value": -0.0032497340104645275}, {"timestamp": "2025-07-31T19:36:03.437186", "value": -0.15}], "symbol_performance": [{"symbol": "EURUSD", "pnl": 150.0, "trades": 12, "win_rate": 0.58}, {"symbol": "GBPUSD", "pnl": -75.0, "trades": 8, "win_rate": 0.38}, {"symbol": "USDJPY", "pnl": 200.0, "trades": 15, "win_rate": 0.67}, {"symbol": "EURJPY", "pnl": 50.0, "trades": 6, "win_rate": 0.5}]}, "trading_status": {"is_trading": false, "last_trade_time": null, "next_signal_check": null, "market_hours": true, "connection_status": false}}