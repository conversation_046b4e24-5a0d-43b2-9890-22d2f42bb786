"""
Deep learning models for trading prediction.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any, Union
import pickle
from pathlib import Path


class DeepLearningModels:
    """
    Implements deep learning models for trading predictions.
    """
    
    def __init__(self):
        """Initialize DeepLearningModels."""
        self.models = {}
        self.model_history = {}
        self.scalers = {}
        
    def create_sequences(self, data: np.ndarray, sequence_length: int = 60, 
                        target_column_idx: int = -1) -> Tuple[np.ndarray, np.ndarray]:
        """
        Create sequences for time series modeling.
        
        Args:
            data: Input data array
            sequence_length: Length of input sequences
            target_column_idx: Index of target column
            
        Returns:
            Tuple of (X, y) arrays
        """
        X, y = [], []
        
        for i in range(sequence_length, len(data)):
            X.append(data[i-sequence_length:i])
            y.append(data[i, target_column_idx])
        
        return np.array(X), np.array(y)
    
    def build_lstm_model(self, input_shape: Tuple, units: List[int] = [50, 50],
                        dropout: float = 0.2, output_units: int = 1) -> Any:
        """
        Build LSTM model.
        
        Args:
            input_shape: Shape of input data (timesteps, features)
            units: List of LSTM units for each layer
            dropout: Dropout rate
            output_units: Number of output units
            
        Returns:
            Compiled model
        """
        try:
            import tensorflow as tf
            from tensorflow.keras.models import Sequential
            from tensorflow.keras.layers import LSTM, Dense, Dropout
            from tensorflow.keras.optimizers import Adam
            
            model = Sequential()
            
            # First LSTM layer
            model.add(LSTM(units[0], return_sequences=len(units) > 1, 
                          input_shape=input_shape))
            model.add(Dropout(dropout))
            
            # Additional LSTM layers
            for i in range(1, len(units)):
                return_sequences = i < len(units) - 1
                model.add(LSTM(units[i], return_sequences=return_sequences))
                model.add(Dropout(dropout))
            
            # Output layer
            model.add(Dense(output_units))
            
            # Compile model
            model.compile(optimizer=Adam(learning_rate=0.001), 
                         loss='mse', metrics=['mae'])
            
            return model
            
        except ImportError:
            print("TensorFlow not available. Using simple LSTM implementation.")
            return self._simple_lstm_model(input_shape, units, output_units)
    
    def _simple_lstm_model(self, input_shape: Tuple, units: List[int], output_units: int) -> Dict:
        """Simple LSTM implementation using numpy."""
        # This is a placeholder for a simple LSTM implementation
        # In practice, you would implement LSTM cells manually
        model_config = {
            'type': 'simple_lstm',
            'input_shape': input_shape,
            'units': units,
            'output_units': output_units,
            'weights': None  # Would store learned weights
        }
        
        return model_config
    
    def train_lstm(self, X_train: np.ndarray, y_train: np.ndarray,
                   X_val: Optional[np.ndarray] = None, y_val: Optional[np.ndarray] = None,
                   units: List[int] = [50, 50], dropout: float = 0.2,
                   epochs: int = 100, batch_size: int = 32, patience: int = 10) -> Dict:
        """
        Train LSTM model.
        
        Args:
            X_train: Training sequences
            y_train: Training targets
            X_val: Validation sequences
            y_val: Validation targets
            units: LSTM units for each layer
            dropout: Dropout rate
            epochs: Number of training epochs
            batch_size: Batch size
            patience: Early stopping patience
            
        Returns:
            Training history and metrics
        """
        try:
            import tensorflow as tf
            from tensorflow.keras.callbacks import EarlyStopping
            
            # Build model
            input_shape = (X_train.shape[1], X_train.shape[2])
            model = self.build_lstm_model(input_shape, units, dropout)
            
            # Callbacks
            callbacks = []
            if X_val is not None and y_val is not None:
                early_stopping = EarlyStopping(monitor='val_loss', patience=patience, 
                                             restore_best_weights=True)
                callbacks.append(early_stopping)
            
            # Train model
            validation_data = (X_val, y_val) if X_val is not None else None
            
            history = model.fit(
                X_train, y_train,
                validation_data=validation_data,
                epochs=epochs,
                batch_size=batch_size,
                callbacks=callbacks,
                verbose=1
            )
            
            self.models['lstm'] = model
            self.model_history['lstm'] = history.history
            
            # Calculate final metrics
            train_loss = history.history['loss'][-1]
            val_loss = history.history['val_loss'][-1] if 'val_loss' in history.history else None
            
            performance = {
                'model_type': 'lstm',
                'train_loss': train_loss,
                'val_loss': val_loss,
                'epochs_trained': len(history.history['loss']),
                'input_shape': input_shape,
                'units': units,
                'dropout': dropout
            }
            
            return performance
            
        except ImportError:
            print("TensorFlow not available. Using simple implementation.")
            return self._simple_lstm_training(X_train, y_train, units)
    
    def _simple_lstm_training(self, X_train: np.ndarray, y_train: np.ndarray, units: List[int]) -> Dict:
        """Simple LSTM training simulation."""
        # This is a placeholder that simulates LSTM training
        # In practice, you would implement the LSTM forward/backward pass
        
        input_shape = (X_train.shape[1], X_train.shape[2])
        
        # Simulate training by creating a simple linear model on flattened sequences
        X_flat = X_train.reshape(X_train.shape[0], -1)
        
        # Simple linear regression on flattened sequences
        X_with_bias = np.column_stack([np.ones(X_flat.shape[0]), X_flat])
        
        try:
            theta = np.linalg.solve(X_with_bias.T @ X_with_bias, X_with_bias.T @ y_train)
        except np.linalg.LinAlgError:
            theta = np.linalg.pinv(X_with_bias.T @ X_with_bias) @ X_with_bias.T @ y_train
        
        # Store model
        model_config = {
            'type': 'simple_lstm',
            'input_shape': input_shape,
            'units': units,
            'theta': theta
        }
        
        self.models['lstm'] = model_config
        
        # Calculate metrics
        y_pred = X_with_bias @ theta
        mse = np.mean((y_train - y_pred) ** 2)
        
        performance = {
            'model_type': 'simple_lstm',
            'train_loss': mse,
            'input_shape': input_shape,
            'units': units
        }
        
        return performance
    
    def build_transformer_model(self, input_shape: Tuple, d_model: int = 64, 
                               nhead: int = 8, num_layers: int = 6, 
                               dropout: float = 0.1, output_units: int = 1) -> Any:
        """
        Build Transformer model.
        
        Args:
            input_shape: Shape of input data
            d_model: Model dimension
            nhead: Number of attention heads
            num_layers: Number of transformer layers
            dropout: Dropout rate
            output_units: Number of output units
            
        Returns:
            Compiled model
        """
        try:
            import tensorflow as tf
            from tensorflow.keras.models import Model
            from tensorflow.keras.layers import Input, Dense, Dropout, LayerNormalization
            from tensorflow.keras.layers import MultiHeadAttention, GlobalAveragePooling1D
            
            # Input layer
            inputs = Input(shape=input_shape)
            x = inputs
            
            # Transformer blocks
            for _ in range(num_layers):
                # Multi-head attention
                attn_output = MultiHeadAttention(
                    num_heads=nhead, key_dim=d_model//nhead, dropout=dropout
                )(x, x)
                
                # Add & Norm
                x = LayerNormalization()(x + attn_output)
                
                # Feed forward
                ff_output = Dense(d_model * 4, activation='relu')(x)
                ff_output = Dropout(dropout)(ff_output)
                ff_output = Dense(d_model)(ff_output)
                
                # Add & Norm
                x = LayerNormalization()(x + ff_output)
            
            # Global pooling and output
            x = GlobalAveragePooling1D()(x)
            x = Dropout(dropout)(x)
            outputs = Dense(output_units)(x)
            
            model = Model(inputs, outputs)
            model.compile(optimizer='adam', loss='mse', metrics=['mae'])
            
            return model
            
        except ImportError:
            print("TensorFlow not available. Transformer not implemented.")
            return None
    
    def train_transformer(self, X_train: np.ndarray, y_train: np.ndarray,
                         X_val: Optional[np.ndarray] = None, y_val: Optional[np.ndarray] = None,
                         d_model: int = 64, nhead: int = 8, num_layers: int = 6,
                         epochs: int = 100, batch_size: int = 32, patience: int = 10) -> Dict:
        """
        Train Transformer model.
        
        Args:
            X_train: Training sequences
            y_train: Training targets
            X_val: Validation sequences
            y_val: Validation targets
            d_model: Model dimension
            nhead: Number of attention heads
            num_layers: Number of transformer layers
            epochs: Number of training epochs
            batch_size: Batch size
            patience: Early stopping patience
            
        Returns:
            Training history and metrics
        """
        try:
            import tensorflow as tf
            from tensorflow.keras.callbacks import EarlyStopping
            
            # Build model
            input_shape = (X_train.shape[1], X_train.shape[2])
            model = self.build_transformer_model(input_shape, d_model, nhead, num_layers)
            
            if model is None:
                return {'error': 'Failed to build transformer model'}
            
            # Callbacks
            callbacks = []
            if X_val is not None and y_val is not None:
                early_stopping = EarlyStopping(monitor='val_loss', patience=patience,
                                             restore_best_weights=True)
                callbacks.append(early_stopping)
            
            # Train model
            validation_data = (X_val, y_val) if X_val is not None else None
            
            history = model.fit(
                X_train, y_train,
                validation_data=validation_data,
                epochs=epochs,
                batch_size=batch_size,
                callbacks=callbacks,
                verbose=1
            )
            
            self.models['transformer'] = model
            self.model_history['transformer'] = history.history
            
            # Calculate final metrics
            train_loss = history.history['loss'][-1]
            val_loss = history.history['val_loss'][-1] if 'val_loss' in history.history else None
            
            performance = {
                'model_type': 'transformer',
                'train_loss': train_loss,
                'val_loss': val_loss,
                'epochs_trained': len(history.history['loss']),
                'input_shape': input_shape,
                'd_model': d_model,
                'nhead': nhead,
                'num_layers': num_layers
            }
            
            return performance
            
        except ImportError:
            print("TensorFlow not available. Transformer training not supported.")
            return {'error': 'TensorFlow required for Transformer'}
    
    def predict(self, model_name: str, X: np.ndarray) -> np.ndarray:
        """
        Make predictions using a trained model.
        
        Args:
            model_name: Name of the model to use
            X: Input sequences
            
        Returns:
            Predictions array
        """
        if model_name not in self.models:
            raise ValueError(f"Model {model_name} not found. Available models: {list(self.models.keys())}")
        
        model = self.models[model_name]
        
        if isinstance(model, dict) and model.get('type') == 'simple_lstm':
            # Handle simple LSTM implementation
            X_flat = X.reshape(X.shape[0], -1)
            X_with_bias = np.column_stack([np.ones(X_flat.shape[0]), X_flat])
            return X_with_bias @ model['theta']
        else:
            # Handle TensorFlow models
            return model.predict(X).flatten()
    
    def evaluate_model(self, model_name: str, X_test: np.ndarray, y_test: np.ndarray) -> Dict:
        """
        Evaluate a trained model on test data.
        
        Args:
            model_name: Name of the model to evaluate
            X_test: Test sequences
            y_test: Test targets
            
        Returns:
            Evaluation metrics
        """
        y_pred = self.predict(model_name, X_test)
        
        mse = np.mean((y_test - y_pred) ** 2)
        rmse = np.sqrt(mse)
        mae = np.mean(np.abs(y_test - y_pred))
        
        # R-squared
        ss_res = np.sum((y_test - y_pred) ** 2)
        ss_tot = np.sum((y_test - np.mean(y_test)) ** 2)
        r2 = 1 - (ss_res / ss_tot)
        
        return {
            'mse': mse,
            'rmse': rmse,
            'mae': mae,
            'r2': r2
        }
    
    def save_models(self, save_dir: str = "models"):
        """Save trained models to disk."""
        save_path = Path(save_dir)
        save_path.mkdir(exist_ok=True)
        
        for model_name, model in self.models.items():
            if hasattr(model, 'save'):
                # TensorFlow model
                model_dir = save_path / f"{model_name}_tf"
                model.save(model_dir)
            else:
                # Simple model
                model_file = save_path / f"{model_name}_simple.pkl"
                with open(model_file, 'wb') as f:
                    pickle.dump(model, f)
        
        # Save training history
        history_file = save_path / "dl_model_history.pkl"
        with open(history_file, 'wb') as f:
            pickle.dump(self.model_history, f)
        
        print(f"Saved {len(self.models)} deep learning models to {save_path}")
    
    def load_models(self, save_dir: str = "models"):
        """Load trained models from disk."""
        save_path = Path(save_dir)
        
        if not save_path.exists():
            print(f"Model directory {save_path} does not exist")
            return
        
        # Load TensorFlow models
        try:
            import tensorflow as tf
            
            for model_dir in save_path.glob("*_tf"):
                if model_dir.is_dir():
                    model_name = model_dir.name.replace("_tf", "")
                    self.models[model_name] = tf.keras.models.load_model(model_dir)
        except ImportError:
            pass
        
        # Load simple models
        for model_file in save_path.glob("*_simple.pkl"):
            model_name = model_file.name.replace("_simple.pkl", "")
            with open(model_file, 'rb') as f:
                self.models[model_name] = pickle.load(f)
        
        # Load training history
        history_file = save_path / "dl_model_history.pkl"
        if history_file.exists():
            with open(history_file, 'rb') as f:
                self.model_history = pickle.load(f)
        
        print(f"Loaded {len(self.models)} deep learning models from {save_path}")
    
    def get_model_summary(self) -> Dict:
        """Get summary of all trained models."""
        summary = {
            'total_models': len(self.models),
            'model_names': list(self.models.keys()),
            'model_types': {}
        }
        
        for name, model in self.models.items():
            if isinstance(model, dict):
                summary['model_types'][name] = model.get('type', 'unknown')
            else:
                summary['model_types'][name] = 'tensorflow'
        
        return summary
