"""
Backtesting engine for trading strategies.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any, Union
from datetime import datetime, timedelta
import json
from pathlib import Path


class BacktestEngine:
    """
    Comprehensive backtesting engine for trading strategies.
    """
    
    def __init__(self, initial_capital: float = 10000.0, commission: float = 0.001,
                 slippage: float = 0.0001):
        """
        Initialize BacktestEngine.
        
        Args:
            initial_capital: Starting capital
            commission: Commission rate (e.g., 0.001 = 0.1%)
            slippage: Slippage rate (e.g., 0.0001 = 0.01%)
        """
        self.initial_capital = initial_capital
        self.commission = commission
        self.slippage = slippage
        
        # Portfolio state
        self.capital = initial_capital
        self.positions = {}  # {symbol: position_size}
        self.portfolio_value = initial_capital
        
        # Trade tracking
        self.trades = []
        self.portfolio_history = []
        self.equity_curve = []
        
        # Performance metrics
        self.metrics = {}
        
    def reset(self):
        """Reset the backtesting engine to initial state."""
        self.capital = self.initial_capital
        self.positions = {}
        self.portfolio_value = self.initial_capital
        self.trades = []
        self.portfolio_history = []
        self.equity_curve = []
        self.metrics = {}
    
    def execute_trade(self, symbol: str, action: str, quantity: float, 
                     price: float, timestamp: datetime, 
                     stop_loss: Optional[float] = None,
                     take_profit: Optional[float] = None) -> Dict:
        """
        Execute a trade.
        
        Args:
            symbol: Trading symbol
            action: 'buy' or 'sell'
            quantity: Number of shares/units
            price: Execution price
            timestamp: Trade timestamp
            stop_loss: Stop loss price
            take_profit: Take profit price
            
        Returns:
            Trade execution result
        """
        # Apply slippage
        if action == 'buy':
            execution_price = price * (1 + self.slippage)
        else:
            execution_price = price * (1 - self.slippage)
        
        # Calculate trade value
        trade_value = quantity * execution_price
        commission_cost = trade_value * self.commission
        total_cost = trade_value + commission_cost
        
        # Check if we have enough capital for buy orders
        if action == 'buy' and total_cost > self.capital:
            return {
                'status': 'rejected',
                'reason': 'insufficient_capital',
                'required': total_cost,
                'available': self.capital
            }
        
        # Update positions
        current_position = self.positions.get(symbol, 0)
        
        if action == 'buy':
            new_position = current_position + quantity
            self.capital -= total_cost
        else:  # sell
            new_position = current_position - quantity
            self.capital += trade_value - commission_cost
        
        self.positions[symbol] = new_position
        
        # Record trade
        trade_record = {
            'timestamp': timestamp,
            'symbol': symbol,
            'action': action,
            'quantity': quantity,
            'price': execution_price,
            'commission': commission_cost,
            'total_cost': total_cost,
            'position_after': new_position,
            'capital_after': self.capital,
            'stop_loss': stop_loss,
            'take_profit': take_profit
        }
        
        self.trades.append(trade_record)
        
        return {
            'status': 'executed',
            'trade': trade_record
        }
    
    def update_portfolio_value(self, prices: Dict[str, float], timestamp: datetime):
        """
        Update portfolio value based on current prices.
        
        Args:
            prices: Dictionary of current prices {symbol: price}
            timestamp: Current timestamp
        """
        position_value = 0
        
        for symbol, position in self.positions.items():
            if symbol in prices and position != 0:
                position_value += position * prices[symbol]
        
        self.portfolio_value = self.capital + position_value
        
        # Record portfolio state
        portfolio_state = {
            'timestamp': timestamp,
            'capital': self.capital,
            'position_value': position_value,
            'total_value': self.portfolio_value,
            'positions': self.positions.copy()
        }
        
        self.portfolio_history.append(portfolio_state)
        self.equity_curve.append({
            'timestamp': timestamp,
            'value': self.portfolio_value
        })
    
    def run_backtest(self, data: pd.DataFrame, strategy_func: callable,
                    symbol: str = 'EURUSD') -> Dict:
        """
        Run backtest with a given strategy.
        
        Args:
            data: Price data DataFrame
            strategy_func: Strategy function that returns signals
            symbol: Trading symbol
            
        Returns:
            Backtest results
        """
        self.reset()
        
        print(f"Running backtest for {symbol} with {len(data)} data points...")
        
        # Generate signals
        signals = strategy_func(data)
        
        if len(signals) != len(data):
            raise ValueError(f"Signal length {len(signals)} doesn't match data length {len(data)}")
        
        # Execute trades based on signals
        for i, (idx, row) in enumerate(data.iterrows()):
            timestamp = row['timestamp'] if 'timestamp' in row else datetime.now()
            price = row['close']
            signal = signals[i]
            
            # Update portfolio value
            self.update_portfolio_value({symbol: price}, timestamp)
            
            # Execute trades based on signal
            if signal == 1:  # Buy signal
                # Calculate position size (simple: use 10% of capital)
                position_size = (self.capital * 0.1) / price
                if position_size > 0:
                    self.execute_trade(symbol, 'buy', position_size, price, timestamp)
            
            elif signal == -1:  # Sell signal
                # Close all positions
                current_position = self.positions.get(symbol, 0)
                if current_position > 0:
                    self.execute_trade(symbol, 'sell', current_position, price, timestamp)
        
        # Calculate final metrics
        self.calculate_performance_metrics()
        
        return {
            'trades': self.trades,
            'portfolio_history': self.portfolio_history,
            'equity_curve': self.equity_curve,
            'metrics': self.metrics,
            'final_value': self.portfolio_value,
            'total_return': (self.portfolio_value - self.initial_capital) / self.initial_capital
        }
    
    def calculate_performance_metrics(self) -> Dict:
        """Calculate comprehensive performance metrics."""
        if not self.equity_curve:
            return {}
        
        # Convert equity curve to pandas Series
        equity_df = pd.DataFrame(self.equity_curve)
        equity_df['timestamp'] = pd.to_datetime(equity_df['timestamp'])
        equity_df = equity_df.set_index('timestamp')
        
        # Calculate returns
        equity_df['returns'] = equity_df['value'].pct_change()
        equity_df = equity_df.dropna()
        
        if len(equity_df) == 0:
            return {}
        
        # Basic metrics
        total_return = (self.portfolio_value - self.initial_capital) / self.initial_capital
        
        # Annualized return (assuming daily data)
        days = (equity_df.index[-1] - equity_df.index[0]).days
        if days > 0:
            annualized_return = (1 + total_return) ** (365 / days) - 1
        else:
            annualized_return = 0
        
        # Volatility
        volatility = equity_df['returns'].std()
        annualized_volatility = volatility * np.sqrt(365) if volatility > 0 else 0
        
        # Sharpe ratio (assuming 0% risk-free rate)
        sharpe_ratio = annualized_return / annualized_volatility if annualized_volatility > 0 else 0
        
        # Maximum drawdown
        equity_df['cummax'] = equity_df['value'].cummax()
        equity_df['drawdown'] = (equity_df['value'] - equity_df['cummax']) / equity_df['cummax']
        max_drawdown = equity_df['drawdown'].min()
        
        # Calmar ratio
        calmar_ratio = annualized_return / abs(max_drawdown) if max_drawdown != 0 else 0
        
        # Trade statistics
        if self.trades:
            trade_df = pd.DataFrame(self.trades)
            
            # Calculate P&L for each trade
            trade_pnl = []
            for i, trade in enumerate(self.trades):
                if trade['action'] == 'sell' and i > 0:
                    # Find corresponding buy trade
                    for j in range(i-1, -1, -1):
                        if (self.trades[j]['action'] == 'buy' and 
                            self.trades[j]['symbol'] == trade['symbol']):
                            buy_price = self.trades[j]['price']
                            sell_price = trade['price']
                            quantity = min(self.trades[j]['quantity'], trade['quantity'])
                            pnl = (sell_price - buy_price) * quantity
                            trade_pnl.append(pnl)
                            break
            
            if trade_pnl:
                win_rate = len([pnl for pnl in trade_pnl if pnl > 0]) / len(trade_pnl)
                avg_win = np.mean([pnl for pnl in trade_pnl if pnl > 0]) if any(pnl > 0 for pnl in trade_pnl) else 0
                avg_loss = np.mean([pnl for pnl in trade_pnl if pnl < 0]) if any(pnl < 0 for pnl in trade_pnl) else 0
                profit_factor = abs(avg_win / avg_loss) if avg_loss != 0 else float('inf')
            else:
                win_rate = 0
                avg_win = 0
                avg_loss = 0
                profit_factor = 0
            
            total_trades = len(self.trades)
        else:
            win_rate = 0
            avg_win = 0
            avg_loss = 0
            profit_factor = 0
            total_trades = 0
        
        self.metrics = {
            'total_return': total_return,
            'annualized_return': annualized_return,
            'volatility': annualized_volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'calmar_ratio': calmar_ratio,
            'win_rate': win_rate,
            'profit_factor': profit_factor,
            'total_trades': total_trades,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'final_value': self.portfolio_value,
            'initial_capital': self.initial_capital
        }
        
        return self.metrics
    
    def get_trade_summary(self) -> Dict:
        """Get summary of all trades."""
        if not self.trades:
            return {'total_trades': 0}
        
        trade_df = pd.DataFrame(self.trades)
        
        summary = {
            'total_trades': len(self.trades),
            'buy_trades': len(trade_df[trade_df['action'] == 'buy']),
            'sell_trades': len(trade_df[trade_df['action'] == 'sell']),
            'total_commission': trade_df['commission'].sum(),
            'avg_trade_size': trade_df['quantity'].mean(),
            'largest_trade': trade_df['total_cost'].max(),
            'smallest_trade': trade_df['total_cost'].min()
        }
        
        return summary
    
    def save_results(self, filepath: str):
        """Save backtest results to file."""
        results = {
            'metrics': self.metrics,
            'trades': self.trades,
            'portfolio_history': self.portfolio_history,
            'equity_curve': self.equity_curve,
            'trade_summary': self.get_trade_summary(),
            'backtest_config': {
                'initial_capital': self.initial_capital,
                'commission': self.commission,
                'slippage': self.slippage
            }
        }
        
        # Convert datetime objects to strings for JSON serialization
        def convert_datetime(obj):
            if isinstance(obj, datetime):
                return obj.isoformat()
            elif isinstance(obj, dict):
                return {k: convert_datetime(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_datetime(item) for item in obj]
            else:
                return obj
        
        results = convert_datetime(results)
        
        with open(filepath, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"Backtest results saved to {filepath}")
    
    def load_results(self, filepath: str):
        """Load backtest results from file."""
        with open(filepath, 'r') as f:
            results = json.load(f)
        
        self.metrics = results.get('metrics', {})
        self.trades = results.get('trades', [])
        self.portfolio_history = results.get('portfolio_history', [])
        self.equity_curve = results.get('equity_curve', [])
        
        # Convert timestamp strings back to datetime objects
        for trade in self.trades:
            if 'timestamp' in trade:
                trade['timestamp'] = datetime.fromisoformat(trade['timestamp'])
        
        for state in self.portfolio_history:
            if 'timestamp' in state:
                state['timestamp'] = datetime.fromisoformat(state['timestamp'])
        
        for point in self.equity_curve:
            if 'timestamp' in point:
                point['timestamp'] = datetime.fromisoformat(point['timestamp'])
        
        print(f"Backtest results loaded from {filepath}")


# Example strategy functions
def simple_moving_average_strategy(data: pd.DataFrame, short_window: int = 10, 
                                  long_window: int = 20) -> List[int]:
    """
    Simple moving average crossover strategy.
    
    Args:
        data: Price data
        short_window: Short MA period
        long_window: Long MA period
        
    Returns:
        List of signals (1=buy, -1=sell, 0=hold)
    """
    short_ma = data['close'].rolling(window=short_window).mean()
    long_ma = data['close'].rolling(window=long_window).mean()
    
    signals = []
    for i in range(len(data)):
        if i < long_window:
            signals.append(0)  # Not enough data
        elif short_ma.iloc[i] > long_ma.iloc[i] and short_ma.iloc[i-1] <= long_ma.iloc[i-1]:
            signals.append(1)  # Buy signal
        elif short_ma.iloc[i] < long_ma.iloc[i] and short_ma.iloc[i-1] >= long_ma.iloc[i-1]:
            signals.append(-1)  # Sell signal
        else:
            signals.append(0)  # Hold
    
    return signals


def rsi_strategy(data: pd.DataFrame, rsi_period: int = 14, 
                oversold: float = 30, overbought: float = 70) -> List[int]:
    """
    RSI-based strategy.
    
    Args:
        data: Price data
        rsi_period: RSI calculation period
        oversold: Oversold threshold
        overbought: Overbought threshold
        
    Returns:
        List of signals (1=buy, -1=sell, 0=hold)
    """
    # Calculate RSI
    delta = data['close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=rsi_period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=rsi_period).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    
    signals = []
    for i in range(len(data)):
        if i < rsi_period:
            signals.append(0)  # Not enough data
        elif rsi.iloc[i] < oversold:
            signals.append(1)  # Buy signal (oversold)
        elif rsi.iloc[i] > overbought:
            signals.append(-1)  # Sell signal (overbought)
        else:
            signals.append(0)  # Hold
    
    return signals
