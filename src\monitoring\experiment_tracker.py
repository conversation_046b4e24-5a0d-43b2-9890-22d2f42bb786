"""
Experiment tracking and monitoring system for trading models.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any, Union
from datetime import datetime, timedelta
import json
import sqlite3
from pathlib import Path
import uuid


class ExperimentTracker:
    """
    Experiment tracking system for trading model development and monitoring.
    """
    
    def __init__(self, db_path: str = "data/experiments.db"):
        """
        Initialize ExperimentTracker.
        
        Args:
            db_path: Path to SQLite database for storing experiments
        """
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(exist_ok=True)
        self._init_database()
        
    def _init_database(self):
        """Initialize the experiments database."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Experiments table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS experiments (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    description TEXT,
                    model_type TEXT,
                    dataset TEXT,
                    parameters TEXT,
                    metrics TEXT,
                    status TEXT,
                    start_time TIMESTAMP,
                    end_time TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Model runs table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS model_runs (
                    id TEXT PRIMARY KEY,
                    experiment_id TEXT,
                    run_name TEXT,
                    model_config TEXT,
                    training_metrics TEXT,
                    validation_metrics TEXT,
                    test_metrics TEXT,
                    artifacts TEXT,
                    status TEXT,
                    start_time TIMESTAMP,
                    end_time TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (experiment_id) REFERENCES experiments (id)
                )
            ''')
            
            # Backtest results table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS backtest_results (
                    id TEXT PRIMARY KEY,
                    experiment_id TEXT,
                    strategy_name TEXT,
                    symbol TEXT,
                    timeframe TEXT,
                    start_date DATE,
                    end_date DATE,
                    initial_capital REAL,
                    final_value REAL,
                    total_return REAL,
                    sharpe_ratio REAL,
                    max_drawdown REAL,
                    total_trades INTEGER,
                    win_rate REAL,
                    profit_factor REAL,
                    parameters TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (experiment_id) REFERENCES experiments (id)
                )
            ''')
            
            # Live trading performance table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS live_performance (
                    id TEXT PRIMARY KEY,
                    experiment_id TEXT,
                    date DATE,
                    portfolio_value REAL,
                    daily_return REAL,
                    cumulative_return REAL,
                    drawdown REAL,
                    trades_count INTEGER,
                    win_rate REAL,
                    profit_loss REAL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (experiment_id) REFERENCES experiments (id)
                )
            ''')
            
            conn.commit()
    
    def create_experiment(self, name: str, description: str = "", 
                         model_type: str = "", dataset: str = "") -> str:
        """
        Create a new experiment.
        
        Args:
            name: Experiment name
            description: Experiment description
            model_type: Type of model being tested
            dataset: Dataset being used
            
        Returns:
            Experiment ID
        """
        experiment_id = str(uuid.uuid4())
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO experiments (id, name, description, model_type, dataset, status, start_time)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (experiment_id, name, description, model_type, dataset, 'RUNNING', datetime.now()))
            conn.commit()
        
        print(f"Created experiment: {name} (ID: {experiment_id})")
        return experiment_id
    
    def log_model_run(self, experiment_id: str, run_name: str, 
                     model_config: Dict, training_metrics: Dict,
                     validation_metrics: Optional[Dict] = None,
                     test_metrics: Optional[Dict] = None,
                     artifacts: Optional[Dict] = None) -> str:
        """
        Log a model training run.
        
        Args:
            experiment_id: Parent experiment ID
            run_name: Name of this run
            model_config: Model configuration
            training_metrics: Training metrics
            validation_metrics: Validation metrics
            test_metrics: Test metrics
            artifacts: Artifacts (model paths, plots, etc.)
            
        Returns:
            Run ID
        """
        run_id = str(uuid.uuid4())
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO model_runs (
                    id, experiment_id, run_name, model_config, 
                    training_metrics, validation_metrics, test_metrics, 
                    artifacts, status, start_time
                )
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                run_id, experiment_id, run_name,
                json.dumps(model_config),
                json.dumps(training_metrics),
                json.dumps(validation_metrics) if validation_metrics else None,
                json.dumps(test_metrics) if test_metrics else None,
                json.dumps(artifacts) if artifacts else None,
                'COMPLETED', datetime.now()
            ))
            conn.commit()
        
        print(f"Logged model run: {run_name} (ID: {run_id})")
        return run_id
    
    def log_backtest_result(self, experiment_id: str, strategy_name: str,
                           symbol: str, timeframe: str, start_date: str,
                           end_date: str, results: Dict, parameters: Dict) -> str:
        """
        Log backtest results.
        
        Args:
            experiment_id: Parent experiment ID
            strategy_name: Name of the strategy
            symbol: Trading symbol
            timeframe: Timeframe used
            start_date: Backtest start date
            end_date: Backtest end date
            results: Backtest results
            parameters: Strategy parameters
            
        Returns:
            Backtest result ID
        """
        result_id = str(uuid.uuid4())
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO backtest_results (
                    id, experiment_id, strategy_name, symbol, timeframe,
                    start_date, end_date, initial_capital, final_value,
                    total_return, sharpe_ratio, max_drawdown, total_trades,
                    win_rate, profit_factor, parameters
                )
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                result_id, experiment_id, strategy_name, symbol, timeframe,
                start_date, end_date,
                results.get('initial_capital', 0),
                results.get('final_value', 0),
                results.get('total_return', 0),
                results.get('sharpe_ratio', 0),
                results.get('max_drawdown', 0),
                results.get('total_trades', 0),
                results.get('win_rate', 0),
                results.get('profit_factor', 0),
                json.dumps(parameters)
            ))
            conn.commit()
        
        print(f"Logged backtest result: {strategy_name} on {symbol} (ID: {result_id})")
        return result_id
    
    def log_live_performance(self, experiment_id: str, date: str,
                           portfolio_value: float, daily_return: float,
                           cumulative_return: float, drawdown: float,
                           trades_count: int, win_rate: float,
                           profit_loss: float) -> str:
        """
        Log daily live trading performance.
        
        Args:
            experiment_id: Parent experiment ID
            date: Trading date
            portfolio_value: Current portfolio value
            daily_return: Daily return
            cumulative_return: Cumulative return
            drawdown: Current drawdown
            trades_count: Number of trades
            win_rate: Win rate
            profit_loss: Daily P&L
            
        Returns:
            Performance record ID
        """
        record_id = str(uuid.uuid4())
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO live_performance (
                    id, experiment_id, date, portfolio_value, daily_return,
                    cumulative_return, drawdown, trades_count, win_rate, profit_loss
                )
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                record_id, experiment_id, date, portfolio_value, daily_return,
                cumulative_return, drawdown, trades_count, win_rate, profit_loss
            ))
            conn.commit()
        
        return record_id
    
    def get_experiment(self, experiment_id: str) -> Optional[Dict]:
        """Get experiment details."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM experiments WHERE id = ?', (experiment_id,))
            row = cursor.fetchone()
            
            if row:
                columns = [desc[0] for desc in cursor.description]
                return dict(zip(columns, row))
            return None
    
    def list_experiments(self, limit: int = 50) -> List[Dict]:
        """List all experiments."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT * FROM experiments 
                ORDER BY created_at DESC 
                LIMIT ?
            ''', (limit,))
            rows = cursor.fetchall()
            
            columns = [desc[0] for desc in cursor.description]
            return [dict(zip(columns, row)) for row in rows]
    
    def get_model_runs(self, experiment_id: str) -> List[Dict]:
        """Get all model runs for an experiment."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT * FROM model_runs 
                WHERE experiment_id = ? 
                ORDER BY created_at DESC
            ''', (experiment_id,))
            rows = cursor.fetchall()
            
            columns = [desc[0] for desc in cursor.description]
            return [dict(zip(columns, row)) for row in rows]
    
    def get_backtest_results(self, experiment_id: str) -> List[Dict]:
        """Get all backtest results for an experiment."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT * FROM backtest_results 
                WHERE experiment_id = ? 
                ORDER BY created_at DESC
            ''', (experiment_id,))
            rows = cursor.fetchall()
            
            columns = [desc[0] for desc in cursor.description]
            return [dict(zip(columns, row)) for row in rows]
    
    def get_live_performance(self, experiment_id: str, 
                           days: int = 30) -> List[Dict]:
        """Get live performance data for an experiment."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT * FROM live_performance 
                WHERE experiment_id = ? 
                AND date >= date('now', '-{} days')
                ORDER BY date DESC
            '''.format(days), (experiment_id,))
            rows = cursor.fetchall()
            
            columns = [desc[0] for desc in cursor.description]
            return [dict(zip(columns, row)) for row in rows]
    
    def compare_experiments(self, experiment_ids: List[str]) -> Dict[str, Any]:
        """Compare multiple experiments."""
        comparison = {
            'experiments': [],
            'best_backtest': None,
            'best_live_performance': None
        }
        
        for exp_id in experiment_ids:
            exp = self.get_experiment(exp_id)
            if exp:
                # Get backtest results
                backtests = self.get_backtest_results(exp_id)
                best_backtest = max(backtests, key=lambda x: x['total_return']) if backtests else None
                
                # Get recent live performance
                live_perf = self.get_live_performance(exp_id, 30)
                avg_daily_return = np.mean([p['daily_return'] for p in live_perf]) if live_perf else 0
                
                comparison['experiments'].append({
                    'id': exp_id,
                    'name': exp['name'],
                    'model_type': exp['model_type'],
                    'best_backtest_return': best_backtest['total_return'] if best_backtest else 0,
                    'avg_daily_return': avg_daily_return,
                    'total_backtests': len(backtests),
                    'live_trading_days': len(live_perf)
                })
        
        # Find best performers
        if comparison['experiments']:
            comparison['best_backtest'] = max(
                comparison['experiments'], 
                key=lambda x: x['best_backtest_return']
            )
            comparison['best_live_performance'] = max(
                comparison['experiments'], 
                key=lambda x: x['avg_daily_return']
            )
        
        return comparison
    
    def generate_experiment_report(self, experiment_id: str) -> Dict[str, Any]:
        """Generate comprehensive experiment report."""
        experiment = self.get_experiment(experiment_id)
        if not experiment:
            return {'error': 'Experiment not found'}
        
        model_runs = self.get_model_runs(experiment_id)
        backtest_results = self.get_backtest_results(experiment_id)
        live_performance = self.get_live_performance(experiment_id, 90)  # Last 90 days
        
        # Calculate summary statistics
        if backtest_results:
            avg_return = np.mean([r['total_return'] for r in backtest_results])
            avg_sharpe = np.mean([r['sharpe_ratio'] for r in backtest_results])
            avg_drawdown = np.mean([r['max_drawdown'] for r in backtest_results])
            best_strategy = max(backtest_results, key=lambda x: x['total_return'])
        else:
            avg_return = avg_sharpe = avg_drawdown = 0
            best_strategy = None
        
        if live_performance:
            total_live_return = live_performance[0]['cumulative_return'] if live_performance else 0
            avg_daily_return = np.mean([p['daily_return'] for p in live_performance])
            current_drawdown = live_performance[0]['drawdown'] if live_performance else 0
        else:
            total_live_return = avg_daily_return = current_drawdown = 0
        
        report = {
            'experiment': experiment,
            'summary': {
                'total_model_runs': len(model_runs),
                'total_backtests': len(backtest_results),
                'live_trading_days': len(live_performance),
                'avg_backtest_return': avg_return,
                'avg_sharpe_ratio': avg_sharpe,
                'avg_max_drawdown': avg_drawdown,
                'best_strategy': best_strategy,
                'total_live_return': total_live_return,
                'avg_daily_return': avg_daily_return,
                'current_drawdown': current_drawdown
            },
            'model_runs': model_runs,
            'backtest_results': backtest_results,
            'live_performance': live_performance[:30]  # Last 30 days
        }
        
        return report
    
    def update_experiment_status(self, experiment_id: str, status: str):
        """Update experiment status."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE experiments 
                SET status = ?, end_time = ?
                WHERE id = ?
            ''', (status, datetime.now() if status == 'COMPLETED' else None, experiment_id))
            conn.commit()
    
    def delete_experiment(self, experiment_id: str):
        """Delete an experiment and all related data."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Delete in order due to foreign key constraints
            cursor.execute('DELETE FROM live_performance WHERE experiment_id = ?', (experiment_id,))
            cursor.execute('DELETE FROM backtest_results WHERE experiment_id = ?', (experiment_id,))
            cursor.execute('DELETE FROM model_runs WHERE experiment_id = ?', (experiment_id,))
            cursor.execute('DELETE FROM experiments WHERE id = ?', (experiment_id,))
            
            conn.commit()
        
        print(f"Deleted experiment: {experiment_id}")
    
    def export_experiment_data(self, experiment_id: str, 
                              output_path: str = "experiment_export.json"):
        """Export experiment data to JSON file."""
        report = self.generate_experiment_report(experiment_id)
        
        # Convert datetime objects to strings for JSON serialization
        def convert_datetime(obj):
            if isinstance(obj, dict):
                return {k: convert_datetime(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_datetime(item) for item in obj]
            elif isinstance(obj, datetime):
                return obj.isoformat()
            else:
                return obj
        
        report = convert_datetime(report)
        
        with open(output_path, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        print(f"Exported experiment data to: {output_path}")
    
    def get_database_stats(self) -> Dict[str, int]:
        """Get database statistics."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            stats = {}
            
            # Count records in each table
            for table in ['experiments', 'model_runs', 'backtest_results', 'live_performance']:
                cursor.execute(f'SELECT COUNT(*) FROM {table}')
                stats[f'total_{table}'] = cursor.fetchone()[0]
            
            return stats
