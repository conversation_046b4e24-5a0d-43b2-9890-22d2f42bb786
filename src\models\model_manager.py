"""
Model manager for coordinating all trading models.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any, Union
import json
from pathlib import Path

from .supervised_models import SupervisedModels
from .deep_learning_models import DeepLearningModels


class ModelManager:
    """
    Manages all trading models including supervised learning and deep learning models.
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """
        Initialize ModelManager.
        
        Args:
            config: Model configuration dictionary
        """
        self.config = config or self._get_default_config()
        self.supervised_models = SupervisedModels()
        self.deep_learning_models = DeepLearningModels()
        self.ensemble_weights = {}
        self.model_performance = {}
        
    def _get_default_config(self) -> Dict:
        """Get default model configuration."""
        return {
            'models': {
                'linear_regression': {
                    'enabled': True,
                    'regularization': 'ridge',
                    'alpha': 1.0
                },
                'random_forest': {
                    'enabled': True,
                    'n_estimators': 100,
                    'max_depth': 10,
                    'min_samples_split': 5
                },
                'xgboost': {
                    'enabled': True,
                    'n_estimators': 100,
                    'max_depth': 6,
                    'learning_rate': 0.1
                },
                'lstm': {
                    'enabled': True,
                    'units': [50, 50],
                    'dropout': 0.2,
                    'epochs': 100,
                    'batch_size': 32,
                    'sequence_length': 60
                },
                'transformer': {
                    'enabled': False,
                    'd_model': 64,
                    'nhead': 8,
                    'num_layers': 6,
                    'epochs': 100,
                    'batch_size': 32,
                    'sequence_length': 60
                }
            },
            'training': {
                'train_ratio': 0.7,
                'val_ratio': 0.15,
                'test_ratio': 0.15,
                'target_column': 'close',
                'prediction_horizon': 1,
                'task_type': 'regression'
            }
        }
    
    def prepare_data_for_models(self, df: pd.DataFrame) -> Dict[str, Tuple]:
        """
        Prepare data for different model types.
        
        Args:
            df: Input DataFrame with features
            
        Returns:
            Dictionary with prepared data for each model type
        """
        target_column = self.config['training']['target_column']
        prediction_horizon = self.config['training']['prediction_horizon']
        task_type = self.config['training']['task_type']
        
        # Prepare data for supervised learning models
        X_supervised, y_supervised = self.supervised_models.prepare_data(
            df, target_column, prediction_horizon, task_type
        )
        
        # Split supervised learning data
        X_train_sup, X_val_sup, X_test_sup, y_train_sup, y_val_sup, y_test_sup = \
            self.supervised_models.split_data(
                X_supervised, y_supervised,
                self.config['training']['train_ratio'],
                self.config['training']['val_ratio']
            )
        
        # Prepare data for deep learning models (sequences)
        sequence_length = self.config['models']['lstm']['sequence_length']
        
        # Get numeric columns for sequence creation
        numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        if 'timestamp' in numeric_cols:
            numeric_cols.remove('timestamp')
        
        # Create sequences
        data_array = df[numeric_cols].values
        target_idx = numeric_cols.index(target_column)
        
        X_sequences, y_sequences = self.deep_learning_models.create_sequences(
            data_array, sequence_length, target_idx
        )
        
        # Split sequence data
        n_seq = len(X_sequences)
        train_end_seq = int(n_seq * self.config['training']['train_ratio'])
        val_end_seq = int(n_seq * (self.config['training']['train_ratio'] + 
                                  self.config['training']['val_ratio']))
        
        X_train_seq = X_sequences[:train_end_seq]
        X_val_seq = X_sequences[train_end_seq:val_end_seq]
        X_test_seq = X_sequences[val_end_seq:]
        
        y_train_seq = y_sequences[:train_end_seq]
        y_val_seq = y_sequences[train_end_seq:val_end_seq]
        y_test_seq = y_sequences[val_end_seq:]
        
        return {
            'supervised': {
                'X_train': X_train_sup,
                'X_val': X_val_sup,
                'X_test': X_test_sup,
                'y_train': y_train_sup,
                'y_val': y_val_sup,
                'y_test': y_test_sup
            },
            'sequences': {
                'X_train': X_train_seq,
                'X_val': X_val_seq,
                'X_test': X_test_seq,
                'y_train': y_train_seq,
                'y_val': y_val_seq,
                'y_test': y_test_seq
            }
        }
    
    def train_all_models(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Train all enabled models.
        
        Args:
            df: Input DataFrame with features
            
        Returns:
            Dictionary with training results for all models
        """
        print("Preparing data for model training...")
        data_splits = self.prepare_data_for_models(df)
        
        results = {}
        task_type = self.config['training']['task_type']
        
        # Train supervised learning models
        print("\nTraining supervised learning models...")
        
        # Linear Regression
        if self.config['models']['linear_regression']['enabled']:
            print("Training Linear Regression...")
            lr_config = self.config['models']['linear_regression']
            lr_result = self.supervised_models.train_linear_regression(
                data_splits['supervised']['X_train'],
                data_splits['supervised']['y_train'],
                lr_config['regularization'],
                lr_config['alpha']
            )
            results['linear_regression'] = lr_result
        
        # Random Forest
        if self.config['models']['random_forest']['enabled']:
            print("Training Random Forest...")
            rf_config = self.config['models']['random_forest']
            rf_result = self.supervised_models.train_random_forest(
                data_splits['supervised']['X_train'],
                data_splits['supervised']['y_train'],
                rf_config['n_estimators'],
                rf_config['max_depth'],
                rf_config['min_samples_split'],
                task_type
            )
            results['random_forest'] = rf_result
        
        # XGBoost
        if self.config['models']['xgboost']['enabled']:
            print("Training XGBoost...")
            xgb_config = self.config['models']['xgboost']
            xgb_result = self.supervised_models.train_gradient_boosting(
                data_splits['supervised']['X_train'],
                data_splits['supervised']['y_train'],
                xgb_config['n_estimators'],
                xgb_config['learning_rate'],
                xgb_config['max_depth'],
                task_type
            )
            results['xgboost'] = xgb_result
        
        # Train deep learning models
        print("\nTraining deep learning models...")
        
        # LSTM
        if self.config['models']['lstm']['enabled']:
            print("Training LSTM...")
            lstm_config = self.config['models']['lstm']
            lstm_result = self.deep_learning_models.train_lstm(
                data_splits['sequences']['X_train'],
                data_splits['sequences']['y_train'],
                data_splits['sequences']['X_val'],
                data_splits['sequences']['y_val'],
                lstm_config['units'],
                lstm_config['dropout'],
                lstm_config['epochs'],
                lstm_config['batch_size']
            )
            results['lstm'] = lstm_result
        
        # Transformer
        if self.config['models']['transformer']['enabled']:
            print("Training Transformer...")
            transformer_config = self.config['models']['transformer']
            transformer_result = self.deep_learning_models.train_transformer(
                data_splits['sequences']['X_train'],
                data_splits['sequences']['y_train'],
                data_splits['sequences']['X_val'],
                data_splits['sequences']['y_val'],
                transformer_config['d_model'],
                transformer_config['nhead'],
                transformer_config['num_layers'],
                transformer_config['epochs'],
                transformer_config['batch_size']
            )
            results['transformer'] = transformer_result
        
        # Store results
        self.model_performance = results
        
        print(f"\nTraining completed! Trained {len(results)} models.")
        return results
    
    def evaluate_all_models(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Evaluate all trained models on test data.
        
        Args:
            df: Input DataFrame with features
            
        Returns:
            Dictionary with evaluation results for all models
        """
        print("Evaluating all models...")
        data_splits = self.prepare_data_for_models(df)
        
        evaluation_results = {}
        task_type = self.config['training']['task_type']
        
        # Evaluate supervised learning models
        for model_name in ['linear_regression', 'random_forest', 'xgboost']:
            if model_name in self.supervised_models.models:
                print(f"Evaluating {model_name}...")
                eval_result = self.supervised_models.evaluate_model(
                    model_name,
                    data_splits['supervised']['X_test'],
                    data_splits['supervised']['y_test'],
                    task_type
                )
                evaluation_results[model_name] = eval_result
        
        # Evaluate deep learning models
        for model_name in ['lstm', 'transformer']:
            if model_name in self.deep_learning_models.models:
                print(f"Evaluating {model_name}...")
                eval_result = self.deep_learning_models.evaluate_model(
                    model_name,
                    data_splits['sequences']['X_test'],
                    data_splits['sequences']['y_test']
                )
                evaluation_results[model_name] = eval_result
        
        print(f"Evaluation completed for {len(evaluation_results)} models.")
        return evaluation_results
    
    def create_ensemble_predictions(self, df: pd.DataFrame, 
                                   weights: Optional[Dict[str, float]] = None) -> np.ndarray:
        """
        Create ensemble predictions from all models.
        
        Args:
            df: Input DataFrame with features
            weights: Optional weights for each model
            
        Returns:
            Ensemble predictions array
        """
        data_splits = self.prepare_data_for_models(df)
        predictions = {}
        
        # Get predictions from supervised learning models
        for model_name in ['linear_regression', 'random_forest', 'xgboost']:
            if model_name in self.supervised_models.models:
                pred = self.supervised_models.predict(
                    model_name, data_splits['supervised']['X_test']
                )
                predictions[model_name] = pred
        
        # Get predictions from deep learning models
        for model_name in ['lstm', 'transformer']:
            if model_name in self.deep_learning_models.models:
                pred = self.deep_learning_models.predict(
                    model_name, data_splits['sequences']['X_test']
                )
                predictions[model_name] = pred
        
        if not predictions:
            raise ValueError("No trained models available for ensemble")
        
        # Align prediction lengths (use minimum length)
        min_length = min(len(pred) for pred in predictions.values())
        aligned_predictions = {name: pred[:min_length] for name, pred in predictions.items()}
        
        # Create ensemble
        if weights is None:
            # Equal weights
            weights = {name: 1.0 / len(aligned_predictions) for name in aligned_predictions}
        
        ensemble_pred = np.zeros(min_length)
        for model_name, pred in aligned_predictions.items():
            weight = weights.get(model_name, 0.0)
            ensemble_pred += weight * pred
        
        return ensemble_pred
    
    def get_best_model(self, evaluation_results: Dict[str, Any], 
                      metric: str = 'r2') -> Tuple[str, float]:
        """
        Get the best performing model based on a metric.
        
        Args:
            evaluation_results: Results from evaluate_all_models
            metric: Metric to use for comparison
            
        Returns:
            Tuple of (best_model_name, best_score)
        """
        best_model = None
        best_score = float('-inf') if metric in ['r2', 'accuracy'] else float('inf')
        
        for model_name, results in evaluation_results.items():
            if metric in results:
                score = results[metric]
                
                if metric in ['r2', 'accuracy']:
                    # Higher is better
                    if score > best_score:
                        best_score = score
                        best_model = model_name
                else:
                    # Lower is better (mse, rmse, mae)
                    if score < best_score:
                        best_score = score
                        best_model = model_name
        
        return best_model, best_score
    
    def save_all_models(self, save_dir: str = "models"):
        """Save all trained models."""
        save_path = Path(save_dir)
        save_path.mkdir(exist_ok=True)
        
        # Save supervised models
        self.supervised_models.save_models(save_dir)
        
        # Save deep learning models
        self.deep_learning_models.save_models(save_dir)
        
        # Save model manager state
        manager_state = {
            'config': self.config,
            'ensemble_weights': self.ensemble_weights,
            'model_performance': self.model_performance
        }
        
        with open(save_path / "model_manager_state.json", 'w') as f:
            json.dump(manager_state, f, indent=2, default=str)
        
        print(f"Saved all models and manager state to {save_path}")
    
    def load_all_models(self, save_dir: str = "models"):
        """Load all trained models."""
        save_path = Path(save_dir)
        
        if not save_path.exists():
            print(f"Model directory {save_path} does not exist")
            return
        
        # Load supervised models
        self.supervised_models.load_models(save_dir)
        
        # Load deep learning models
        self.deep_learning_models.load_models(save_dir)
        
        # Load model manager state
        state_file = save_path / "model_manager_state.json"
        if state_file.exists():
            with open(state_file, 'r') as f:
                manager_state = json.load(f)
                self.config = manager_state.get('config', self.config)
                self.ensemble_weights = manager_state.get('ensemble_weights', {})
                self.model_performance = manager_state.get('model_performance', {})
        
        print(f"Loaded all models and manager state from {save_path}")
    
    def get_model_summary(self) -> Dict[str, Any]:
        """Get comprehensive summary of all models."""
        supervised_summary = self.supervised_models.get_model_summary()
        dl_summary = self.deep_learning_models.get_model_summary()
        
        summary = {
            'total_models': supervised_summary['total_models'] + dl_summary['total_models'],
            'supervised_models': supervised_summary,
            'deep_learning_models': dl_summary,
            'model_performance': self.model_performance,
            'ensemble_weights': self.ensemble_weights
        }
        
        return summary
