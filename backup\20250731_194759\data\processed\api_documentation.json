{"title": "AI Trading Bot API Documentation", "version": "1.0.0", "base_url": "http://localhost:8000", "endpoints": {"health": {"method": "GET", "path": "/health", "description": "Check API health status"}, "mt5_connect": {"method": "POST", "path": "/mt5/connect", "description": "Connect to MetaTrader 5"}, "mt5_account": {"method": "GET", "path": "/mt5/account", "description": "Get MT5 account information"}, "current_prices": {"method": "GET", "path": "/mt5/prices/{symbol}", "description": "Get current price for a symbol"}, "historical_data": {"method": "GET", "path": "/mt5/history/{symbol}", "description": "Get historical data", "parameters": {"timeframe": "M1, M5, M15, M30, H1, H4, D1", "count": "Number of bars to retrieve"}}, "place_order": {"method": "POST", "path": "/trading/order", "description": "Place a trading order", "body": {"symbol": "Trading symbol", "order_type": "buy or sell", "volume": "Order volume", "price": "Order price (optional)", "stop_loss": "Stop loss price (optional)", "take_profit": "Take profit price (optional)"}}, "get_positions": {"method": "GET", "path": "/trading/positions", "description": "Get current open positions"}, "close_position": {"method": "DELETE", "path": "/trading/position/{ticket}", "description": "Close a position by ticket"}, "model_summary": {"method": "GET", "path": "/models/summary", "description": "Get summary of trained models"}, "make_prediction": {"method": "POST", "path": "/models/predict", "description": "Make a prediction", "body": {"symbol": "Trading symbol", "model_name": "Model to use (optional)", "horizon": "Prediction horizon"}}, "run_backtest": {"method": "POST", "path": "/backtest/run", "description": "Run a backtest", "body": {"symbol": "Trading symbol", "strategy": "Strategy name", "initial_capital": "Starting capital"}}, "start_trading": {"method": "POST", "path": "/trading/start", "description": "Start automated trading"}, "stop_trading": {"method": "POST", "path": "/trading/stop", "description": "Stop automated trading"}, "system_status": {"method": "GET", "path": "/status", "description": "Get comprehensive system status"}}}