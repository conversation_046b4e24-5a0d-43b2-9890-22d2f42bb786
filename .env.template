# Environment Variables Template for AI Trading Bot
# Copy this file to .env and fill in your actual values

# MetaTrader 5 Configuration
MT5_SERVER=MetaQuotes-Demo
MT5_LOGIN=your_login_here
MT5_PASSWORD=your_password_here
MT5_PATH=

# Trading Configuration
TRADING_MODE=demo  # demo or live
ACCOUNT_BALANCE=10000
MAX_RISK_PER_TRADE=0.02

# API Keys (Optional)
ALPHA_VANTAGE_API_KEY=
NEWS_API_KEY=
ECONOMIC_CALENDAR_API_KEY=

# Database Configuration (Optional)
DATABASE_URL=postgresql://user:password@localhost/trading_bot

# MLflow Configuration
MLFLOW_TRACKING_URI=sqlite:///mlflow.db
MLFLOW_EXPERIMENT_NAME=trading_bot_experiments

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/trading_bot.log

# Email Notifications (Optional)
SMTP_SERVER=
SMTP_PORT=587
SMTP_USERNAME=
SMTP_PASSWORD=
NOTIFICATION_EMAIL=

# Cloud Storage (Optional)
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_REGION=us-east-1
S3_BUCKET=

# Security
SECRET_KEY=your_secret_key_here
API_TOKEN=your_api_token_here

# Development Settings
DEBUG=False
TESTING=False
