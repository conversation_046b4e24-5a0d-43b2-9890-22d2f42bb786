"""
Statistical features module for trading analysis.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Union
from scipy import stats


class StatisticalFeatures:
    """
    Implements various statistical features for trading analysis.
    """
    
    @staticmethod
    def returns(data: pd.Series, periods: int = 1) -> pd.Series:
        """
        Calculate simple returns.
        
        Args:
            data: Price series
            periods: Number of periods for return calculation
            
        Returns:
            Returns series
        """
        return data.pct_change(periods=periods)
    
    @staticmethod
    def log_returns(data: pd.Series, periods: int = 1) -> pd.Series:
        """
        Calculate logarithmic returns.
        
        Args:
            data: Price series
            periods: Number of periods for return calculation
            
        Returns:
            Log returns series
        """
        return np.log(data / data.shift(periods))
    
    @staticmethod
    def volatility(data: pd.Series, window: int = 20, annualize: bool = False) -> pd.Series:
        """
        Calculate rolling volatility.
        
        Args:
            data: Price series or returns series
            window: Rolling window size
            annualize: Whether to annualize volatility
            
        Returns:
            Volatility series
        """
        if data.name and 'return' in data.name.lower():
            # If already returns, use directly
            vol = data.rolling(window=window).std()
        else:
            # Calculate returns first
            returns = StatisticalFeatures.returns(data)
            vol = returns.rolling(window=window).std()
        
        if annualize:
            # Assume daily data, annualize with sqrt(252)
            vol = vol * np.sqrt(252)
        
        return vol
    
    @staticmethod
    def rolling_mean(data: pd.Series, window: int) -> pd.Series:
        """
        Calculate rolling mean.
        
        Args:
            data: Input series
            window: Rolling window size
            
        Returns:
            Rolling mean series
        """
        return data.rolling(window=window).mean()
    
    @staticmethod
    def rolling_std(data: pd.Series, window: int) -> pd.Series:
        """
        Calculate rolling standard deviation.
        
        Args:
            data: Input series
            window: Rolling window size
            
        Returns:
            Rolling standard deviation series
        """
        return data.rolling(window=window).std()
    
    @staticmethod
    def rolling_skewness(data: pd.Series, window: int) -> pd.Series:
        """
        Calculate rolling skewness.
        
        Args:
            data: Input series
            window: Rolling window size
            
        Returns:
            Rolling skewness series
        """
        return data.rolling(window=window).skew()
    
    @staticmethod
    def rolling_kurtosis(data: pd.Series, window: int) -> pd.Series:
        """
        Calculate rolling kurtosis.
        
        Args:
            data: Input series
            window: Rolling window size
            
        Returns:
            Rolling kurtosis series
        """
        return data.rolling(window=window).kurt()
    
    @staticmethod
    def rolling_quantile(data: pd.Series, window: int, quantile: float) -> pd.Series:
        """
        Calculate rolling quantile.
        
        Args:
            data: Input series
            window: Rolling window size
            quantile: Quantile to calculate (0-1)
            
        Returns:
            Rolling quantile series
        """
        return data.rolling(window=window).quantile(quantile)
    
    @staticmethod
    def z_score(data: pd.Series, window: int) -> pd.Series:
        """
        Calculate rolling z-score.
        
        Args:
            data: Input series
            window: Rolling window size
            
        Returns:
            Z-score series
        """
        rolling_mean = data.rolling(window=window).mean()
        rolling_std = data.rolling(window=window).std()
        
        z_score = (data - rolling_mean) / rolling_std
        return z_score
    
    @staticmethod
    def percentile_rank(data: pd.Series, window: int) -> pd.Series:
        """
        Calculate rolling percentile rank.
        
        Args:
            data: Input series
            window: Rolling window size
            
        Returns:
            Percentile rank series
        """
        def rank_pct(x):
            return stats.percentileofscore(x[:-1], x[-1]) / 100.0
        
        return data.rolling(window=window).apply(rank_pct, raw=True)
    
    @staticmethod
    def momentum(data: pd.Series, window: int) -> pd.Series:
        """
        Calculate momentum (rate of change).
        
        Args:
            data: Price series
            window: Lookback period
            
        Returns:
            Momentum series
        """
        return data / data.shift(window) - 1
    
    @staticmethod
    def rsi_divergence(price: pd.Series, rsi: pd.Series, window: int = 5) -> pd.Series:
        """
        Detect RSI divergence.
        
        Args:
            price: Price series
            rsi: RSI series
            window: Window for peak/trough detection
            
        Returns:
            Divergence signal series
        """
        # Find local peaks and troughs
        price_peaks = price.rolling(window=window, center=True).max() == price
        price_troughs = price.rolling(window=window, center=True).min() == price
        
        rsi_peaks = rsi.rolling(window=window, center=True).max() == rsi
        rsi_troughs = rsi.rolling(window=window, center=True).min() == rsi
        
        # Initialize divergence signal
        divergence = pd.Series(0, index=price.index)
        
        # Bullish divergence: price makes lower low, RSI makes higher low
        for i in range(window, len(price) - window):
            if price_troughs.iloc[i]:
                # Look for previous trough
                prev_troughs = price_troughs.iloc[max(0, i-50):i]
                if prev_troughs.any():
                    prev_idx = prev_troughs.idxmax()
                    if (price.iloc[i] < price.loc[prev_idx] and 
                        rsi.iloc[i] > rsi.loc[prev_idx]):
                        divergence.iloc[i] = 1  # Bullish divergence
        
        # Bearish divergence: price makes higher high, RSI makes lower high
        for i in range(window, len(price) - window):
            if price_peaks.iloc[i]:
                # Look for previous peak
                prev_peaks = price_peaks.iloc[max(0, i-50):i]
                if prev_peaks.any():
                    prev_idx = prev_peaks.idxmax()
                    if (price.iloc[i] > price.loc[prev_idx] and 
                        rsi.iloc[i] < rsi.loc[prev_idx]):
                        divergence.iloc[i] = -1  # Bearish divergence
        
        return divergence
    
    @staticmethod
    def correlation(series1: pd.Series, series2: pd.Series, window: int) -> pd.Series:
        """
        Calculate rolling correlation between two series.
        
        Args:
            series1: First series
            series2: Second series
            window: Rolling window size
            
        Returns:
            Rolling correlation series
        """
        return series1.rolling(window=window).corr(series2)
    
    @staticmethod
    def beta(returns: pd.Series, market_returns: pd.Series, window: int) -> pd.Series:
        """
        Calculate rolling beta.
        
        Args:
            returns: Asset returns
            market_returns: Market returns
            window: Rolling window size
            
        Returns:
            Rolling beta series
        """
        covariance = returns.rolling(window=window).cov(market_returns)
        market_variance = market_returns.rolling(window=window).var()
        
        beta = covariance / market_variance
        return beta
    
    @staticmethod
    def sharpe_ratio(returns: pd.Series, window: int, risk_free_rate: float = 0.0) -> pd.Series:
        """
        Calculate rolling Sharpe ratio.
        
        Args:
            returns: Returns series
            window: Rolling window size
            risk_free_rate: Risk-free rate (annualized)
            
        Returns:
            Rolling Sharpe ratio series
        """
        excess_returns = returns - risk_free_rate / 252  # Daily risk-free rate
        mean_excess = excess_returns.rolling(window=window).mean()
        std_excess = excess_returns.rolling(window=window).std()
        
        sharpe = mean_excess / std_excess * np.sqrt(252)  # Annualized
        return sharpe
    
    @staticmethod
    def max_drawdown(data: pd.Series, window: int) -> pd.Series:
        """
        Calculate rolling maximum drawdown.
        
        Args:
            data: Price or cumulative returns series
            window: Rolling window size
            
        Returns:
            Rolling maximum drawdown series
        """
        def calc_max_dd(x):
            cumulative = (1 + x).cumprod() if x.min() > -1 else x
            running_max = cumulative.expanding().max()
            drawdown = (cumulative - running_max) / running_max
            return drawdown.min()
        
        if data.name and 'return' in data.name.lower():
            return data.rolling(window=window).apply(calc_max_dd, raw=False)
        else:
            returns = StatisticalFeatures.returns(data)
            return returns.rolling(window=window).apply(calc_max_dd, raw=False)
    
    @staticmethod
    def hurst_exponent(data: pd.Series, window: int) -> pd.Series:
        """
        Calculate rolling Hurst exponent.
        
        Args:
            data: Price series
            window: Rolling window size
            
        Returns:
            Rolling Hurst exponent series
        """
        def calc_hurst(x):
            if len(x) < 10:
                return np.nan
            
            lags = range(2, min(20, len(x)//2))
            tau = [np.sqrt(np.std(np.subtract(x[lag:], x[:-lag]))) for lag in lags]
            
            if len(tau) < 2:
                return np.nan
            
            poly = np.polyfit(np.log(lags), np.log(tau), 1)
            return poly[0] * 2.0
        
        return data.rolling(window=window).apply(calc_hurst, raw=True)
    
    @staticmethod
    def fractal_dimension(data: pd.Series, window: int) -> pd.Series:
        """
        Calculate rolling fractal dimension.
        
        Args:
            data: Price series
            window: Rolling window size
            
        Returns:
            Rolling fractal dimension series
        """
        def calc_fractal_dim(x):
            if len(x) < 4:
                return np.nan
            
            # Higuchi's method
            k_max = min(10, len(x) // 4)
            lk = []
            
            for k in range(1, k_max + 1):
                lm = []
                for m in range(k):
                    ll = 0
                    n = int((len(x) - m - 1) / k)
                    if n > 0:
                        for i in range(1, n + 1):
                            ll += abs(x[m + i * k] - x[m + (i - 1) * k])
                        ll = ll * (len(x) - 1) / (k * n * k)
                        lm.append(ll)
                
                if lm:
                    lk.append(np.mean(lm))
            
            if len(lk) < 2:
                return np.nan
            
            # Linear regression
            x_vals = np.log(range(1, len(lk) + 1))
            y_vals = np.log(lk)
            
            if len(x_vals) > 1:
                slope = np.polyfit(x_vals, y_vals, 1)[0]
                return -slope
            else:
                return np.nan
        
        return data.rolling(window=window).apply(calc_fractal_dim, raw=True)
