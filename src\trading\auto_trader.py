"""
Automatic trading engine for AI Trading Bot.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
import time
import asyncio
import logging
from pathlib import Path
import sys

# Add parent directories to path
sys.path.append(str(Path(__file__).parent.parent))

from trading.mt5_connector import MT5Connector
from models.model_manager import ModelManager
from features.feature_engineer import FeatureEngineer
from risk_management.risk_manager import RiskManager, TrailingStopManager
from monitoring.dashboard import TradingDashboard


class AutoTrader:
    """
    Automatic trading engine that combines all components for live trading.
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """
        Initialize AutoTrader.
        
        Args:
            config: Trading configuration
        """
        self.config = config or self._get_default_config()
        self.running = False
        self.last_signal_time = {}
        self.active_positions = {}
        
        # Initialize components
        self.mt5_connector = MT5Connector(self.config.get('mt5', {}))
        self.model_manager = ModelManager(self.config.get('models', {}))
        self.feature_engineer = FeatureEngineer()
        self.risk_manager = RiskManager(self.config.get('risk_management', {}))
        self.trailing_manager = TrailingStopManager()
        self.dashboard = TradingDashboard()
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('logs/auto_trader.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def _get_default_config(self) -> Dict:
        """Get default auto-trading configuration."""
        return {
            'trading': {
                'enabled': True,
                'symbols': ['EURUSD', 'GBPUSD', 'USDJPY'],
                'timeframe': 'M15',
                'signal_interval': 300,  # 5 minutes between signal checks
                'min_signal_strength': 0.6,
                'max_trades_per_day': 10,
                'trading_hours': {
                    'start': '00:00',
                    'end': '23:59'
                }
            },
            'risk_management': {
                'position_sizing': {
                    'risk_per_trade': 0.30  # 30% risk as requested
                },
                'portfolio_limits': {
                    'max_daily_loss': 0.50,
                    'max_positions': 5
                }
            }
        }
    
    async def start_auto_trading(self):
        """Start the automatic trading engine."""
        self.logger.info("🤖 Starting Automatic Trading Engine")
        self.logger.info("="*60)
        
        # Connect to MT5
        if not self.mt5_connector.connect():
            self.logger.error("❌ Failed to connect to MT5")
            return False
        
        # Enable auto-trading
        if not self.mt5_connector.enable_auto_trading():
            self.logger.error("❌ Failed to enable auto-trading")
            return False
        
        # Check trading permissions
        permissions = self.mt5_connector.check_trading_permissions()
        if not permissions['auto_trading_allowed']:
            self.logger.error(f"❌ Auto-trading not allowed: {permissions.get('error', 'Unknown error')}")
            self.logger.info("💡 Make sure to enable 'Allow automated trading' in MT5")
            return False
        
        self.logger.info("✅ Auto-trading enabled and ready!")
        self.logger.info(f"   Account: {permissions['account_info']['login']}")
        self.logger.info(f"   Balance: ${permissions['account_info']['balance']:,.2f}")
        self.logger.info(f"   Server: {permissions['account_info']['server']}")
        
        # Start trading loop
        self.running = True
        await self._trading_loop()
        
        return True
    
    async def _trading_loop(self):
        """Main trading loop."""
        self.logger.info("🔄 Starting trading loop...")
        
        symbols = self.config['trading']['symbols']
        signal_interval = self.config['trading']['signal_interval']
        
        while self.running:
            try:
                # Check if trading hours
                if not self._is_trading_hours():
                    await asyncio.sleep(60)  # Check every minute
                    continue
                
                # Process each symbol
                for symbol in symbols:
                    await self._process_symbol(symbol)
                
                # Update trailing stops
                await self._update_trailing_stops()
                
                # Update dashboard
                await self._update_dashboard()
                
                # Wait for next signal check
                await asyncio.sleep(signal_interval)
                
            except Exception as e:
                self.logger.error(f"❌ Error in trading loop: {str(e)}")
                await asyncio.sleep(60)  # Wait before retrying
    
    async def _process_symbol(self, symbol: str):
        """Process trading signals for a symbol."""
        try:
            # Check if we can trade this symbol
            if not self._can_trade_symbol(symbol):
                return
            
            # Get market data
            data = self.mt5_connector.get_historical_data(symbol, 'M15', 100)
            if data is None or len(data) < 50:
                self.logger.warning(f"⚠️ Insufficient data for {symbol}")
                return
            
            # Generate features
            features_df = self.feature_engineer.create_comprehensive_features(data)
            if features_df is None or len(features_df) == 0:
                self.logger.warning(f"⚠️ Failed to generate features for {symbol}")
                return
            
            # Get AI prediction
            prediction = self._get_trading_signal(features_df, symbol)
            if prediction is None:
                return
            
            # Process trading signal
            await self._process_trading_signal(symbol, prediction, data.iloc[-1])
            
        except Exception as e:
            self.logger.error(f"❌ Error processing {symbol}: {str(e)}")
    
    def _get_trading_signal(self, features_df: pd.DataFrame, symbol: str) -> Optional[Dict]:
        """Get trading signal from AI models."""
        try:
            # Get ensemble prediction
            predictions = self.model_manager.create_ensemble_predictions(features_df)
            if predictions is None or len(predictions) == 0:
                return None
            
            # Get latest prediction
            latest_prediction = predictions[-1]
            signal_strength = abs(latest_prediction)
            
            # Check minimum signal strength
            min_strength = self.config['trading']['min_signal_strength']
            if signal_strength < min_strength:
                return None
            
            # Determine signal direction
            if latest_prediction > min_strength:
                signal = 'BUY'
            elif latest_prediction < -min_strength:
                signal = 'SELL'
            else:
                signal = 'HOLD'
            
            return {
                'symbol': symbol,
                'signal': signal,
                'strength': signal_strength,
                'prediction': latest_prediction,
                'timestamp': datetime.now()
            }
            
        except Exception as e:
            self.logger.error(f"❌ Error getting signal for {symbol}: {str(e)}")
            return None
    
    async def _process_trading_signal(self, symbol: str, signal: Dict, current_bar: pd.Series):
        """Process a trading signal and execute trade if appropriate."""
        try:
            if signal['signal'] == 'HOLD':
                return
            
            # Check if we already have a position for this symbol
            positions = self.mt5_connector.get_positions()
            existing_position = None
            for pos in positions:
                if pos['symbol'] == symbol:
                    existing_position = pos
                    break
            
            # If we have an existing position, check if signal agrees
            if existing_position:
                pos_type = existing_position['type']
                if (pos_type == 'buy' and signal['signal'] == 'SELL') or \
                   (pos_type == 'sell' and signal['signal'] == 'BUY'):
                    # Conflicting signal - close existing position
                    await self._close_position(existing_position['ticket'])
                return
            
            # Calculate position size and risk parameters
            account_info = self.mt5_connector.get_account_info()
            if not account_info:
                self.logger.error("❌ Failed to get account info")
                return
            
            entry_price = current_bar['close']
            
            # Calculate stop loss
            atr = self._calculate_atr(symbol)
            stop_loss = self.risk_manager.calculate_stop_loss(
                symbol, entry_price, signal['signal'].lower(), atr
            )
            
            # Calculate position size
            position_size = self.risk_manager.calculate_position_size(
                symbol, entry_price, stop_loss, account_info['balance']
            )
            
            # Validate trade
            trade_request = {
                'symbol': symbol,
                'direction': signal['signal'].lower(),
                'price': entry_price,
                'position_size': position_size,
                'stop_loss': stop_loss
            }
            
            current_positions = [
                {'symbol': pos['symbol'], 'value': pos['volume'] * pos['price']}
                for pos in positions
            ]
            
            market_data = {
                'price': entry_price,
                'atr': atr,
                'volatility': 0.02  # Default volatility
            }
            
            validation = self.risk_manager.validate_trade(
                trade_request, current_positions, account_info, market_data
            )
            
            if not validation['approved']:
                self.logger.warning(f"⚠️ Trade rejected for {symbol}: {validation['reasons']}")
                return
            
            # Execute trade
            await self._execute_trade(validation['adjusted_trade'], signal)
            
        except Exception as e:
            self.logger.error(f"❌ Error processing signal for {symbol}: {str(e)}")
    
    async def _execute_trade(self, trade: Dict, signal: Dict):
        """Execute a validated trade."""
        try:
            symbol = trade['symbol']
            direction = trade['direction']
            volume = trade['position_size']
            stop_loss = trade.get('stop_loss')
            take_profit = trade.get('take_profit')
            
            self.logger.info(f"🚀 Executing {direction.upper()} trade for {symbol}")
            self.logger.info(f"   Volume: {volume:.2f}")
            self.logger.info(f"   Stop Loss: {stop_loss:.5f}")
            self.logger.info(f"   Signal Strength: {signal['strength']:.2f}")
            
            # Place market order
            result = self.mt5_connector.place_market_order(
                symbol=symbol,
                order_type=direction,
                volume=volume,
                stop_loss=stop_loss,
                take_profit=take_profit,
                comment=f"AI Bot - {signal['strength']:.2f}"
            )
            
            if result['status'] == 'success':
                self.logger.info(f"✅ Trade executed successfully!")
                self.logger.info(f"   Ticket: {result.get('order', 'N/A')}")
                self.logger.info(f"   Price: {result.get('price', 'N/A')}")
                
                # Initialize trailing stop
                if 'order' in result:
                    self.trailing_manager.initialize_trailing_stop(
                        str(result['order']),
                        result['price'],
                        direction,
                        stop_loss
                    )
                
                # Update dashboard
                self._log_trade_to_dashboard(trade, signal, result)
                
            else:
                self.logger.error(f"❌ Trade execution failed: {result.get('message', 'Unknown error')}")
                
        except Exception as e:
            self.logger.error(f"❌ Error executing trade: {str(e)}")
    
    async def _close_position(self, ticket: int):
        """Close a position."""
        try:
            result = self.mt5_connector.close_position(ticket)
            if result['status'] == 'success':
                self.logger.info(f"✅ Position {ticket} closed successfully")
                # Remove from trailing stop tracking
                self.trailing_manager.remove_position(str(ticket))
            else:
                self.logger.error(f"❌ Failed to close position {ticket}: {result.get('message')}")
        except Exception as e:
            self.logger.error(f"❌ Error closing position {ticket}: {str(e)}")
    
    async def _update_trailing_stops(self):
        """Update trailing stops for all positions."""
        try:
            positions = self.mt5_connector.get_positions()
            if not positions:
                return
            
            # Get current prices
            current_prices = {}
            for pos in positions:
                symbol = pos['symbol']
                tick = self.mt5_connector.get_current_prices([symbol])
                if tick and symbol in tick:
                    current_prices[symbol] = tick[symbol]['bid'] if pos['type'] == 'buy' else tick[symbol]['ask']
            
            # Update trailing stops
            for pos in positions:
                ticket = str(pos['ticket'])
                symbol = pos['symbol']
                
                if ticket in self.trailing_manager.position_trails and symbol in current_prices:
                    update_result = self.trailing_manager.update_trailing_stop(
                        ticket, current_prices[symbol]
                    )
                    
                    if update_result['stop_updated']:
                        # Modify position in MT5
                        modify_result = self.mt5_connector.modify_position(
                            int(ticket), 
                            stop_loss=update_result['new_stop']
                        )
                        
                        if modify_result['status'] == 'success':
                            self.logger.info(f"📈 Trailing stop updated for {symbol}: {update_result['new_stop']:.5f}")
                        
        except Exception as e:
            self.logger.error(f"❌ Error updating trailing stops: {str(e)}")
    
    def _can_trade_symbol(self, symbol: str) -> bool:
        """Check if we can trade a symbol."""
        # Check if market is open
        if not self.mt5_connector.is_market_open(symbol):
            return False
        
        # Check daily trade limits
        # Implementation depends on your tracking system
        
        return True
    
    def _is_trading_hours(self) -> bool:
        """Check if current time is within trading hours."""
        now = datetime.now().time()
        trading_hours = self.config['trading']['trading_hours']
        
        start_time = datetime.strptime(trading_hours['start'], '%H:%M').time()
        end_time = datetime.strptime(trading_hours['end'], '%H:%M').time()
        
        return start_time <= now <= end_time
    
    def _calculate_atr(self, symbol: str) -> float:
        """Calculate ATR for a symbol."""
        try:
            data = self.mt5_connector.get_historical_data(symbol, 'M15', 20)
            if data is None or len(data) < 14:
                return 0.001  # Default ATR
            
            # Simple ATR calculation
            high_low = data['high'] - data['low']
            high_close = abs(data['high'] - data['close'].shift(1))
            low_close = abs(data['low'] - data['close'].shift(1))
            
            true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
            atr = true_range.rolling(14).mean().iloc[-1]
            
            return atr if not pd.isna(atr) else 0.001
            
        except Exception as e:
            self.logger.error(f"❌ Error calculating ATR for {symbol}: {str(e)}")
            return 0.001
    
    async def _update_dashboard(self):
        """Update trading dashboard with current metrics."""
        try:
            # Get account info
            account_info = self.mt5_connector.get_account_info()
            positions = self.mt5_connector.get_positions()
            
            if account_info:
                # Calculate metrics
                total_pnl = sum(pos.get('profit', 0) for pos in positions)
                
                metrics = {
                    'portfolio_value': account_info['equity'],
                    'account_balance': account_info['balance'],
                    'daily_pnl': total_pnl,
                    'active_positions': len(positions),
                    'auto_trading_active': self.running,
                    'last_update': datetime.now()
                }
                
                self.dashboard.update_trading_metrics(metrics)
                
        except Exception as e:
            self.logger.error(f"❌ Error updating dashboard: {str(e)}")
    
    def _log_trade_to_dashboard(self, trade: Dict, signal: Dict, result: Dict):
        """Log trade execution to dashboard."""
        # Implementation for logging trades
        pass
    
    def stop_auto_trading(self):
        """Stop the automatic trading engine."""
        self.logger.info("🛑 Stopping automatic trading...")
        self.running = False
        
        # Disconnect from MT5
        if self.mt5_connector.connected:
            self.mt5_connector.disconnect()
        
        self.logger.info("✅ Auto-trading stopped")


async def main():
    """Main function for testing auto-trader."""
    # Test configuration with high risk settings
    config = {
        'trading': {
            'enabled': True,
            'symbols': ['EURUSD'],
            'signal_interval': 60,  # 1 minute for testing
            'min_signal_strength': 0.6
        },
        'risk_management': {
            'position_sizing': {
                'risk_per_trade': 0.30  # 30% risk as requested
            }
        }
    }
    
    auto_trader = AutoTrader(config)
    
    try:
        await auto_trader.start_auto_trading()
    except KeyboardInterrupt:
        print("\n🛑 Auto-trading interrupted by user")
        auto_trader.stop_auto_trading()


if __name__ == "__main__":
    asyncio.run(main())
