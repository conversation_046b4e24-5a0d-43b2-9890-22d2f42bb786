# Data Configuration for AI Trading Bot

# Data Sources
data_sources:
  local_files:
    enabled: true
    directory: "data/raw"
    file_patterns:
      - "*.csv"
    
  mt5_historical:
    enabled: false
    symbols:
      - "EURUSD"
      - "GBPUSD"
      - "USDJPY"
    timeframes:
      - "M15"
      - "H1"
      - "D1"
    start_date: "2020-01-01"
    
  external_apis:
    enabled: false
    providers:
      - "alpha_vantage"
      - "yahoo_finance"
      - "quandl"

# Data Processing
preprocessing:
  cleaning:
    remove_duplicates: true
    handle_missing_values: "forward_fill"  # "drop", "forward_fill", "interpolate"
    outlier_detection:
      method: "z_score"  # "z_score", "iqr", "isolation_forest"
      threshold: 3.0
      
  normalization:
    method: "min_max"  # "min_max", "standard", "robust"
    feature_range: [0, 1]
    
  resampling:
    enabled: false
    target_frequency: "15T"  # 15 minutes
    method: "ohlc"  # "ohlc", "mean", "last"

# Data Validation
validation:
  checks:
    - "no_missing_values"
    - "no_duplicates"
    - "price_consistency"  # high >= low, etc.
    - "chronological_order"
    
  quality_metrics:
    min_data_points: 1000
    max_missing_ratio: 0.05
    
# Feature Engineering Pipeline
feature_engineering:
  technical_indicators:
    price_based:
      - "sma"
      - "ema"
      - "bollinger_bands"
      - "price_channels"
      
    momentum:
      - "rsi"
      - "stochastic"
      - "williams_r"
      - "roc"
      
    trend:
      - "macd"
      - "adx"
      - "aroon"
      - "parabolic_sar"
      
    volatility:
      - "atr"
      - "bollinger_width"
      - "keltner_channels"
      
    volume:
      - "volume_sma"
      - "volume_ratio"
      - "obv"
      - "vwap"
      
  statistical_features:
    - "returns"
    - "log_returns"
    - "volatility"
    - "skewness"
    - "kurtosis"
    
  time_features:
    - "hour_of_day"
    - "day_of_week"
    - "month"
    - "quarter"
    
  lagged_features:
    enabled: true
    max_lags: 10
    
# Data Storage
storage:
  processed_data:
    format: "parquet"  # "csv", "parquet", "hdf5"
    compression: "snappy"
    
  feature_store:
    enabled: false
    backend: "local"  # "local", "s3", "gcs"
    
# Data Splitting
splitting:
  method: "time_based"  # "time_based", "random"
  train_ratio: 0.7
  validation_ratio: 0.15
  test_ratio: 0.15
  
  walk_forward:
    enabled: true
    window_size: 252  # Trading days
    step_size: 21     # Monthly steps
    
# Data Monitoring
monitoring:
  data_drift:
    enabled: true
    methods:
      - "kolmogorov_smirnov"
      - "population_stability_index"
    threshold: 0.1
    
  feature_importance:
    track_changes: true
    alert_threshold: 0.2
