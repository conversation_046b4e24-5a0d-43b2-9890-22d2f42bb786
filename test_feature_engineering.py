#!/usr/bin/env python3
"""
Test script for feature engineering functionality.
"""

import pandas as pd
import numpy as np
from pathlib import Path
import sys

# Add src to path
sys.path.append(str(Path(__file__).parent))

# Simple logger replacement
class SimpleLogger:
    def info(self, msg): print(f"INFO: {msg}")
    def warning(self, msg): print(f"WARNING: {msg}")
    def error(self, msg): print(f"ERROR: {msg}")

def test_feature_engineering():
    """Test feature engineering functionality."""
    print("Testing feature engineering functionality...")
    
    try:
        # Load sample data
        sample_file = Path("data/processed/sample_standardized.csv")
        if not sample_file.exists():
            print("Sample data not found. Please run simple_data_test.py first.")
            return
        
        df = pd.read_csv(sample_file)
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        
        print(f"Loaded sample data: {df.shape}")
        print(f"Columns: {list(df.columns)}")
        print(f"Date range: {df['timestamp'].min()} to {df['timestamp'].max()}")
        
        # Import feature engineering modules
        from src.features.technical_indicators import TechnicalIndicators
        from src.features.statistical_features import StatisticalFeatures
        from src.features.feature_engineer import FeatureEngineer
        
        print("\n" + "="*50)
        print("Testing Technical Indicators")
        print("="*50)
        
        # Test individual technical indicators
        print("Testing SMA...")
        sma_20 = TechnicalIndicators.sma(df['close'], 20)
        print(f"SMA 20: {sma_20.iloc[-5:].values}")
        
        print("Testing EMA...")
        ema_12 = TechnicalIndicators.ema(df['close'], 12)
        print(f"EMA 12: {ema_12.iloc[-5:].values}")
        
        print("Testing RSI...")
        rsi = TechnicalIndicators.rsi(df['close'])
        print(f"RSI: {rsi.iloc[-5:].values}")
        
        print("Testing MACD...")
        macd_data = TechnicalIndicators.macd(df['close'])
        print(f"MACD: {macd_data['macd'].iloc[-5:].values}")
        
        print("Testing Bollinger Bands...")
        bb_data = TechnicalIndicators.bollinger_bands(df['close'])
        print(f"BB Upper: {bb_data['upper'].iloc[-5:].values}")
        print(f"BB Lower: {bb_data['lower'].iloc[-5:].values}")
        
        print("Testing ATR...")
        atr = TechnicalIndicators.atr(df['high'], df['low'], df['close'])
        print(f"ATR: {atr.iloc[-5:].values}")
        
        print("\n" + "="*50)
        print("Testing Statistical Features")
        print("="*50)
        
        print("Testing Returns...")
        returns = StatisticalFeatures.returns(df['close'])
        print(f"Returns: {returns.iloc[-5:].values}")
        
        print("Testing Volatility...")
        volatility = StatisticalFeatures.volatility(df['close'])
        print(f"Volatility: {volatility.iloc[-5:].values}")
        
        print("Testing Z-Score...")
        z_score = StatisticalFeatures.z_score(df['close'], 20)
        print(f"Z-Score: {z_score.iloc[-5:].values}")
        
        print("Testing Momentum...")
        momentum = StatisticalFeatures.momentum(df['close'], 10)
        print(f"Momentum: {momentum.iloc[-5:].values}")
        
        print("\n" + "="*50)
        print("Testing Feature Engineer")
        print("="*50)
        
        # Test complete feature engineering
        feature_engineer = FeatureEngineer()
        
        print("Creating all features...")
        df_features = feature_engineer.create_all_features(df)
        
        print(f"Original shape: {df.shape}")
        print(f"Features shape: {df_features.shape}")
        print(f"Added {df_features.shape[1] - df.shape[1]} features")
        
        # Show feature names
        feature_names = feature_engineer.get_feature_names()
        print(f"\nCreated {len(feature_names)} features:")
        
        # Group features by type
        tech_features = [f for f in feature_names if any(indicator in f for indicator in ['sma', 'ema', 'rsi', 'macd', 'bb', 'atr', 'stoch', 'williams', 'roc', 'adx', 'obv', 'vwap'])]
        stat_features = [f for f in feature_names if any(stat in f for stat in ['returns', 'volatility', 'rolling', 'z_score', 'momentum', 'percentile'])]
        lag_features = [f for f in feature_names if 'lag' in f]
        time_features = [f for f in feature_names if any(time_word in f for time_word in ['hour', 'day', 'month', 'quarter', 'sin', 'cos'])]
        
        print(f"  Technical indicators: {len(tech_features)}")
        print(f"  Statistical features: {len(stat_features)}")
        print(f"  Lagged features: {len(lag_features)}")
        print(f"  Time features: {len(time_features)}")
        
        # Show sample of each type
        if tech_features:
            print(f"\n  Sample technical indicators: {tech_features[:5]}")
        if stat_features:
            print(f"  Sample statistical features: {stat_features[:5]}")
        if lag_features:
            print(f"  Sample lagged features: {lag_features[:5]}")
        if time_features:
            print(f"  Sample time features: {time_features[:5]}")
        
        # Check for missing values
        missing_counts = df_features.isnull().sum()
        features_with_missing = missing_counts[missing_counts > 0]
        
        print(f"\nFeatures with missing values: {len(features_with_missing)}")
        if len(features_with_missing) > 0:
            print("Top 10 features with most missing values:")
            print(features_with_missing.sort_values(ascending=False).head(10))
        
        # Basic statistics for some key features
        print("\nBasic statistics for key features:")
        key_features = ['close', 'sma_20', 'ema_12', 'rsi', 'macd', 'bb_percent_b', 'atr', 'volatility']
        available_key_features = [f for f in key_features if f in df_features.columns]
        
        if available_key_features:
            stats_df = df_features[available_key_features].describe()
            print(stats_df)
        
        # Save features sample
        output_dir = Path("data/processed")
        output_dir.mkdir(exist_ok=True)
        
        # Save first 100 rows with features
        sample_features = df_features.head(100)
        sample_features.to_csv(output_dir / "sample_with_features.csv", index=False)
        print(f"\nSaved sample with features to: {output_dir / 'sample_with_features.csv'}")
        
        # Feature importance data
        feature_stats = feature_engineer.get_feature_importance_data(df_features)
        print(f"\nGenerated feature statistics for {len(feature_stats)} features")
        
        print("\n" + "="*50)
        print("Feature Engineering Test Completed Successfully!")
        print("="*50)
        
    except Exception as e:
        print(f"Error during feature engineering test: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_feature_engineering()
