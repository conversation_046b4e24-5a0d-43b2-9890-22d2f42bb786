#!/usr/bin/env python3
"""
Test script for model training functionality.
"""

import pandas as pd
import numpy as np
from pathlib import Path
import sys

# Add src to path
sys.path.append(str(Path(__file__).parent / "src" / "models"))

# Import modules directly
import supervised_models
import deep_learning_models
import model_manager


def create_sample_data_with_features():
    """Create sample data with features for testing."""
    np.random.seed(42)
    
    # Create base price data
    n_samples = 500
    dates = pd.date_range('2020-01-01', periods=n_samples, freq='H')
    
    price = 100
    prices = [price]
    
    for _ in range(n_samples - 1):
        change = np.random.normal(0, 0.02)
        price = price * (1 + change)
        prices.append(price)
    
    # Create OHLCV data
    df = pd.DataFrame({
        'timestamp': dates,
        'open': prices,
        'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
        'close': prices,
        'volume': np.random.randint(1000, 10000, n_samples)
    })
    
    # Add some basic features
    # Moving averages
    df['sma_10'] = df['close'].rolling(10).mean()
    df['sma_20'] = df['close'].rolling(20).mean()
    df['ema_12'] = df['close'].ewm(span=12).mean()
    
    # Returns
    df['returns'] = df['close'].pct_change()
    df['log_returns'] = np.log(df['close'] / df['close'].shift(1))
    
    # Volatility
    df['volatility'] = df['returns'].rolling(20).std()
    
    # Lagged features
    for lag in [1, 2, 3]:
        df['close_lag_' + str(lag)] = df['close'].shift(lag)
        df['volume_lag_' + str(lag)] = df['volume'].shift(lag)
    
    # Time features
    df['hour'] = df['timestamp'].dt.hour
    df['day_of_week'] = df['timestamp'].dt.dayofweek
    
    # Remove rows with NaN values
    df = df.dropna().reset_index(drop=True)
    
    print(f"Created sample data with {df.shape[0]} rows and {df.shape[1]} columns")
    print(f"Features: {list(df.columns)}")
    
    return df


def test_supervised_models():
    """Test supervised learning models."""
    print("\n" + "="*50)
    print("Testing Supervised Learning Models")
    print("="*50)
    
    # Create sample data
    df = create_sample_data_with_features()
    
    # Initialize supervised models
    sup_models = supervised_models.SupervisedModels()
    
    # Prepare data
    print("\nPreparing data for supervised learning...")
    X, y = sup_models.prepare_data(df, target_column='close', prediction_horizon=1, task_type='regression')
    print(f"Data shape: X={X.shape}, y={y.shape}")
    
    # Split data
    X_train, X_val, X_test, y_train, y_val, y_test = sup_models.split_data(X, y)
    print(f"Train: {X_train.shape}, Val: {X_val.shape}, Test: {X_test.shape}")
    
    # Test Linear Regression
    print("\nTesting Linear Regression...")
    lr_result = sup_models.train_linear_regression(X_train, y_train)
    print(f"Linear Regression Result: {lr_result}")
    
    # Test predictions
    lr_pred = sup_models.predict('linear_regression', X_test)
    print(f"Linear Regression Predictions shape: {lr_pred.shape}")
    
    # Evaluate
    lr_eval = sup_models.evaluate_model('linear_regression', X_test, y_test)
    print(f"Linear Regression Evaluation: {lr_eval}")
    
    # Test Random Forest (if sklearn available)
    print("\nTesting Random Forest...")
    rf_result = sup_models.train_random_forest(X_train, y_train, n_estimators=50, max_depth=5)
    print(f"Random Forest Result: {rf_result}")
    
    if 'random_forest' in sup_models.models:
        rf_pred = sup_models.predict('random_forest', X_test)
        rf_eval = sup_models.evaluate_model('random_forest', X_test, y_test)
        print(f"Random Forest Evaluation: {rf_eval}")
    
    # Test XGBoost
    print("\nTesting XGBoost...")
    xgb_result = sup_models.train_gradient_boosting(X_train, y_train, n_estimators=50)
    print(f"XGBoost Result: {xgb_result}")
    
    if 'xgboost' in sup_models.models or 'gradient_boosting' in sup_models.models:
        model_name = 'xgboost' if 'xgboost' in sup_models.models else 'gradient_boosting'
        xgb_pred = sup_models.predict(model_name, X_test)
        xgb_eval = sup_models.evaluate_model(model_name, X_test, y_test)
        print(f"Gradient Boosting Evaluation: {xgb_eval}")
    
    return sup_models


def test_deep_learning_models():
    """Test deep learning models."""
    print("\n" + "="*50)
    print("Testing Deep Learning Models")
    print("="*50)
    
    # Create sample data
    df = create_sample_data_with_features()
    
    # Initialize deep learning models
    dl_models = deep_learning_models.DeepLearningModels()
    
    # Prepare sequence data
    print("\nPreparing sequence data...")
    numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
    if 'timestamp' in numeric_cols:
        numeric_cols.remove('timestamp')
    
    data_array = df[numeric_cols].values
    target_idx = numeric_cols.index('close')
    
    sequence_length = 30
    X_seq, y_seq = dl_models.create_sequences(data_array, sequence_length, target_idx)
    print(f"Sequence data shape: X={X_seq.shape}, y={y_seq.shape}")
    
    # Split sequence data
    n = len(X_seq)
    train_end = int(n * 0.7)
    val_end = int(n * 0.85)
    
    X_train_seq = X_seq[:train_end]
    X_val_seq = X_seq[train_end:val_end]
    X_test_seq = X_seq[val_end:]
    
    y_train_seq = y_seq[:train_end]
    y_val_seq = y_seq[train_end:val_end]
    y_test_seq = y_seq[val_end:]
    
    print(f"Sequence splits - Train: {X_train_seq.shape}, Val: {X_val_seq.shape}, Test: {X_test_seq.shape}")
    
    # Test LSTM
    print("\nTesting LSTM...")
    lstm_result = dl_models.train_lstm(
        X_train_seq, y_train_seq, X_val_seq, y_val_seq,
        units=[32, 32], epochs=5, batch_size=16  # Small values for testing
    )
    print(f"LSTM Result: {lstm_result}")
    
    if 'lstm' in dl_models.models:
        lstm_pred = dl_models.predict('lstm', X_test_seq)
        lstm_eval = dl_models.evaluate_model('lstm', X_test_seq, y_test_seq)
        print(f"LSTM Evaluation: {lstm_eval}")
    
    # Test Transformer (if TensorFlow available)
    print("\nTesting Transformer...")
    transformer_result = dl_models.train_transformer(
        X_train_seq, y_train_seq, X_val_seq, y_val_seq,
        d_model=32, nhead=4, num_layers=2, epochs=5, batch_size=16
    )
    print(f"Transformer Result: {transformer_result}")
    
    if 'transformer' in dl_models.models:
        transformer_pred = dl_models.predict('transformer', X_test_seq)
        transformer_eval = dl_models.evaluate_model('transformer', X_test_seq, y_test_seq)
        print(f"Transformer Evaluation: {transformer_eval}")
    
    return dl_models


def test_model_manager():
    """Test model manager."""
    print("\n" + "="*50)
    print("Testing Model Manager")
    print("="*50)
    
    # Create sample data
    df = create_sample_data_with_features()
    
    # Create model manager with custom config
    config = {
        'models': {
            'linear_regression': {'enabled': True, 'regularization': 'ridge', 'alpha': 1.0},
            'random_forest': {'enabled': True, 'n_estimators': 50, 'max_depth': 5, 'min_samples_split': 5},
            'xgboost': {'enabled': True, 'n_estimators': 50, 'max_depth': 4, 'learning_rate': 0.1},
            'lstm': {'enabled': True, 'units': [32, 32], 'dropout': 0.2, 'epochs': 5, 'batch_size': 16, 'sequence_length': 30},
            'transformer': {'enabled': False}  # Disable for testing
        },
        'training': {
            'train_ratio': 0.7,
            'val_ratio': 0.15,
            'test_ratio': 0.15,
            'target_column': 'close',
            'prediction_horizon': 1,
            'task_type': 'regression'
        }
    }
    
    manager = model_manager.ModelManager(config)
    
    # Train all models
    print("\nTraining all models...")
    training_results = manager.train_all_models(df)
    print(f"Training completed. Results: {list(training_results.keys())}")
    
    # Evaluate all models
    print("\nEvaluating all models...")
    evaluation_results = manager.evaluate_all_models(df)
    print(f"Evaluation completed. Results: {list(evaluation_results.keys())}")
    
    # Print evaluation results
    for model_name, results in evaluation_results.items():
        print(f"{model_name}: {results}")
    
    # Get best model
    best_model, best_score = manager.get_best_model(evaluation_results, 'r2')
    print(f"\nBest model: {best_model} with R² = {best_score:.4f}")
    
    # Test ensemble predictions
    print("\nTesting ensemble predictions...")
    try:
        ensemble_pred = manager.create_ensemble_predictions(df)
        print(f"Ensemble predictions shape: {ensemble_pred.shape}")
    except Exception as e:
        print(f"Ensemble prediction failed: {e}")
    
    # Get model summary
    summary = manager.get_model_summary()
    print(f"\nModel Summary:")
    print(f"Total models: {summary['total_models']}")
    print(f"Supervised models: {summary['supervised_models']['total_models']}")
    print(f"Deep learning models: {summary['deep_learning_models']['total_models']}")
    
    return manager


def main():
    """Main test function."""
    print("Model Training Test")
    print("="*50)
    
    try:
        # Test supervised models
        sup_models = test_supervised_models()
        
        # Test deep learning models
        dl_models = test_deep_learning_models()
        
        # Test model manager
        manager = test_model_manager()
        
        print("\n" + "="*50)
        print("Model Training Test Completed Successfully!")
        print("="*50)
        
        # Save test results
        output_dir = Path("data/processed")
        output_dir.mkdir(exist_ok=True)
        
        # Save a simple summary
        test_summary = {
            'supervised_models_trained': len(sup_models.models),
            'deep_learning_models_trained': len(dl_models.models),
            'total_models': len(sup_models.models) + len(dl_models.models)
        }
        
        import json
        with open(output_dir / "model_training_test_summary.json", 'w') as f:
            json.dump(test_summary, f, indent=2)
        
        print(f"Test summary saved to: {output_dir / 'model_training_test_summary.json'}")
        
    except Exception as e:
        print(f"Error during model training test: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
