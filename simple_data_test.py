#!/usr/bin/env python3
"""
Simple test script to verify data loading without external dependencies.
"""

import pandas as pd
import numpy as np
from pathlib import Path
import glob


def load_and_analyze_data():
    """Load and analyze the CSV data files."""
    print("Analyzing available data files...")
    
    # Check for CSV files in data/raw
    data_dir = Path("data/raw")
    if not data_dir.exists():
        print(f"Data directory {data_dir} does not exist")
        return
    
    csv_files = list(data_dir.glob("*.csv"))
    print(f"Found {len(csv_files)} CSV files:")
    
    for file_path in csv_files:
        print(f"  - {file_path.name}")
    
    if not csv_files:
        print("No CSV files found")
        return
    
    # Analyze each file
    for file_path in csv_files[:3]:  # Analyze first 3 files
        print(f"\n{'='*50}")
        print(f"Analyzing: {file_path.name}")
        print(f"{'='*50}")
        
        try:
            # Load the file
            df = pd.read_csv(file_path)
            print(f"Shape: {df.shape}")
            print(f"Columns: {list(df.columns)}")
            
            # Show first few rows
            print("\nFirst 5 rows:")
            print(df.head())
            
            # Basic statistics
            print("\nBasic info:")
            print(f"  - Total rows: {len(df):,}")
            print(f"  - Missing values: {df.isnull().sum().sum()}")
            print(f"  - Duplicate rows: {df.duplicated().sum()}")
            
            # Identify price columns
            price_cols = []
            for col in df.columns:
                col_lower = col.lower()
                if any(price_word in col_lower for price_word in ['open', 'high', 'low', 'close', 'price']):
                    price_cols.append(col)
            
            if price_cols:
                print(f"  - Price columns: {price_cols}")
                for col in price_cols:
                    if df[col].dtype in ['float64', 'int64']:
                        print(f"    {col}: min={df[col].min():.4f}, max={df[col].max():.4f}, mean={df[col].mean():.4f}")
            
            # Identify timestamp columns
            timestamp_cols = []
            for col in df.columns:
                col_lower = col.lower()
                if any(time_word in col_lower for time_word in ['date', 'time', 'timestamp']):
                    timestamp_cols.append(col)
            
            if timestamp_cols:
                print(f"  - Timestamp columns: {timestamp_cols}")
                for col in timestamp_cols:
                    try:
                        # Try to parse as datetime
                        if col.lower() == 'date' and 'clock' in [c.lower() for c in df.columns]:
                            # Handle date + clock format
                            combined = pd.to_datetime(df[col].astype(str) + ' ' + df['clock'].astype(str))
                            print(f"    Combined datetime range: {combined.min()} to {combined.max()}")
                        else:
                            dt_series = pd.to_datetime(df[col])
                            print(f"    {col} range: {dt_series.min()} to {dt_series.max()}")
                    except:
                        print(f"    {col}: Could not parse as datetime")
            
            # Check for volume data
            volume_cols = [col for col in df.columns if 'vol' in col.lower()]
            if volume_cols:
                print(f"  - Volume columns: {volume_cols}")
            
        except Exception as e:
            print(f"Error analyzing {file_path.name}: {str(e)}")
    
    print(f"\n{'='*50}")
    print("Analysis complete!")


def standardize_sample_data():
    """Create a standardized sample from one of the files."""
    print("\nCreating standardized sample data...")

    data_dir = Path("data/raw")
    csv_files = list(data_dir.glob("*.csv"))

    if not csv_files:
        print("No CSV files found")
        return

    # Prefer EURUSD file for forex data
    preferred_files = ['EURUSD-2000-2020-15m.csv', 'forex-1year.csv', 'USDJPY-2000-2020-15m.csv']
    file_path = None

    for preferred in preferred_files:
        if (data_dir / preferred).exists():
            file_path = data_dir / preferred
            break

    if file_path is None:
        file_path = csv_files[0]

    print(f"Using file: {file_path.name}")

    try:
        df = pd.read_csv(file_path)

        # Create standardized format
        df_std = pd.DataFrame()

        # Handle timestamp
        if 'DATE_TIME' in df.columns:
            df_std['timestamp'] = pd.to_datetime(df['DATE_TIME'])
        elif 'date' in df.columns and 'clock' in df.columns:
            df_std['timestamp'] = pd.to_datetime(df['date'].astype(str) + ' ' + df['clock'].astype(str))
        elif 'date' in df.columns:
            df_std['timestamp'] = pd.to_datetime(df['date'])
        else:
            print("Could not identify timestamp columns")
            print(f"Available columns: {list(df.columns)}")
            return
        
        # Handle OHLC data
        price_mapping = {
            'OPEN': 'open', 'open_eurusd': 'open', 'Open': 'open',
            'HIGH': 'high', 'high_eurusd': 'high', 'High': 'high', 
            'LOW': 'low', 'low_eurusd': 'low', 'Low': 'low',
            'CLOSE': 'close', 'close_eurusd': 'close', 'Close': 'close'
        }
        
        for old_col, new_col in price_mapping.items():
            if old_col in df.columns:
                df_std[new_col] = df[old_col]
        
        # Handle volume
        volume_cols = ['volume', 'tikvol_eurusd', 'Volume']
        for vol_col in volume_cols:
            if vol_col in df.columns:
                df_std['volume'] = df[vol_col]
                break
        else:
            # Create synthetic volume
            df_std['volume'] = np.random.randint(100, 1000, len(df_std))
            print("Created synthetic volume data")
        
        # Sort by timestamp
        df_std = df_std.sort_values('timestamp').reset_index(drop=True)
        
        print(f"Standardized data shape: {df_std.shape}")
        print(f"Columns: {list(df_std.columns)}")
        print(f"Date range: {df_std['timestamp'].min()} to {df_std['timestamp'].max()}")
        
        # Save sample
        sample_dir = Path("data/processed")
        sample_dir.mkdir(parents=True, exist_ok=True)
        
        # Save first 1000 rows as sample
        sample_df = df_std.head(1000)
        sample_df.to_csv(sample_dir / "sample_standardized.csv", index=False)
        print(f"Saved sample to: {sample_dir / 'sample_standardized.csv'}")
        
        # Basic analysis of standardized data
        print("\nStandardized data analysis:")
        print(f"  - Price range: {sample_df['close'].min():.4f} to {sample_df['close'].max():.4f}")
        print(f"  - Volume range: {sample_df['volume'].min()} to {sample_df['volume'].max()}")
        print(f"  - Missing values: {sample_df.isnull().sum().sum()}")
        
        return sample_df
        
    except Exception as e:
        print(f"Error creating standardized data: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    load_and_analyze_data()
    standardize_sample_data()
