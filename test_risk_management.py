#!/usr/bin/env python3
"""
Test script for risk management functionality.
"""

import pandas as pd
import numpy as np
from pathlib import Path
import sys
import json
from datetime import datetime

# Add src to path
sys.path.append(str(Path(__file__).parent / "src" / "risk_management"))

# Import risk management modules directly
from risk_manager import RiskManager, PositionSizer, StopLossCalculator, PortfolioRiskAnalyzer


def test_risk_manager():
    """Test the main risk manager functionality."""
    print("\n" + "="*50)
    print("Testing Risk Manager")
    print("="*50)
    
    # Initialize risk manager with custom config
    config = {
        'position_sizing': {
            'method': 'fixed_percentage',
            'risk_per_trade': 0.02,  # 2% risk per trade
            'max_position_size': 0.1,
            'min_position_size': 0.001
        },
        'stop_loss': {
            'method': 'atr_based',
            'atr_multiplier': 2.0,
            'fixed_percentage': 0.02,
            'max_stop_loss': 0.05
        },
        'take_profit': {
            'method': 'risk_reward_ratio',
            'risk_reward_ratio': 2.0
        },
        'portfolio_limits': {
            'max_daily_loss': 0.05,
            'max_drawdown': 0.15,
            'max_positions': 10,
            'max_exposure_per_symbol': 0.2
        }
    }
    
    risk_manager = RiskManager(config)
    
    print(f"Risk manager initialized with config")
    print(f"Position sizing method: {config['position_sizing']['method']}")
    print(f"Stop loss method: {config['stop_loss']['method']}")
    print(f"Take profit method: {config['take_profit']['method']}")
    
    # Test position sizing
    print(f"\nTesting position sizing...")
    
    account_balance = 10000.0
    entry_price = 1.1000
    stop_loss = 1.0950
    symbol = 'EURUSD'
    
    position_size = risk_manager.calculate_position_size(
        symbol, entry_price, stop_loss, account_balance
    )
    
    print(f"Account balance: ${account_balance:,.2f}")
    print(f"Entry price: {entry_price:.4f}")
    print(f"Stop loss: {stop_loss:.4f}")
    print(f"Calculated position size: {position_size:.2f}")
    print(f"Position value: ${position_size * entry_price:,.2f}")
    print(f"Risk amount: ${abs(entry_price - stop_loss) * position_size:,.2f}")
    print(f"Risk percentage: {(abs(entry_price - stop_loss) * position_size) / account_balance:.2%}")
    
    # Test stop loss calculation
    print(f"\nTesting stop loss calculation...")
    
    atr = 0.0050  # 50 pips ATR
    calculated_stop = risk_manager.calculate_stop_loss(
        symbol, entry_price, 'buy', atr
    )
    
    print(f"Entry price: {entry_price:.4f}")
    print(f"ATR: {atr:.4f}")
    print(f"Calculated stop loss: {calculated_stop:.4f}")
    print(f"Stop distance: {abs(entry_price - calculated_stop):.4f}")
    
    # Test take profit calculation
    print(f"\nTesting take profit calculation...")
    
    take_profit = risk_manager.calculate_take_profit(
        symbol, entry_price, calculated_stop, 'buy'
    )
    
    print(f"Entry price: {entry_price:.4f}")
    print(f"Stop loss: {calculated_stop:.4f}")
    print(f"Take profit: {take_profit:.4f}")
    print(f"Risk distance: {abs(entry_price - calculated_stop):.4f}")
    print(f"Reward distance: {abs(take_profit - entry_price):.4f}")
    print(f"Risk/Reward ratio: {abs(take_profit - entry_price) / abs(entry_price - calculated_stop):.2f}")
    
    return risk_manager


def test_position_sizer():
    """Test position sizing algorithms."""
    print("\n" + "="*50)
    print("Testing Position Sizer")
    print("="*50)
    
    account_balance = 10000.0
    entry_price = 1.1000
    stop_loss = 1.0950
    risk_percentage = 0.02
    
    print(f"Test parameters:")
    print(f"  Account balance: ${account_balance:,.2f}")
    print(f"  Entry price: {entry_price:.4f}")
    print(f"  Stop loss: {stop_loss:.4f}")
    print(f"  Risk percentage: {risk_percentage:.1%}")
    
    # Test fixed percentage sizing
    print(f"\nTesting fixed percentage sizing...")
    fixed_size = PositionSizer.fixed_percentage(
        account_balance, risk_percentage, entry_price, stop_loss
    )
    print(f"Fixed percentage position size: {fixed_size:.2f}")
    print(f"Position value: ${fixed_size * entry_price:,.2f}")
    
    # Test Kelly criterion sizing
    print(f"\nTesting Kelly criterion sizing...")
    win_rate = 0.55
    avg_win = 100.0
    avg_loss = -80.0
    
    kelly_size = PositionSizer.kelly_criterion(
        win_rate, avg_win, avg_loss, account_balance, entry_price, stop_loss
    )
    print(f"Kelly criterion position size: {kelly_size:.2f}")
    print(f"Win rate: {win_rate:.1%}")
    print(f"Average win: ${avg_win:.2f}")
    print(f"Average loss: ${avg_loss:.2f}")
    
    # Test volatility adjusted sizing
    print(f"\nTesting volatility adjusted sizing...")
    current_volatility = 0.03  # 3% volatility
    target_volatility = 0.02   # 2% target
    
    vol_adjusted_size = PositionSizer.volatility_adjusted(
        fixed_size, current_volatility, target_volatility
    )
    print(f"Base position size: {fixed_size:.2f}")
    print(f"Current volatility: {current_volatility:.1%}")
    print(f"Target volatility: {target_volatility:.1%}")
    print(f"Volatility adjusted size: {vol_adjusted_size:.2f}")
    
    # Test ATR-based sizing
    print(f"\nTesting ATR-based sizing...")
    atr = 0.0050
    atr_multiplier = 2.0
    
    atr_size = PositionSizer.atr_based(
        account_balance, risk_percentage, atr, atr_multiplier
    )
    print(f"ATR: {atr:.4f}")
    print(f"ATR multiplier: {atr_multiplier}")
    print(f"ATR-based position size: {atr_size:.2f}")


def test_stop_loss_calculator():
    """Test stop loss calculation methods."""
    print("\n" + "="*50)
    print("Testing Stop Loss Calculator")
    print("="*50)
    
    entry_price = 1.1000
    direction = 'buy'
    
    print(f"Test parameters:")
    print(f"  Entry price: {entry_price:.4f}")
    print(f"  Direction: {direction}")
    
    # Test ATR stop
    print(f"\nTesting ATR stop...")
    atr = 0.0050
    atr_multiplier = 2.0
    
    atr_stop = StopLossCalculator.atr_stop(entry_price, atr, direction, atr_multiplier)
    print(f"ATR: {atr:.4f}")
    print(f"ATR multiplier: {atr_multiplier}")
    print(f"ATR stop loss: {atr_stop:.4f}")
    print(f"Stop distance: {abs(entry_price - atr_stop):.4f}")
    
    # Test percentage stop
    print(f"\nTesting percentage stop...")
    percentage = 0.02
    
    pct_stop = StopLossCalculator.percentage_stop(entry_price, direction, percentage)
    print(f"Percentage: {percentage:.1%}")
    print(f"Percentage stop loss: {pct_stop:.4f}")
    print(f"Stop distance: {abs(entry_price - pct_stop):.4f}")
    
    # Test volatility stop
    print(f"\nTesting volatility stop...")
    volatility = 0.025
    vol_multiplier = 2.0
    
    vol_stop = StopLossCalculator.volatility_stop(
        entry_price, volatility, direction, vol_multiplier
    )
    print(f"Volatility: {volatility:.1%}")
    print(f"Volatility multiplier: {vol_multiplier}")
    print(f"Volatility stop loss: {vol_stop:.4f}")
    print(f"Stop distance: {abs(entry_price - vol_stop):.4f}")
    
    # Test support/resistance stop
    print(f"\nTesting support/resistance stop...")
    support_level = 1.0980
    buffer = 0.001
    
    sr_stop = StopLossCalculator.support_resistance_stop(
        entry_price, support_level, direction, buffer
    )
    print(f"Support level: {support_level:.4f}")
    print(f"Buffer: {buffer:.3f}")
    print(f"S/R stop loss: {sr_stop:.4f}")
    print(f"Stop distance: {abs(entry_price - sr_stop):.4f}")


def test_portfolio_risk_analyzer():
    """Test portfolio risk analysis."""
    print("\n" + "="*50)
    print("Testing Portfolio Risk Analyzer")
    print("="*50)
    
    analyzer = PortfolioRiskAnalyzer()
    
    # Create sample portfolio positions
    positions = [
        {
            'symbol': 'EURUSD',
            'value': 2000.0,
            'volatility': 0.02,
            'direction': 'buy'
        },
        {
            'symbol': 'GBPUSD',
            'value': 1500.0,
            'volatility': 0.025,
            'direction': 'buy'
        },
        {
            'symbol': 'USDJPY',
            'value': 1000.0,
            'volatility': 0.018,
            'direction': 'sell'
        },
        {
            'symbol': 'EURJPY',
            'value': 800.0,
            'volatility': 0.022,
            'direction': 'buy'
        }
    ]
    
    print(f"Sample portfolio with {len(positions)} positions:")
    for pos in positions:
        print(f"  {pos['symbol']}: ${pos['value']:,.0f} ({pos['direction']})")
    
    total_value = sum(pos['value'] for pos in positions)
    print(f"Total portfolio value: ${total_value:,.2f}")
    
    # Test VaR calculation
    print(f"\nTesting VaR calculation...")
    var_95 = analyzer.calculate_portfolio_var(positions, 0.05, 1)
    var_99 = analyzer.calculate_portfolio_var(positions, 0.01, 1)
    
    print(f"1-day VaR (95%): ${var_95:,.2f} ({var_95/total_value:.1%})")
    print(f"1-day VaR (99%): ${var_99:,.2f} ({var_99/total_value:.1%})")
    
    # Test correlation risk
    print(f"\nTesting correlation risk...")
    correlation_risk = analyzer.calculate_correlation_risk(positions)
    
    print(f"Average correlation: {correlation_risk['avg_correlation']:.2f}")
    print(f"Maximum correlation: {correlation_risk['max_correlation']:.2f}")
    print(f"Correlation risk level: {correlation_risk['correlation_risk']}")
    print(f"Correlation pairs analyzed: {correlation_risk['correlation_pairs']}")
    
    # Test concentration risk
    print(f"\nTesting concentration risk...")
    concentration_risk = analyzer.calculate_concentration_risk(positions)
    
    print(f"Concentration risk level: {concentration_risk['concentration_risk']}")
    print(f"Herfindahl-Hirschman Index: {concentration_risk['herfindahl_index']:.3f}")
    print(f"Maximum symbol weight: {concentration_risk['max_symbol_weight']:.1%}")
    print(f"Effective number of positions: {concentration_risk['effective_positions']:.1f}")
    
    print(f"\nSymbol weights:")
    for symbol, weight in concentration_risk['symbol_weights'].items():
        print(f"  {symbol}: {weight:.1%}")


def test_trade_validation():
    """Test comprehensive trade validation."""
    print("\n" + "="*50)
    print("Testing Trade Validation")
    print("="*50)
    
    risk_manager = RiskManager()
    
    # Sample trade request
    trade_request = {
        'symbol': 'EURUSD',
        'direction': 'buy',
        'price': 1.1000
    }
    
    # Sample current positions
    current_positions = [
        {
            'symbol': 'GBPUSD',
            'value': 1500.0,
            'pnl': 50.0
        },
        {
            'symbol': 'USDJPY',
            'value': 1000.0,
            'pnl': -30.0
        }
    ]
    
    # Sample account info
    account_info = {
        'balance': 10000.0,
        'equity': 10020.0,
        'margin': 500.0,
        'free_margin': 9520.0
    }
    
    # Sample market data
    market_data = {
        'price': 1.1000,
        'atr': 0.0050,
        'volatility': 0.02,
        'spread': 0.0002
    }
    
    print(f"Trade request: {trade_request}")
    print(f"Current positions: {len(current_positions)}")
    print(f"Account balance: ${account_info['balance']:,.2f}")
    
    # Validate trade
    validation_result = risk_manager.validate_trade(
        trade_request, current_positions, account_info, market_data
    )
    
    print(f"\nValidation result:")
    print(f"  Approved: {validation_result['approved']}")
    print(f"  Reasons: {validation_result['reasons']}")
    print(f"  Warnings: {validation_result['warnings']}")
    
    print(f"\nAdjusted trade:")
    adjusted_trade = validation_result['adjusted_trade']
    for key, value in adjusted_trade.items():
        if isinstance(value, float):
            print(f"  {key}: {value:.4f}")
        else:
            print(f"  {key}: {value}")
    
    print(f"\nRisk metrics:")
    risk_metrics = validation_result['risk_metrics']
    for key, value in risk_metrics.items():
        if isinstance(value, float):
            if 'percentage' in key or 'ratio' in key:
                print(f"  {key}: {value:.2%}")
            else:
                print(f"  {key}: {value:.4f}")
        else:
            print(f"  {key}: {value}")


def test_risk_summary():
    """Test comprehensive risk summary."""
    print("\n" + "="*50)
    print("Testing Risk Summary")
    print("="*50)
    
    risk_manager = RiskManager()
    
    # Sample positions with more details
    positions = [
        {
            'symbol': 'EURUSD',
            'value': 2000.0,
            'risk_amount': 40.0,
            'pnl': 25.0,
            'direction': 'buy'
        },
        {
            'symbol': 'GBPUSD',
            'value': 1500.0,
            'risk_amount': 30.0,
            'pnl': -15.0,
            'direction': 'buy'
        },
        {
            'symbol': 'USDJPY',
            'value': 1000.0,
            'risk_amount': 20.0,
            'pnl': 10.0,
            'direction': 'sell'
        },
        {
            'symbol': 'EURUSD',  # Second EURUSD position
            'value': 800.0,
            'risk_amount': 16.0,
            'pnl': -5.0,
            'direction': 'sell'
        }
    ]
    
    account_info = {
        'balance': 10000.0,
        'equity': 10015.0,
        'margin': 800.0,
        'free_margin': 9215.0
    }
    
    print(f"Portfolio positions: {len(positions)}")
    print(f"Account balance: ${account_info['balance']:,.2f}")
    
    # Get risk summary
    risk_summary = risk_manager.get_risk_summary(positions, account_info)
    
    print(f"\nAccount Summary:")
    account = risk_summary['account']
    for key, value in account.items():
        print(f"  {key}: ${value:,.2f}")
    
    print(f"\nPosition Summary:")
    pos_summary = risk_summary['positions']
    print(f"  Total positions: {pos_summary['total_count']}")
    print(f"  Total exposure: ${pos_summary['total_exposure']:,.2f}")
    print(f"  Exposure ratio: {pos_summary['exposure_ratio']:.1%}")
    print(f"  Total risk: ${pos_summary['total_risk']:,.2f}")
    print(f"  Risk ratio: {pos_summary['risk_ratio']:.1%}")
    print(f"  Current P&L: ${pos_summary['current_pnl']:,.2f}")
    print(f"  P&L ratio: {pos_summary['pnl_ratio']:.1%}")
    
    print(f"\nRisk Limits:")
    limits = risk_summary['limits']
    for key, value in limits.items():
        if isinstance(value, float):
            print(f"  {key}: {value:.1%}")
        else:
            print(f"  {key}: {value}")
    
    print(f"\nSymbol Distribution:")
    for symbol, count in risk_summary['symbol_distribution'].items():
        print(f"  {symbol}: {count} position(s)")
    
    print(f"\nOverall Risk Level: {risk_summary['risk_level']}")


def main():
    """Main test function."""
    print("Risk Management System Test")
    print("="*50)
    
    try:
        # Test main risk manager
        risk_manager = test_risk_manager()
        
        # Test position sizer
        test_position_sizer()
        
        # Test stop loss calculator
        test_stop_loss_calculator()
        
        # Test portfolio risk analyzer
        test_portfolio_risk_analyzer()
        
        # Test trade validation
        test_trade_validation()
        
        # Test risk summary
        test_risk_summary()
        
        print("\n" + "="*50)
        print("Risk Management System Test Completed Successfully!")
        print("="*50)
        
        # Save test summary
        output_dir = Path("data/processed")
        output_dir.mkdir(exist_ok=True)
        
        test_summary = {
            'risk_manager_tested': True,
            'position_sizer_tested': True,
            'stop_loss_calculator_tested': True,
            'portfolio_analyzer_tested': True,
            'trade_validation_tested': True,
            'risk_summary_tested': True,
            'timestamp': datetime.now().isoformat()
        }
        
        with open(output_dir / "risk_management_test_summary.json", 'w') as f:
            json.dump(test_summary, f, indent=2)
        
        print(f"\nTest summary saved to: {output_dir / 'risk_management_test_summary.json'}")
        
    except Exception as e:
        print(f"Error during risk management testing: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
