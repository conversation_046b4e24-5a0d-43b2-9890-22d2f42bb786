# AI Trading Bot - Deployment Guide

## 🚀 Production Deployment Guide

This guide provides step-by-step instructions for deploying the AI Trading Bot in a production environment.

## 📋 Prerequisites

### System Requirements
- **Operating System**: Windows 10/11 (for MT5 integration)
- **Python**: 3.8 or higher
- **RAM**: 8GB minimum, 16GB recommended
- **Storage**: 10GB free space
- **Network**: Stable internet connection

### Required Software
- **MetaTrader 5**: Latest version installed and configured
- **Python Environment**: Virtual environment recommended
- **Database**: SQLite (included) or PostgreSQL for production

## 🛠️ Installation Steps

### 1. Environment Setup

```bash
# Create virtual environment
python -m venv trading_bot_env

# Activate environment (Windows)
trading_bot_env\Scripts\activate

# Activate environment (Linux/Mac)
source trading_bot_env/bin/activate
```

### 2. Install Dependencies

```bash
# Core dependencies
pip install pandas numpy scikit-learn
pip install fastapi uvicorn
pip install MetaTrader5

# Optional dependencies for enhanced features
pip install tensorflow  # For deep learning models
pip install xgboost     # For gradient boosting
pip install psutil      # For system monitoring
pip install pyyaml      # For configuration files
```

### 3. Project Setup

```bash
# Clone or download the project
git clone <repository-url>
cd BOT_ALPHA

# Verify installation
python test_all_systems.py
```

## ⚙️ Configuration

### 1. MetaTrader 5 Configuration

Create `config/mt5_config.json`:
```json
{
  "server": "YourBroker-Live",
  "login": 12345678,
  "password": "your_password",
  "path": "C:\\Program Files\\MetaTrader 5\\terminal64.exe",
  "symbols": ["EURUSD", "GBPUSD", "USDJPY"],
  "timeframes": {
    "M1": 1, "M5": 5, "M15": 15, "M30": 30,
    "H1": 60, "H4": 240, "D1": 1440
  }
}
```

### 2. Risk Management Configuration

Create `config/risk_config.json`:
```json
{
  "position_sizing": {
    "method": "fixed_percentage",
    "risk_per_trade": 0.01,
    "max_position_size": 0.05,
    "min_position_size": 0.001
  },
  "portfolio_limits": {
    "max_daily_loss": 0.02,
    "max_drawdown": 0.10,
    "max_positions": 5,
    "max_exposure_per_symbol": 0.15
  },
  "stop_loss": {
    "method": "atr_based",
    "atr_multiplier": 2.0,
    "max_stop_loss": 0.03
  }
}
```

### 3. Model Configuration

Create `config/model_config.json`:
```json
{
  "models": {
    "linear_regression": {
      "enabled": true,
      "regularization": "ridge",
      "alpha": 1.0
    },
    "random_forest": {
      "enabled": true,
      "n_estimators": 100,
      "max_depth": 10
    },
    "lstm": {
      "enabled": true,
      "units": [50, 50],
      "epochs": 50,
      "batch_size": 32
    }
  },
  "ensemble": {
    "enabled": true,
    "voting": "soft",
    "weights": "auto"
  }
}
```

## 🔄 Deployment Process

### 1. Demo Environment Testing

```bash
# Test with demo account first
python test_mt5_api.py

# Run comprehensive system test
python test_all_systems.py

# Test backtesting
python test_backtesting.py
```

### 2. Model Training

```python
# Train models on historical data
from src.models.model_manager import ModelManager
from src.data.data_loader import DataLoader
from src.features.feature_engineer import FeatureEngineer

# Load and prepare data
loader = DataLoader()
data = loader.load_mt5_data('EURUSD', 'M15', count=10000)

# Engineer features
engineer = FeatureEngineer()
features_df = engineer.create_comprehensive_features(data)

# Train models
manager = ModelManager()
results = manager.train_all_models(features_df)

# Save trained models
manager.save_all_models("models/production")
```

### 3. Backtesting Validation

```python
# Validate strategies
from src.backtesting.backtest_engine import BacktestEngine

engine = BacktestEngine(initial_capital=10000.0)
results = engine.run_backtest(data, your_strategy_function)

# Ensure satisfactory performance before live deployment
assert results['metrics']['sharpe_ratio'] > 1.0
assert results['metrics']['max_drawdown'] > -0.15
```

### 4. Live Trading Deployment

```bash
# Start the API server
python src/api/main.py

# Or using uvicorn for production
uvicorn src.api.main:app --host 0.0.0.0 --port 8000 --workers 1
```

## 📊 Monitoring Setup

### 1. Performance Monitoring

```python
# Set up monitoring
from src.monitoring.dashboard import TradingDashboard
from src.monitoring.experiment_tracker import ExperimentTracker

dashboard = TradingDashboard()
tracker = ExperimentTracker()

# Create live trading experiment
exp_id = tracker.create_experiment(
    "Live Trading - Production",
    "Production deployment",
    "Ensemble",
    "Live_Data"
)
```

### 2. Alert Configuration

Configure alerts in `config/alert_config.json`:
```json
{
  "alert_thresholds": {
    "max_drawdown": 0.08,
    "daily_loss": 0.03,
    "win_rate_min": 0.45,
    "system_cpu": 80,
    "system_memory": 85
  },
  "notification_methods": {
    "email": "<EMAIL>",
    "webhook": "https://your-webhook-url.com"
  }
}
```

## 🛡️ Security Considerations

### 1. Credential Management
- Store MT5 credentials securely
- Use environment variables for sensitive data
- Implement proper access controls

### 2. Network Security
- Use HTTPS for API endpoints
- Implement rate limiting
- Monitor for unusual activity

### 3. Data Protection
- Encrypt sensitive configuration files
- Regular backup of trading data
- Secure database access

## 📈 Performance Optimization

### 1. System Optimization
```python
# Optimize for production
import os
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'  # Reduce TensorFlow logging

# Use efficient data structures
pd.options.mode.chained_assignment = None
```

### 2. Memory Management
- Monitor memory usage
- Implement data cleanup routines
- Use efficient data processing

### 3. Execution Speed
- Optimize feature calculation
- Use vectorized operations
- Implement caching where appropriate

## 🔧 Maintenance Procedures

### 1. Daily Checks
- Verify MT5 connection
- Check system performance
- Review trading results
- Monitor alerts

### 2. Weekly Maintenance
- Update market data
- Retrain models if needed
- Review risk parameters
- Backup trading data

### 3. Monthly Reviews
- Analyze performance metrics
- Update risk management rules
- Review and optimize strategies
- System health assessment

## 🚨 Troubleshooting

### Common Issues

#### MT5 Connection Problems
```python
# Check MT5 status
from src.trading.mt5_connector import MT5Connector

connector = MT5Connector()
if not connector.connect():
    print("MT5 connection failed - check credentials and terminal")
```

#### Model Performance Issues
```python
# Monitor model performance
from src.monitoring.dashboard import TradingDashboard

dashboard = TradingDashboard()
performance = dashboard.generate_performance_report(30)

if performance['performance']['sharpe_ratio'] < 0.5:
    print("Model performance degraded - consider retraining")
```

#### System Resource Issues
```python
# Monitor system resources
from src.monitoring.dashboard import SystemMonitor

metrics = SystemMonitor.get_system_metrics()
if metrics['cpu_usage'] > 80:
    print("High CPU usage detected")
```

## 📞 Support and Monitoring

### 1. Log Files
- Application logs: `logs/trading_bot.log`
- Error logs: `logs/errors.log`
- Performance logs: `logs/performance.log`

### 2. Health Checks
- API health: `GET /health`
- System status: `GET /status`
- MT5 connection: `GET /mt5/account`

### 3. Emergency Procedures
- Stop trading: `POST /trading/stop`
- Close all positions: Emergency script
- System shutdown: Graceful shutdown procedure

## 🎯 Go-Live Checklist

### Pre-Deployment
- [ ] All tests passing
- [ ] Demo trading successful
- [ ] Risk parameters configured
- [ ] Monitoring setup complete
- [ ] Backup procedures in place

### Deployment
- [ ] Production environment configured
- [ ] MT5 connection established
- [ ] Models trained and validated
- [ ] API server running
- [ ] Monitoring active

### Post-Deployment
- [ ] Initial trades executed successfully
- [ ] Performance monitoring active
- [ ] Alerts functioning
- [ ] Daily maintenance scheduled
- [ ] Emergency procedures tested

## 📋 Production Checklist

### System Readiness
- ✅ All core components tested
- ✅ MT5 integration working
- ✅ Risk management active
- ✅ Monitoring configured
- ✅ Performance validated

### Risk Management
- ✅ Position sizing configured
- ✅ Stop-loss mechanisms active
- ✅ Portfolio limits enforced
- ✅ Daily loss limits set
- ✅ Emergency stop procedures ready

### Monitoring
- ✅ Real-time dashboard active
- ✅ Alert system configured
- ✅ Performance tracking enabled
- ✅ System health monitoring
- ✅ Experiment tracking setup

---

**🚀 Your AI Trading Bot is ready for production deployment!**

Remember to start with small position sizes and gradually increase as you gain confidence in the system's performance.
