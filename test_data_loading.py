#!/usr/bin/env python3
"""
Simple test script to verify data loading functionality.
"""

import sys
import pandas as pd
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent))

# Simple logger replacement
class SimpleLogger:
    def info(self, msg): print(f"INFO: {msg}")
    def warning(self, msg): print(f"WARNING: {msg}")
    def error(self, msg): print(f"ERROR: {msg}")

# Replace loguru logger in modules
import src.data.data_loader
import src.data.data_processor
import src.data.data_validator

logger = SimpleLogger()
src.data.data_loader.logger = logger
src.data.data_processor.logger = logger
src.data.data_validator.logger = logger

from src.data.data_loader import DataLoader
from src.data.data_processor import DataProcessor
from src.data.data_validator import DataValidator


def test_data_loading():
    """Test basic data loading functionality."""
    print("Testing data loading functionality...")
    
    try:
        # Initialize data loader
        data_loader = DataLoader()
        
        # Get available symbols
        symbols = data_loader.get_available_symbols()
        print(f"Available symbols: {symbols}")
        
        if not symbols:
            print("No symbols found. Checking raw data directory...")
            import glob
            files = glob.glob("data/raw/*.csv")
            print(f"CSV files found: {[Path(f).name for f in files]}")
            return
        
        # Test loading first symbol
        symbol = symbols[0]
        print(f"\nTesting with symbol: {symbol}")
        
        df = data_loader.load_currency_pair_data(symbol)
        if df is not None:
            print(f"Successfully loaded {len(df)} rows")
            print(f"Columns: {list(df.columns)}")
            print(f"Date range: {df['timestamp'].min()} to {df['timestamp'].max()}")
            print("\nFirst 5 rows:")
            print(df.head())
            
            # Test data validation
            print("\nTesting data validation...")
            validator = DataValidator()
            results = validator.validate_dataframe(df, symbol)
            print(f"Quality score: {results['quality_score']:.2f}")
            print(f"Passed checks: {results['passed_checks']}")
            print(f"Failed checks: {results['failed_checks']}")
            
            # Test data processing
            print("\nTesting data processing...")
            processor = DataProcessor()
            df_clean = processor.clean_data(df)
            print(f"Cleaned data shape: {df_clean.shape}")
            
            df_with_returns = processor.add_returns(df_clean)
            print(f"Data with returns shape: {df_with_returns.shape}")
            print(f"New columns: {[col for col in df_with_returns.columns if col not in df.columns]}")
            
        else:
            print(f"Failed to load data for {symbol}")
            
    except Exception as e:
        print(f"Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_data_loading()
