{"timestamp": "2025-07-31T19:39:38.631180", "tests": {"Data Infrastructure": {"status": "FAILED", "error": "No module named 'yaml'", "duration": 0}, "Feature Engineering": {"status": "FAILED", "error": "No module named 'yaml'", "duration": 0}, "Model Training": {"status": "PASSED", "duration": 0.005977630615234375, "result": {"training_data_shape": [140, 10], "test_data_shape": [30, 10], "linear_regression_r2": -0.06593355545198887, "models_trained": 1}}, "Backtesting": {"status": "FAILED", "error": "No module named 'yaml'", "duration": 0}, "MT5 Integration": {"status": "PASSED", "duration": 0.05850791931152344, "result": {"connection_successful": false, "simulation_mode": true, "note": "MT5 not available, using simulation"}}, "Risk Management": {"status": "PASSED", "duration": 0.003185749053955078, "result": {"position_size": 909.090909090909, "stop_loss": 1.09, "portfolio_check_passed": true, "risk_manager_initialized": true}}, "Monitoring": {"status": "PASSED", "duration": 0.04351449012756348, "result": {"experiment_created": true, "dashboard_initialized": true, "dashboard_data_keys": ["timestamp", "summary", "system_status", "active_alerts", "recent_alerts", "charts", "trading_status"], "metrics_updated": true}}, "API Components": {"status": "PASSED", "duration": 0.010416507720947266, "result": {"api_imported": false, "error": "No module named '<PERSON><PERSON><PERSON>'", "note": "API components may have dependencies not available"}}}, "summary": {"total_tests": 8, "passed_tests": 5, "failed_tests": 3, "success_rate": 62.5}}