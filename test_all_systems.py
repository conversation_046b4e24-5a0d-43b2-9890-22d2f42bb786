#!/usr/bin/env python3
"""
Comprehensive test script for all AI Trading Bot systems.
"""

import pandas as pd
import numpy as np
from pathlib import Path
import sys
import json
import time
from datetime import datetime

print("AI Trading Bot - Comprehensive System Test")
print("="*60)
print(f"Test started at: {datetime.now()}")
print("="*60)

# Test results tracking
test_results = {
    'timestamp': datetime.now().isoformat(),
    'tests': {},
    'summary': {
        'total_tests': 0,
        'passed_tests': 0,
        'failed_tests': 0,
        'success_rate': 0
    }
}

def run_test(test_name, test_function):
    """Run a test and track results."""
    print(f"\n{'='*20} {test_name} {'='*20}")
    
    try:
        start_time = time.time()
        result = test_function()
        end_time = time.time()
        
        test_results['tests'][test_name] = {
            'status': 'PASSED',
            'duration': end_time - start_time,
            'result': result if isinstance(result, (dict, list, str, int, float)) else 'Success'
        }
        
        test_results['summary']['passed_tests'] += 1
        print(f"✅ {test_name} PASSED ({end_time - start_time:.2f}s)")
        
    except Exception as e:
        test_results['tests'][test_name] = {
            'status': 'FAILED',
            'error': str(e),
            'duration': 0
        }
        
        test_results['summary']['failed_tests'] += 1
        print(f"❌ {test_name} FAILED: {str(e)}")
    
    test_results['summary']['total_tests'] += 1


def test_data_infrastructure():
    """Test data loading and processing."""
    sys.path.append(str(Path(__file__).parent / "src" / "data"))
    
    from data_loader import DataLoader
    from data_processor import DataProcessor
    
    # Test data loader
    loader = DataLoader()
    sample_data = loader.create_sample_data('EURUSD', 1000)
    
    # Test data processor
    processor = DataProcessor()
    processed_data = processor.clean_data(sample_data)
    validated_data = processor.validate_data(processed_data)
    
    return {
        'sample_data_shape': sample_data.shape,
        'processed_data_shape': processed_data.shape,
        'validation_passed': validated_data['is_valid']
    }


def test_feature_engineering():
    """Test feature engineering pipeline."""
    sys.path.append(str(Path(__file__).parent / "src" / "features"))
    sys.path.append(str(Path(__file__).parent / "src" / "data"))
    
    from feature_engineer import FeatureEngineer
    from data_loader import DataLoader
    
    # Create sample data
    loader = DataLoader()
    data = loader.create_sample_data('EURUSD', 500)
    
    # Engineer features
    engineer = FeatureEngineer()
    features_df = engineer.create_comprehensive_features(data)
    
    return {
        'original_columns': len(data.columns),
        'feature_columns': len(features_df.columns),
        'features_created': len(features_df.columns) - len(data.columns),
        'data_shape': features_df.shape
    }


def test_model_training():
    """Test model training and evaluation."""
    sys.path.append(str(Path(__file__).parent / "src" / "models"))
    
    from supervised_models import SupervisedModels
    
    # Create sample data for training
    np.random.seed(42)
    n_samples = 200
    n_features = 10
    
    X = np.random.randn(n_samples, n_features)
    y = np.random.randn(n_samples)
    
    # Test supervised models
    models = SupervisedModels()
    X_train, X_val, X_test, y_train, y_val, y_test = models.split_data(X, y)
    
    # Train linear regression
    lr_result = models.train_linear_regression(X_train, y_train)
    lr_eval = models.evaluate_model('linear_regression', X_test, y_test)
    
    return {
        'training_data_shape': X_train.shape,
        'test_data_shape': X_test.shape,
        'linear_regression_r2': lr_eval.get('r2', 0),
        'models_trained': len(models.models)
    }


def test_backtesting():
    """Test backtesting framework."""
    sys.path.append(str(Path(__file__).parent / "src" / "backtesting"))
    sys.path.append(str(Path(__file__).parent / "src" / "data"))
    
    from backtest_engine import BacktestEngine, simple_moving_average_strategy
    from data_loader import DataLoader
    
    # Create sample data
    loader = DataLoader()
    data = loader.create_sample_data('EURUSD', 200)
    
    # Run backtest
    engine = BacktestEngine(initial_capital=10000.0)
    results = engine.run_backtest(data, simple_moving_average_strategy)
    
    return {
        'initial_capital': engine.initial_capital,
        'final_value': results['final_value'],
        'total_return': results['total_return'],
        'total_trades': len(results['trades']),
        'sharpe_ratio': results['metrics'].get('sharpe_ratio', 0)
    }


def test_mt5_integration():
    """Test MT5 connector and API."""
    sys.path.append(str(Path(__file__).parent / "src" / "trading"))
    
    from mt5_connector import MT5Connector
    
    # Test MT5 connector
    connector = MT5Connector()
    connected = connector.connect()
    
    if connected:
        account_info = connector.get_account_info()
        prices = connector.get_current_prices(['EURUSD'])
        historical_data = connector.get_historical_data('EURUSD', 'M15', 50)
        
        connector.disconnect()
        
        return {
            'connection_successful': True,
            'account_balance': account_info.get('balance', 0),
            'price_data_available': 'EURUSD' in prices,
            'historical_data_shape': historical_data.shape
        }
    else:
        return {
            'connection_successful': False,
            'simulation_mode': True,
            'note': 'MT5 not available, using simulation'
        }


def test_risk_management():
    """Test risk management system."""
    sys.path.append(str(Path(__file__).parent / "src" / "risk_management"))
    
    from risk_manager import RiskManager, PositionSizer
    
    # Test risk manager
    risk_manager = RiskManager()
    
    # Test position sizing
    position_size = risk_manager.calculate_position_size(
        'EURUSD', 1.1000, 1.0950, 10000.0
    )
    
    # Test stop loss calculation
    stop_loss = risk_manager.calculate_stop_loss(
        'EURUSD', 1.1000, 'buy', atr=0.0050
    )
    
    # Test portfolio limits
    new_position = {'symbol': 'EURUSD', 'value': 1000.0}
    current_positions = []
    portfolio_check = risk_manager.check_portfolio_limits(
        new_position, current_positions, 10000.0
    )
    
    return {
        'position_size': position_size,
        'stop_loss': stop_loss,
        'portfolio_check_passed': portfolio_check['allowed'],
        'risk_manager_initialized': True
    }


def test_monitoring():
    """Test monitoring and experiment tracking."""
    sys.path.append(str(Path(__file__).parent / "src" / "monitoring"))
    
    from experiment_tracker import ExperimentTracker
    from dashboard import TradingDashboard
    
    # Test experiment tracker
    tracker = ExperimentTracker("data/test_experiments.db")
    exp_id = tracker.create_experiment("Test Experiment", "System test")
    
    # Test dashboard
    dashboard = TradingDashboard()
    
    # Update with sample metrics
    sample_metrics = {
        'portfolio_value': 10500.0,
        'daily_pnl': 50.0,
        'win_rate': 0.6,
        'active_positions': 3
    }
    
    dashboard.update_trading_metrics(sample_metrics)
    dashboard_data = dashboard.get_dashboard_data()
    
    return {
        'experiment_created': exp_id is not None,
        'dashboard_initialized': True,
        'dashboard_data_keys': list(dashboard_data.keys()),
        'metrics_updated': True
    }


def test_api_components():
    """Test API components (without starting server)."""
    sys.path.append(str(Path(__file__).parent / "src" / "api"))
    
    try:
        # Import API components
        from main import TradingBotAPI
        
        # Test API initialization
        api = TradingBotAPI()
        initialized = api.initialize()
        
        return {
            'api_imported': True,
            'api_initialized': initialized,
            'components_available': True
        }
    except Exception as e:
        return {
            'api_imported': False,
            'error': str(e),
            'note': 'API components may have dependencies not available'
        }


def main():
    """Run all system tests."""
    
    # Run all tests
    run_test("Data Infrastructure", test_data_infrastructure)
    run_test("Feature Engineering", test_feature_engineering)
    run_test("Model Training", test_model_training)
    run_test("Backtesting", test_backtesting)
    run_test("MT5 Integration", test_mt5_integration)
    run_test("Risk Management", test_risk_management)
    run_test("Monitoring", test_monitoring)
    run_test("API Components", test_api_components)
    
    # Calculate success rate
    total = test_results['summary']['total_tests']
    passed = test_results['summary']['passed_tests']
    test_results['summary']['success_rate'] = (passed / total) * 100 if total > 0 else 0
    
    # Print summary
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    
    summary = test_results['summary']
    print(f"Total Tests: {summary['total_tests']}")
    print(f"Passed: {summary['passed_tests']}")
    print(f"Failed: {summary['failed_tests']}")
    print(f"Success Rate: {summary['success_rate']:.1f}%")
    
    print("\nDetailed Results:")
    for test_name, result in test_results['tests'].items():
        status_icon = "✅" if result['status'] == 'PASSED' else "❌"
        duration = result.get('duration', 0)
        print(f"{status_icon} {test_name}: {result['status']} ({duration:.2f}s)")
        
        if result['status'] == 'FAILED':
            print(f"   Error: {result.get('error', 'Unknown error')}")
    
    # Save results
    output_dir = Path("data/processed")
    output_dir.mkdir(exist_ok=True)
    
    with open(output_dir / "comprehensive_test_results.json", 'w') as f:
        json.dump(test_results, f, indent=2, default=str)
    
    print(f"\nTest results saved to: {output_dir / 'comprehensive_test_results.json'}")
    
    # Overall status
    if summary['success_rate'] >= 80:
        print("\n🎉 SYSTEM STATUS: READY FOR DEPLOYMENT")
        print("All critical components are working correctly!")
    elif summary['success_rate'] >= 60:
        print("\n⚠️  SYSTEM STATUS: MOSTLY FUNCTIONAL")
        print("Most components working, some issues to address.")
    else:
        print("\n🚨 SYSTEM STATUS: NEEDS ATTENTION")
        print("Multiple components have issues that need to be resolved.")
    
    print(f"\nTest completed at: {datetime.now()}")
    print("="*60)
    
    return test_results


if __name__ == "__main__":
    results = main()
