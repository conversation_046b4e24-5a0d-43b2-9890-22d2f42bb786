[2025-07-31 19:47:59] INFO: Starting AI Trading Bot deployment...
[2025-07-31 19:47:59] INFO: ==================================================
[2025-07-31 19:47:59] INFO: Executing: Prerequisites Check
[2025-07-31 19:47:59] INFO: Checking deployment prerequisites...
[2025-07-31 19:47:59] INFO: Python version: [PASS]
[2025-07-31 19:47:59] INFO: Config file exists: [PASS]
[2025-07-31 19:47:59] INFO: Data directory: [PASS]
[2025-07-31 19:47:59] INFO: Source code: [PASS]
[2025-07-31 19:47:59] INFO: [SUCCESS] Prerequisites Check completed successfully
[2025-07-31 19:47:59] INFO: Executing: Backup Data
[2025-07-31 19:47:59] INFO: Backing up existing data...
[2025-07-31 19:47:59] INFO: Backed up: data
[2025-07-31 19:47:59] INFO: Backed up: models
[2025-07-31 19:47:59] INFO: Backed up: logs
[2025-07-31 19:47:59] INFO: Backed up: config
[2025-07-31 19:47:59] INFO: Backup completed: D:\BOT ALPHA\backup\20250731_194759
[2025-07-31 19:47:59] INFO: [SUCCESS] Backup Data completed successfully
[2025-07-31 19:47:59] INFO: Executing: Setup Directories
[2025-07-31 19:47:59] INFO: Setting up directories...
[2025-07-31 19:47:59] INFO: Created directory: data/raw
[2025-07-31 19:47:59] INFO: Created directory: data/processed
[2025-07-31 19:47:59] INFO: Created directory: data/backup
[2025-07-31 19:47:59] INFO: Created directory: logs
[2025-07-31 19:47:59] INFO: Created directory: models/production
[2025-07-31 19:47:59] INFO: Created directory: artifacts
[2025-07-31 19:47:59] INFO: Created directory: config
[2025-07-31 19:47:59] INFO: [SUCCESS] Setup Directories completed successfully
[2025-07-31 19:47:59] INFO: Executing: Install Dependencies
[2025-07-31 19:47:59] INFO: Installing dependencies...
[2025-07-31 19:48:07] ERROR: Failed to install dependencies: ERROR: Could not find a version that satisfies the requirement tensorflow>=2.13.0 (from versions: 2.20.0rc0)

[notice] A new release of pip is available: 25.0.1 -> 25.2
[notice] To update, run: python.exe -m pip install --upgrade pip
ERROR: No matching distribution found for tensorflow>=2.13.0

[2025-07-31 19:48:07] ERROR: [FAILED] Install Dependencies failed
[2025-07-31 19:48:07] INFO: Executing: Validate Configuration
[2025-07-31 19:48:07] INFO: Validating configuration...
[2025-07-31 19:48:07] INFO: Configuration validation passed
[2025-07-31 19:48:07] INFO: [SUCCESS] Validate Configuration completed successfully
[2025-07-31 19:48:07] INFO: Executing: Test Components
[2025-07-31 19:48:07] INFO: [SUCCESS] Test Components completed successfully
[2025-07-31 19:48:07] INFO: Executing: Setup Monitoring
[2025-07-31 19:48:07] INFO: Setting up monitoring...
[2025-07-31 19:48:07] INFO: Monitoring setup completed
[2025-07-31 19:48:07] INFO: [SUCCESS] Setup Monitoring completed successfully
[2025-07-31 19:48:07] INFO: Executing: Create Startup Script
[2025-07-31 19:48:07] INFO: Creating startup script...
[2025-07-31 19:48:07] INFO: Startup script created
[2025-07-31 19:48:07] INFO: [SUCCESS] Create Startup Script completed successfully
[2025-07-31 19:48:07] INFO: Executing: Create Service Files
[2025-07-31 19:48:07] INFO: Creating service files...
[2025-07-31 19:48:07] INFO: Service files created
[2025-07-31 19:48:07] INFO: [SUCCESS] Create Service Files completed successfully