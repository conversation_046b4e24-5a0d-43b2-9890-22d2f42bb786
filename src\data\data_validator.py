"""
Data validation module for ensuring data quality and consistency.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from loguru import logger
import yaml
from datetime import datetime, timedelta


class DataValidator:
    """
    Validates data quality and consistency for trading data.
    """
    
    def __init__(self, config_path: str = "configs/data_config.yaml"):
        """
        Initialize DataValidator with configuration.
        
        Args:
            config_path: Path to data configuration file
        """
        self.config = self._load_config(config_path)
        self.validation_results = {}
        
    def _load_config(self, config_path: str) -> Dict:
        """Load configuration from YAML file."""
        try:
            with open(config_path, 'r') as file:
                return yaml.safe_load(file)
        except FileNotFoundError:
            logger.warning(f"Config file {config_path} not found. Using default settings.")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict:
        """Return default configuration."""
        return {
            'validation': {
                'checks': [
                    'no_missing_values',
                    'no_duplicates', 
                    'price_consistency',
                    'chronological_order'
                ],
                'quality_metrics': {
                    'min_data_points': 1000,
                    'max_missing_ratio': 0.05
                }
            }
        }
    
    def validate_dataframe(self, df: pd.DataFrame, symbol: str = "Unknown") -> Dict[str, Any]:
        """
        Perform comprehensive validation on a DataFrame.
        
        Args:
            df: DataFrame to validate
            symbol: Symbol name for reporting
            
        Returns:
            Dictionary with validation results
        """
        logger.info(f"Starting validation for {symbol}")
        
        results = {
            'symbol': symbol,
            'timestamp': datetime.now(),
            'total_rows': len(df),
            'total_columns': len(df.columns),
            'passed_checks': [],
            'failed_checks': [],
            'warnings': [],
            'quality_score': 0.0,
            'details': {}
        }
        
        # Get validation checks from config
        checks = self.config.get('validation', {}).get('checks', [])
        
        # Run each validation check
        for check in checks:
            try:
                if hasattr(self, f'_check_{check}'):
                    check_result = getattr(self, f'_check_{check}')(df)
                    if check_result['passed']:
                        results['passed_checks'].append(check)
                    else:
                        results['failed_checks'].append(check)
                    
                    results['details'][check] = check_result
                else:
                    logger.warning(f"Unknown validation check: {check}")
                    
            except Exception as e:
                logger.error(f"Error in validation check {check}: {str(e)}")
                results['failed_checks'].append(check)
                results['details'][check] = {
                    'passed': False,
                    'error': str(e)
                }
        
        # Calculate quality score
        total_checks = len(checks)
        passed_checks = len(results['passed_checks'])
        results['quality_score'] = passed_checks / total_checks if total_checks > 0 else 0.0
        
        # Add quality metrics
        results['quality_metrics'] = self._calculate_quality_metrics(df)
        
        # Store results
        self.validation_results[symbol] = results
        
        logger.info(f"Validation completed for {symbol}. Quality score: {results['quality_score']:.2f}")
        
        return results
    
    def _check_no_missing_values(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Check for missing values."""
        missing_counts = df.isnull().sum()
        total_missing = missing_counts.sum()
        missing_ratio = total_missing / (len(df) * len(df.columns))
        
        max_missing_ratio = self.config.get('validation', {}).get('quality_metrics', {}).get('max_missing_ratio', 0.05)
        
        return {
            'passed': missing_ratio <= max_missing_ratio,
            'total_missing': int(total_missing),
            'missing_ratio': float(missing_ratio),
            'missing_by_column': missing_counts.to_dict(),
            'threshold': max_missing_ratio
        }
    
    def _check_no_duplicates(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Check for duplicate rows."""
        duplicate_count = df.duplicated().sum()
        duplicate_ratio = duplicate_count / len(df)
        
        return {
            'passed': duplicate_count == 0,
            'duplicate_count': int(duplicate_count),
            'duplicate_ratio': float(duplicate_ratio)
        }
    
    def _check_price_consistency(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Check price consistency (high >= low, etc.)."""
        issues = {}
        
        if all(col in df.columns for col in ['open', 'high', 'low', 'close']):
            # High should be >= Low
            high_low_issues = (df['high'] < df['low']).sum()
            issues['high_less_than_low'] = int(high_low_issues)
            
            # High should be >= Open and Close
            high_open_issues = (df['high'] < df['open']).sum()
            high_close_issues = (df['high'] < df['close']).sum()
            issues['high_less_than_open'] = int(high_open_issues)
            issues['high_less_than_close'] = int(high_close_issues)
            
            # Low should be <= Open and Close
            low_open_issues = (df['low'] > df['open']).sum()
            low_close_issues = (df['low'] > df['close']).sum()
            issues['low_greater_than_open'] = int(low_open_issues)
            issues['low_greater_than_close'] = int(low_close_issues)
            
            # Check for negative prices
            negative_prices = (
                (df['open'] <= 0) | (df['high'] <= 0) | 
                (df['low'] <= 0) | (df['close'] <= 0)
            ).sum()
            issues['negative_prices'] = int(negative_prices)
            
            total_issues = sum(issues.values())
            
            return {
                'passed': total_issues == 0,
                'total_issues': total_issues,
                'issues': issues
            }
        else:
            return {
                'passed': False,
                'error': 'Required price columns (open, high, low, close) not found'
            }
    
    def _check_chronological_order(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Check if timestamps are in chronological order."""
        if 'timestamp' not in df.columns:
            return {
                'passed': False,
                'error': 'Timestamp column not found'
            }
        
        # Check if timestamps are sorted
        is_sorted = df['timestamp'].is_monotonic_increasing
        
        # Count out-of-order timestamps
        out_of_order = 0
        if not is_sorted:
            out_of_order = (df['timestamp'].diff() < pd.Timedelta(0)).sum()
        
        return {
            'passed': is_sorted,
            'is_sorted': is_sorted,
            'out_of_order_count': int(out_of_order)
        }
    
    def _calculate_quality_metrics(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Calculate additional quality metrics."""
        metrics = {}
        
        # Basic metrics
        metrics['row_count'] = len(df)
        metrics['column_count'] = len(df.columns)
        
        # Data completeness
        total_cells = len(df) * len(df.columns)
        missing_cells = df.isnull().sum().sum()
        metrics['completeness_ratio'] = (total_cells - missing_cells) / total_cells
        
        # Time series metrics
        if 'timestamp' in df.columns:
            time_span = df['timestamp'].max() - df['timestamp'].min()
            metrics['time_span_days'] = time_span.days
            
            # Check for gaps in data
            if len(df) > 1:
                time_diffs = df['timestamp'].diff().dropna()
                median_interval = time_diffs.median()
                large_gaps = (time_diffs > median_interval * 2).sum()
                metrics['large_time_gaps'] = int(large_gaps)
                metrics['median_interval_minutes'] = median_interval.total_seconds() / 60
        
        # Price metrics (if available)
        price_cols = ['open', 'high', 'low', 'close']
        available_price_cols = [col for col in price_cols if col in df.columns]
        
        if available_price_cols:
            for col in available_price_cols:
                metrics[f'{col}_min'] = float(df[col].min())
                metrics[f'{col}_max'] = float(df[col].max())
                metrics[f'{col}_mean'] = float(df[col].mean())
                metrics[f'{col}_std'] = float(df[col].std())
        
        # Volume metrics (if available)
        if 'volume' in df.columns:
            metrics['volume_min'] = float(df['volume'].min())
            metrics['volume_max'] = float(df['volume'].max())
            metrics['volume_mean'] = float(df['volume'].mean())
            metrics['zero_volume_count'] = int((df['volume'] == 0).sum())
        
        return metrics
    
    def generate_validation_report(self, symbol: str = None) -> str:
        """
        Generate a human-readable validation report.
        
        Args:
            symbol: Symbol to generate report for (None for all)
            
        Returns:
            Formatted validation report
        """
        if symbol and symbol in self.validation_results:
            results = [self.validation_results[symbol]]
        else:
            results = list(self.validation_results.values())
        
        if not results:
            return "No validation results available."
        
        report = "Data Validation Report\n"
        report += "=" * 50 + "\n\n"
        
        for result in results:
            report += f"Symbol: {result['symbol']}\n"
            report += f"Timestamp: {result['timestamp']}\n"
            report += f"Quality Score: {result['quality_score']:.2f}\n"
            report += f"Total Rows: {result['total_rows']:,}\n"
            report += f"Total Columns: {result['total_columns']}\n\n"
            
            # Passed checks
            if result['passed_checks']:
                report += "✅ Passed Checks:\n"
                for check in result['passed_checks']:
                    report += f"  - {check}\n"
                report += "\n"
            
            # Failed checks
            if result['failed_checks']:
                report += "❌ Failed Checks:\n"
                for check in result['failed_checks']:
                    details = result['details'].get(check, {})
                    report += f"  - {check}"
                    if 'error' in details:
                        report += f" (Error: {details['error']})"
                    report += "\n"
                report += "\n"
            
            # Quality metrics summary
            metrics = result.get('quality_metrics', {})
            if metrics:
                report += "📊 Quality Metrics:\n"
                report += f"  - Completeness: {metrics.get('completeness_ratio', 0):.2%}\n"
                if 'time_span_days' in metrics:
                    report += f"  - Time Span: {metrics['time_span_days']} days\n"
                if 'large_time_gaps' in metrics:
                    report += f"  - Large Time Gaps: {metrics['large_time_gaps']}\n"
                report += "\n"
            
            report += "-" * 50 + "\n\n"
        
        return report
    
    def get_validation_summary(self) -> Dict[str, Any]:
        """Get summary of all validation results."""
        if not self.validation_results:
            return {}
        
        summary = {
            'total_symbols': len(self.validation_results),
            'average_quality_score': np.mean([r['quality_score'] for r in self.validation_results.values()]),
            'symbols_passed': sum(1 for r in self.validation_results.values() if r['quality_score'] >= 0.8),
            'symbols_failed': sum(1 for r in self.validation_results.values() if r['quality_score'] < 0.5),
            'common_issues': {}
        }
        
        # Count common issues
        all_failed_checks = []
        for result in self.validation_results.values():
            all_failed_checks.extend(result['failed_checks'])
        
        from collections import Counter
        issue_counts = Counter(all_failed_checks)
        summary['common_issues'] = dict(issue_counts.most_common(5))
        
        return summary
